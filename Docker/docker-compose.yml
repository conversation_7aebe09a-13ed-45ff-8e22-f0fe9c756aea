version: "3.8"
services:
  postgres:
    image: postgres:16
    restart: unless-stopped
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: flexi
    ports:
      - "5454:5432"
    volumes:
      - pgdata:/var/lib/postgresql/data
  keycloak:
    image: quay.io/keycloak/keycloak:24.0.2
    restart: unless-stopped
    environment:
      KC_DB: postgres
      KC_DB_URL: *************************************
      KC_DB_USERNAME: postgres
      KC_DB_PASSWORD: postgres
      KC_HOSTNAME: localhost
      KC_HTTP_PORT: 8888
      KEYCLOAK_ADMIN: admin
      <PERSON>_ADMIN_PASSWORD: admin
      KEYCLOAK_REALM: flexin
      KEYCLOAK_USER_CLIENT_ID: normal-user
      KEYCLOAK_USER_CLIENT_SECRET: pRYqWw5GoSIBVvjUKbnwLXEhv4ox8c<PERSON>H
      KEYCLOAK_ADMIN_CLIENT_ID: admin-user
      KEYCLOAK_ADMIN_CLIENT_SECRET: owpIyKkq1jq8kJphfexEBrxeysUGzpDv
      KEYCLOAK_TOKEN_URI: http://localhost:8888/realms/flexin/protocol/openid-connect/token
      KEYCLOAK_DEFAULT_ROLE_NAME: default-roles-flexin
    ports:
      - "8888:8888"
    depends_on:
      - postgres
    command: start-dev
volumes:
  pgdata:
