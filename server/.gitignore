# Compiled class file
*.class

# Log file
*.log

# BlueJ files
*.ctxt

# Mobile Tools for Java (J2ME)
.mtj.tmp/

# Package Files #
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# virtual machine crash logs, see http://www.java.com/en/download/help/error_hotspot.xml
hs_err_pid*
replay_pid*

.gradle/8.7/checksums/checksums.lock
.gradle/8.7/checksums/md5-checksums.bin
.gradle/8.7/checksums/sha1-checksums.bin
.gradle/8.7/executionHistory/executionHistory.bin
.gradle/8.7/executionHistory/executionHistory.lock
.gradle/8.7/fileHashes/fileHashes.bin
.gradle/8.7/fileHashes/fileHashes.lock
.gradle/8.7/fileHashes/resourceHashesCache.bin
.gradle/buildOutputCleanup/buildOutputCleanup.lock
.gradle/buildOutputCleanup/outputFiles.bin
.gradle/file-system.probe
.gradle/workspace-id.txt
.gradle/workspace-id.txt.lock
build/resources/main/application-local.yml
build/resources/main/db/changelog/changelog-master.yaml
build/resources/main/db/changelog/changes/dummy-data.sql
build/tmp/compileJava/previous-compilation-data.bin

# Ignore build directories
/build/
/out/

# Ignore Gradle files
.gradle/

# Ignore IntelliJ IDEA project files
.idea/
*.iml
/.gradle/

/bin/
bin*
/bin/*
bin/*
bin/
local-storage/
