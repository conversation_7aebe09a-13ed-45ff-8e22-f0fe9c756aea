{"info": {"name": "Flexin API Collection", "description": "Complete API collection for Flexin project", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "local.admin", "value": "localhost:8080/v1/admin", "type": "string"}, {"key": "local.mobile", "value": "localhost:8080/v1/mobile", "type": "string"}, {"key": "token", "value": "", "type": "string"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Pre-request script for authentication", "const needsAuth = pm.request.url.path.includes('admin') || pm.request.auth;", "", "if (needsAuth && !pm.environment.get('token')) {", "    const loginUrl = pm.request.url.host + ':' + pm.request.url.port + '/v1/admin/auth/login';", "    const loginRequest = {", "        url: loginUrl,", "        method: 'POST',", "        header: {", "            'Content-Type': 'application/json',", "        },", "        body: {", "            mode: 'raw',", "            raw: JSON.stringify({", "                email: '<EMAIL>',", "                password: 'Admin@123'", "            })", "        }", "    };", "", "    pm.sendRequest(loginRequest, function (err, response) {", "        if (err) {", "            console.error('<PERSON><PERSON> failed:', err);", "        } else {", "            const responseJson = response.json();", "            if (responseJson.data && responseJson.data.accessToken) {", "                pm.environment.set('token', responseJson.data.accessToken);", "                pm.request.headers.add({", "                    key: 'Authorization',", "                    value: 'Bearer ' + responseJson.data.accessToken", "                });", "            }", "        }", "    });", "}"]}}], "item": [{"name": "Admin APIs", "item": [{"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Admin@123\",\n  \"remember\": true\n}"}, "url": {"raw": "{{local.admin}}/auth/login", "host": ["{{local.admin}}"], "path": ["auth", "login"]}}}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [], "url": {"raw": "{{local.admin}}/auth/logout", "host": ["{{local.admin}}"], "path": ["auth", "logout"]}}}, {"name": "Refresh <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"your_refresh_token_here\"\n}"}, "url": {"raw": "{{local.admin}}/auth/refresh-token", "host": ["{{local.admin}}"], "path": ["auth", "refresh-token"]}}}]}, {"name": "Users Management", "item": [{"name": "Get All Users", "request": {"method": "GET", "header": [], "url": {"raw": "{{local.admin}}/users", "host": ["{{local.admin}}"], "path": ["users"]}}}, {"name": "Search Users", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"page\": 0,\n  \"size\": 10,\n  \"keyword\": \"\",\n  \"role\": \"\"\n}"}, "url": {"raw": "{{local.admin}}/users/search", "host": ["{{local.admin}}"], "path": ["users", "search"]}}}, {"name": "Get User by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{local.admin}}/users/1", "host": ["{{local.admin}}"], "path": ["users", "1"]}}}, {"name": "Create User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"phoneNumber\": \"0123456789\",\n  \"role\": \"jobSeeker\",\n  \"dateOfBirth\": \"1995-01-15\",\n  \"gender\": \"male\",\n  \"password\": \"password123\",\n  \"address\": {\n    \"provinceCode\": 1,\n    \"districtCode\": 1,\n    \"wardCode\": 1,\n    \"detail\": \"123 Main Street\"\n  }\n}"}, "url": {"raw": "{{local.admin}}/users", "host": ["{{local.admin}}"], "path": ["users"]}}}, {"name": "Update User", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON><PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"phoneNumber\": \"0987829297\",\n  \"role\": \"employer\",\n  \"dateOfBirth\": \"1997-08-11\",\n  \"gender\": \"male\",\n  \"roleIds\": [3],\n  \"address\": {\n    \"provinceCode\": 1,\n    \"districtCode\": 8,\n    \"wardCode\": 301,\n    \"detailAddress\": \"tecco\"\n  },\n  \"isActive\": true\n}"}, "url": {"raw": "{{local.admin}}/users/15", "host": ["{{local.admin}}"], "path": ["users", "15"]}}}, {"name": "Delete User", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{local.admin}}/users/1", "host": ["{{local.admin}}"], "path": ["users", "1"]}}}]}, {"name": "Resumes Management", "item": [{"name": "Get All Resumes", "request": {"method": "GET", "header": [], "url": {"raw": "{{local.admin}}/resumes", "host": ["{{local.admin}}"], "path": ["resumes"]}}}, {"name": "Search Resumes", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"page\": 0,\n  \"size\": 10,\n  \"keyword\": \"\"\n}"}, "url": {"raw": "{{local.admin}}/resumes/search", "host": ["{{local.admin}}"], "path": ["resumes", "search"]}}}, {"name": "Get Resume by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{local.admin}}/resumes/1", "host": ["{{local.admin}}"], "path": ["resumes", "1"]}}}, {"name": "Create Resume", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Software Engineer Resume\",\n  \"description\": \"Experienced software engineer with 5+ years in web development\",\n  \"userId\": 1,\n  \"isActive\": true,\n  \"skills\": [\"JavaScript\", \"React\", \"Node.js\", \"Java\", \"Spring Boot\"],\n  \"workExperiences\": [\n    {\n      \"company\": \"Tech Corp\",\n      \"position\": \"Senior Software Engineer\",\n      \"startDate\": \"2020-01-15\",\n      \"endDate\": \"2023-12-31\",\n      \"description\": \"Developed web applications using React and Node.js\"\n    }\n  ],\n  \"educations\": [\n    {\n      \"institution\": \"University of Technology\",\n      \"degree\": \"Bachelor of Computer Science\",\n      \"startDate\": \"2016-09-01\",\n      \"endDate\": \"2020-06-30\",\n      \"description\": \"Computer Science with focus on software engineering\"\n    }\n  ],\n  \"contactInfo\": {\n    \"phoneNumber\": \"0123456789\",\n    \"email\": \"<EMAIL>\",\n    \"address\": \"123 Main Street\",\n    \"city\": \"Ho Chi Minh City\",\n    \"country\": \"Vietnam\",\n    \"linkedInUrl\": \"https://linkedin.com/in/johndoe\"\n  }\n}"}, "url": {"raw": "{{local.admin}}/resumes", "host": ["{{local.admin}}"], "path": ["resumes"]}}}, {"name": "Update Resume", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Senior Software Engineer Resume\",\n  \"description\": \"Senior software engineer with 7+ years in full-stack development\",\n  \"userId\": 1,\n  \"isActive\": true,\n  \"skills\": [\"JavaScript\", \"React\", \"Node.js\", \"Java\", \"Spring Boot\", \"Docker\", \"AWS\"],\n  \"workExperiences\": [\n    {\n      \"company\": \"Tech Corp\",\n      \"position\": \"Senior Software Engineer\",\n      \"startDate\": \"2020-01-15\",\n      \"description\": \"Lead full-stack development projects using React and Spring Boot\"\n    }\n  ],\n  \"educations\": [\n    {\n      \"institution\": \"University of Technology\",\n      \"degree\": \"Bachelor of Computer Science\",\n      \"startDate\": \"2016-09-01\",\n      \"endDate\": \"2020-06-30\",\n      \"description\": \"Computer Science with focus on software engineering\"\n    }\n  ],\n  \"contactInfo\": {\n    \"phoneNumber\": \"0123456789\",\n    \"email\": \"<EMAIL>\",\n    \"address\": \"456 New Street\",\n    \"city\": \"Ho Chi Minh City\",\n    \"country\": \"Vietnam\",\n    \"linkedInUrl\": \"https://linkedin.com/in/johndoe\",\n    \"portfolioUrl\": \"https://johndoe.dev\"\n  }\n}"}, "url": {"raw": "{{local.admin}}/resumes/1", "host": ["{{local.admin}}"], "path": ["resumes", "1"]}}}, {"name": "Delete Resume", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{local.admin}}/resumes/1", "host": ["{{local.admin}}"], "path": ["resumes", "1"]}}}]}, {"name": "Posts Management", "item": [{"name": "Search Posts", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"page\": 0,\n  \"size\": 10,\n  \"keyword\": \"\"\n}"}, "url": {"raw": "{{local.admin}}/posts/search", "host": ["{{local.admin}}"], "path": ["posts", "search"]}}}, {"name": "Get Post by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{local.admin}}/posts/1", "host": ["{{local.admin}}"], "path": ["posts", "1"]}}}, {"name": "Create Post", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Part-time Frontend Developer\",\n  \"description\": \"Looking for a skilled part-time frontend developer to join our team\",\n  \"location\": \"Ho Chi Minh City\",\n  \"branchId\": 1,\n  \"salary\": {\n    \"min\": 400000,\n    \"max\": 600000,\n    \"currency\": \"VND\",\n    \"period\": \"hour\"\n  },\n  \"status\": \"ACTIVE\",\n  \"isFeatureJob\": false,\n  \"urgentHiring\": false,\n  \"workingInformation\": [\n    {\n      \"dayOfWeek\": \"MONDAY\",\n      \"startTime\": \"09:00\",\n      \"endTime\": \"17:00\"\n    },\n    {\n      \"dayOfWeek\": \"WEDNESDAY\",\n      \"startTime\": \"09:00\",\n      \"endTime\": \"17:00\"\n    }\n  ],\n  \"workType\": \"PART_TIME\",\n  \"jobType\": \"REMOTE\",\n  \"experience\": {\n    \"minYears\": 2,\n    \"maxYears\": 5,\n    \"level\": \"INTERMEDIATE\"\n  },\n  \"positions\": \"Frontend Developer\",\n  \"skills\": [\"React\", \"JavaScript\", \"CSS\", \"HTML\"],\n  \"benefits\": [\"Flexible working hours\", \"Remote work\", \"Health insurance\"],\n  \"experienceLevel\": \"INTERMEDIATE\"\n}"}, "url": {"raw": "{{local.admin}}/posts", "host": ["{{local.admin}}"], "path": ["posts"]}}}, {"name": "Update Post", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Senior Part-time Developer\",\n  \"description\": \"Looking for senior part-time developer\",\n  \"hourlyRate\": 70000\n}"}, "url": {"raw": "{{local.admin}}/posts/1", "host": ["{{local.admin}}"], "path": ["posts", "1"]}}}, {"name": "Delete Post", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{local.admin}}/posts/1", "host": ["{{local.admin}}"], "path": ["posts", "1"]}}}]}, {"name": "Companies Management", "item": [{"name": "Search Companies", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"page\": 0,\n  \"size\": 10,\n  \"keyword\": \"\"\n}"}, "url": {"raw": "{{local.admin}}/companies/search", "host": ["{{local.admin}}"], "path": ["companies", "search"]}}}, {"name": "Get Company by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{local.admin}}/companies/1", "host": ["{{local.admin}}"], "path": ["companies", "1"]}}}, {"name": "Create Company", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Tech Innovations Co.\",\n  \"industry\": \"Information Technology\",\n  \"description\": \"A leading technology company specializing in software development and digital solutions\",\n  \"address\": {\n    \"provinceCode\": 1,\n    \"districtCode\": 1,\n    \"wardCode\": 1,\n    \"detail\": \"123 Technology Street, District 1\"\n  },\n  \"logo\": {\n    \"url\": \"https://example.com/logo.png\",\n    \"name\": \"company_logo.png\"\n  },\n  \"coverImage\": {\n    \"url\": \"https://example.com/cover.jpg\",\n    \"name\": \"company_cover.jpg\"\n  },\n  \"galleryImages\": [\n    {\n      \"url\": \"https://example.com/office1.jpg\",\n      \"name\": \"office_photo_1.jpg\"\n    }\n  ],\n  \"websiteUrl\": \"https://techinnovations.com\",\n  \"email\": \"<EMAIL>\",\n  \"phoneNumber\": \"0123456789\",\n  \"foundedYear\": \"2020\",\n  \"isIndividual\": false,\n  \"additionalData\": {\n    \"employeeCount\": \"50-100\",\n    \"socialMedia\": {\n      \"facebook\": \"https://facebook.com/techinnovations\",\n      \"linkedin\": \"https://linkedin.com/company/techinnovations\"\n    }\n  }\n}"}, "url": {"raw": "{{local.admin}}/companies", "host": ["{{local.admin}}"], "path": ["companies"]}}}, {"name": "Update Company", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Tech Company Updated\",\n  \"description\": \"A leading technology company\",\n  \"website\": \"https://techcompany.com\"\n}"}, "url": {"raw": "{{local.admin}}/companies/1", "host": ["{{local.admin}}"], "path": ["companies", "1"]}}}, {"name": "Delete Company", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{local.admin}}/companies/1", "host": ["{{local.admin}}"], "path": ["companies", "1"]}}}]}, {"name": "Contracts Management", "item": [{"name": "Get All Contracts", "request": {"method": "GET", "header": [], "url": {"raw": "{{local.admin}}/contracts", "host": ["{{local.admin}}"], "path": ["contracts"]}}}, {"name": "Search Contracts", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"page\": 0,\n  \"size\": 10,\n  \"keyword\": \"\"\n}"}, "url": {"raw": "{{local.admin}}/contracts/search", "host": ["{{local.admin}}"], "path": ["contracts", "search"]}}}, {"name": "Get Contract by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{local.admin}}/contracts/1", "host": ["{{local.admin}}"], "path": ["contracts", "1"]}}}]}, {"name": "Roles Management", "item": [{"name": "Get All Roles", "request": {"method": "GET", "header": [], "url": {"raw": "{{local.admin}}/roles", "host": ["{{local.admin}}"], "path": ["roles"]}}}, {"name": "Create Role", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"MANAGER\",\n  \"description\": \"Manager role with extended permissions for company management\",\n  \"permissions\": [\n    \"user_read\",\n    \"user_update\",\n    \"post_read\",\n    \"post_create\",\n    \"post_update\",\n    \"company_read\",\n    \"company_update\"\n  ]\n}"}, "url": {"raw": "{{local.admin}}/roles", "host": ["{{local.admin}}"], "path": ["roles"]}}}]}, {"name": "Permissions Management", "item": [{"name": "Get All Permissions", "request": {"method": "GET", "header": [], "url": {"raw": "{{local.admin}}/permissions", "host": ["{{local.admin}}"], "path": ["permissions"]}}}]}]}, {"name": "Mobile APIs", "item": [{"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"0123456789\",\n  \"password\": \"password123\",\n  \"deviceId\": \"device123\",\n  \"deviceName\": \"iPhone 15\"\n}"}, "url": {"raw": "{{local.mobile}}/auth/login", "host": ["{{local.mobile}}"], "path": ["auth", "login"]}}}, {"name": "Register", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"0123456789\",\n  \"deviceId\": \"device123\"\n}"}, "url": {"raw": "{{local.mobile}}/auth/register", "host": ["{{local.mobile}}"], "path": ["auth", "register"]}}}, {"name": "Verify OTP", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"0123456789\",\n  \"otp\": \"123456\",\n  \"tempToken\": \"temp_token_here\"\n}"}, "url": {"raw": "{{local.mobile}}/auth/verify-otp", "host": ["{{local.mobile}}"], "path": ["auth", "verify-otp"]}}}, {"name": "Create Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"0123456789\",\n  \"password\": \"password123\",\n  \"tempToken\": \"temp_token_here\"\n}"}, "url": {"raw": "{{local.mobile}}/auth/create-password", "host": ["{{local.mobile}}"], "path": ["auth", "create-password"]}}}, {"name": "Select Role", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"phone\": \"0123456789\",\n  \"role\": \"JOB_SEEKER\",\n  \"tempToken\": \"temp_token_here\",\n  \"deviceId\": \"device123\"\n}"}, "url": {"raw": "{{local.mobile}}/auth/select-role", "host": ["{{local.mobile}}"], "path": ["auth", "select-role"]}}}, {"name": "Logout", "request": {"method": "POST", "header": [], "url": {"raw": "{{local.mobile}}/auth/logout", "host": ["{{local.mobile}}"], "path": ["auth", "logout"]}}}, {"name": "Refresh <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"refresh_token_here\"\n}"}, "url": {"raw": "{{local.mobile}}/auth/refresh-token", "host": ["{{local.mobile}}"], "path": ["auth", "refresh-token"]}}}]}, {"name": "Resumes", "item": [{"name": "Create Resume", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Mobile Developer Resume\",\n  \"description\": \"Experienced mobile developer specializing in React Native and Flutter\",\n  \"userId\": 1,\n  \"isActive\": true,\n  \"skills\": [\"React Native\", \"Flutter\", \"JavaScript\", \"Dart\", \"TypeScript\", \"Firebase\"],\n  \"workExperiences\": [\n    {\n      \"company\": \"Mobile Solutions Inc.\",\n      \"position\": \"React Native Developer\",\n      \"startDate\": \"2021-03-01\",\n      \"endDate\": \"2023-11-30\",\n      \"description\": \"Developed cross-platform mobile applications using React Native and integrated with various APIs\"\n    },\n    {\n      \"company\": \"Tech Startup\",\n      \"position\": \"Junior Mobile Developer\",\n      \"startDate\": \"2020-01-15\",\n      \"endDate\": \"2021-02-28\",\n      \"description\": \"Built Flutter applications and worked with Firebase for backend services\"\n    }\n  ],\n  \"educations\": [\n    {\n      \"institution\": \"University of Technology\",\n      \"degree\": \"Bachelor of Software Engineering\",\n      \"startDate\": \"2016-09-01\",\n      \"endDate\": \"2020-06-30\",\n      \"description\": \"Software Engineering with focus on mobile application development\"\n    }\n  ],\n  \"contactInfo\": {\n    \"phoneNumber\": \"0987654321\",\n    \"email\": \"<EMAIL>\",\n    \"address\": \"456 Mobile Street\",\n    \"city\": \"Ho Chi Minh City\",\n    \"country\": \"Vietnam\",\n    \"linkedInUrl\": \"https://linkedin.com/in/janesmith\",\n    \"portfolioUrl\": \"https://janesmith.dev\"\n  },\n  \"partTimePreference\": {\n    \"availableDays\": [\"MONDAY\", \"WEDNESDAY\", \"FRIDAY\"],\n    \"preferredStartTime\": \"09:00\",\n    \"preferredEndTime\": \"17:00\",\n    \"maxHoursPerWeek\": 25\n  }\n}"}, "url": {"raw": "{{local.mobile}}/resumes", "host": ["{{local.mobile}}"], "path": ["resumes"]}}}, {"name": "Get Resume by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{local.mobile}}/resumes/1", "host": ["{{local.mobile}}"], "path": ["resumes", "1"]}}}, {"name": "Get Resumes by User ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{local.mobile}}/resumes/user/1", "host": ["{{local.mobile}}"], "path": ["resumes", "user", "1"]}}}, {"name": "Update Resume", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Senior Mobile Developer Resume\",\n  \"description\": \"Senior mobile developer with 5+ years experience in React Native and Flutter development\",\n  \"userId\": 1,\n  \"isActive\": true,\n  \"skills\": [\"React Native\", \"Flutter\", \"JavaScript\", \"Dart\", \"TypeScript\", \"Firebase\", \"Redux\", \"GraphQL\"],\n  \"workExperiences\": [\n    {\n      \"company\": \"Mobile Solutions Inc.\",\n      \"position\": \"Senior React Native Developer\",\n      \"startDate\": \"2021-03-01\",\n      \"description\": \"Lead mobile development team and architected scalable React Native applications\"\n    }\n  ],\n  \"educations\": [\n    {\n      \"institution\": \"University of Technology\",\n      \"degree\": \"Bachelor of Software Engineering\",\n      \"startDate\": \"2016-09-01\",\n      \"endDate\": \"2020-06-30\",\n      \"description\": \"Software Engineering with focus on mobile application development\"\n    }\n  ],\n  \"contactInfo\": {\n    \"phoneNumber\": \"0987654321\",\n    \"email\": \"<EMAIL>\",\n    \"address\": \"789 Updated Street\",\n    \"city\": \"Ho Chi Minh City\",\n    \"country\": \"Vietnam\",\n    \"linkedInUrl\": \"https://linkedin.com/in/janesmith\",\n    \"portfolioUrl\": \"https://janesmith.dev\"\n  }\n}"}, "url": {"raw": "{{local.mobile}}/resumes/1", "host": ["{{local.mobile}}"], "path": ["resumes", "1"]}}}, {"name": "Delete Resume", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{local.mobile}}/resumes/1", "host": ["{{local.mobile}}"], "path": ["resumes", "1"]}}}]}, {"name": "Companies", "item": [{"name": "Get All Companies", "request": {"method": "GET", "header": [], "url": {"raw": "{{local.mobile}}/companies", "host": ["{{local.mobile}}"], "path": ["companies"]}}}, {"name": "Get Company by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{local.mobile}}/companies/1", "host": ["{{local.mobile}}"], "path": ["companies", "1"]}}}, {"name": "Create Company", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Tech Company\",\n  \"description\": \"A technology company\",\n  \"website\": \"https://techcompany.com\"\n}"}, "url": {"raw": "{{local.mobile}}/companies", "host": ["{{local.mobile}}"], "path": ["companies"]}}}, {"name": "Update Company", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Tech Innovations Co. (Updated)\",\n  \"industry\": \"Information Technology & Digital Solutions\",\n  \"description\": \"A leading technology company specializing in software development, mobile applications, and digital transformation solutions. We help businesses leverage technology to achieve their goals.\",\n  \"address\": {\n    \"provinceCode\": 1,\n    \"districtCode\": 1,\n    \"wardCode\": 1,\n    \"detail\": \"456 Updated Technology Street, District 1\"\n  },\n  \"logo\": {\n    \"url\": \"https://example.com/updated_logo.png\",\n    \"name\": \"updated_company_logo.png\"\n  },\n  \"coverImage\": {\n    \"url\": \"https://example.com/updated_cover.jpg\",\n    \"name\": \"updated_company_cover.jpg\"\n  },\n  \"galleryImages\": [\n    {\n      \"url\": \"https://example.com/office1_updated.jpg\",\n      \"name\": \"office_photo_1_updated.jpg\"\n    },\n    {\n      \"url\": \"https://example.com/office2.jpg\",\n      \"name\": \"office_photo_2.jpg\"\n    }\n  ],\n  \"websiteUrl\": \"https://techinnovations.com\",\n  \"email\": \"<EMAIL>\",\n  \"phoneNumber\": \"0123456789\",\n  \"foundedYear\": \"2020\",\n  \"isIndividual\": false,\n  \"additionalData\": {\n    \"employeeCount\": \"100-200\",\n    \"certifications\": [\"ISO 9001\", \"ISO 27001\"],\n    \"socialMedia\": {\n      \"facebook\": \"https://facebook.com/techinnovations\",\n      \"linkedin\": \"https://linkedin.com/company/techinnovations\",\n      \"twitter\": \"https://twitter.com/techinnovations\"\n    }\n  }\n}"}, "url": {"raw": "{{local.mobile}}/companies/1", "host": ["{{local.mobile}}"], "path": ["companies", "1"]}}}, {"name": "Add Company to Favorites", "request": {"method": "POST", "header": [], "url": {"raw": "{{local.mobile}}/companies/1/favorite", "host": ["{{local.mobile}}"], "path": ["companies", "1", "favorite"]}}}, {"name": "Remove Company from Favorites", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{local.mobile}}/companies/1/favorite", "host": ["{{local.mobile}}"], "path": ["companies", "1", "favorite"]}}}]}, {"name": "Jobs", "item": [{"name": "Get All Jobs", "request": {"method": "GET", "header": [], "url": {"raw": "{{local.mobile}}/jobs", "host": ["{{local.mobile}}"], "path": ["jobs"]}}}, {"name": "Get Job by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{local.mobile}}/jobs/1", "host": ["{{local.mobile}}"], "path": ["jobs", "1"]}}}, {"name": "Create Job", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Software Engineer\",\n  \"description\": \"We are looking for a skilled software engineer to join our team and help build innovative web applications\",\n  \"location\": \"Ho Chi Minh City\",\n  \"jobType\": \"FULL_TIME\",\n  \"hourlyRate\": 500000,\n  \"requirements\": \"Bachelor's degree in Computer Science or related field, 2+ years of experience\",\n  \"responsibilities\": \"Develop and maintain web applications, collaborate with cross-functional teams\",\n  \"status\": \"ACTIVE\",\n  \"employerId\": 1,\n  \"remote\": true,\n  \"hoursPerWeek\": 40,\n  \"workDays\": [\"MONDAY\", \"TUESDAY\", \"WEDNESDAY\", \"THURSDAY\", \"FRIDAY\"],\n  \"requiredSkills\": [\"Java\", \"Spring Boot\", \"React\", \"JavaScript\", \"MySQL\"],\n  \"startDate\": \"2024-02-01\",\n  \"endDate\": \"2024-12-31\",\n  \"featured\": false\n}"}, "url": {"raw": "{{local.mobile}}/jobs", "host": ["{{local.mobile}}"], "path": ["jobs"]}}}, {"name": "Update Job", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Senior Software Engineer\",\n  \"description\": \"Looking for a senior software engineer\",\n  \"location\": \"Ho Chi Minh City\",\n  \"jobType\": \"FULL_TIME\",\n  \"hourlyRate\": 70000\n}"}, "url": {"raw": "{{local.mobile}}/jobs/1", "host": ["{{local.mobile}}"], "path": ["jobs", "1"]}}}]}, {"name": "Posts", "item": [{"name": "Search Posts", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"page\": 0,\n  \"size\": 10,\n  \"keyword\": \"\"\n}"}, "url": {"raw": "{{local.mobile}}/employer-posts", "host": ["{{local.mobile}}"], "path": ["employer-posts"]}}}, {"name": "Create Post", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"employerId\": 1,\n  \"title\": \"Part-time React Developer\",\n  \"description\": \"Looking for a skilled part-time React developer to help with our web application development\",\n  \"location\": \"Ho Chi Minh City\",\n  \"salary\": {\n    \"min\": 400000,\n    \"max\": 600000,\n    \"currency\": \"VND\",\n    \"period\": \"hour\"\n  },\n  \"jobType\": \"PART_TIME\",\n  \"isRemote\": true,\n  \"skills\": [\"React\", \"JavaScript\", \"TypeScript\", \"CSS\"],\n  \"experience\": {\n    \"minYears\": 2,\n    \"maxYears\": 5,\n    \"level\": \"INTERMEDIATE\"\n  },\n  \"education\": \"Bachelor's degree in Computer Science or related field\",\n  \"isFeatureJob\": false,\n  \"workingInformation\": [\n    {\n      \"dayOfWeek\": \"MONDAY\",\n      \"startTime\": \"09:00\",\n      \"endTime\": \"13:00\"\n    },\n    {\n      \"dayOfWeek\": \"WEDNESDAY\",\n      \"startTime\": \"09:00\",\n      \"endTime\": \"13:00\"\n    },\n    {\n      \"dayOfWeek\": \"FRIDAY\",\n      \"startTime\": \"09:00\",\n      \"endTime\": \"13:00\"\n    }\n  ],\n  \"workType\": \"PART_TIME\"\n}"}, "url": {"raw": "{{local.mobile}}/employer-posts", "host": ["{{local.mobile}}"], "path": ["employer-posts"]}}}, {"name": "Update Post", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Senior Part-time Developer\",\n  \"description\": \"Looking for senior part-time developer\",\n  \"hourlyRate\": 70000\n}"}, "url": {"raw": "{{local.mobile}}/employer-posts/1", "host": ["{{local.mobile}}"], "path": ["employer-posts", "1"]}}}, {"name": "Delete Post", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{local.mobile}}/employer-posts/1", "host": ["{{local.mobile}}"], "path": ["employer-posts", "1"]}}}, {"name": "Apply to Post", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"resumeId\": 1,\n  \"coverLetter\": \"Dear Hiring Manager,\\n\\nI am excited to apply for the Part-time React Developer position at your company. As a passionate React developer with 3 years of experience, I am confident that my skills and enthusiasm make me an ideal candidate for this role.\\n\\nKey highlights of my experience:\\n- 3+ years of React development experience\\n- Strong proficiency in JavaScript, TypeScript, and modern CSS\\n- Experience with state management using Redux and Context API\\n- Familiar with testing frameworks like Jest and React Testing Library\\n- Part-time work experience with remote collaboration\\n\\nI am particularly drawn to this opportunity because it offers the flexibility I need while allowing me to contribute to meaningful projects. I am available for the proposed schedule and excited about the possibility of working with your team.\\n\\nThank you for considering my application. I look forward to discussing how my skills and passion for React development can contribute to your team's success.\\n\\nBest regards,\\nAlex Johnson\",\n  \"availableStartDate\": \"2024-02-01\",\n  \"expectedSalary\": {\n    \"min\": 450000,\n    \"max\": 550000,\n    \"currency\": \"VND\",\n    \"period\": \"hour\"\n  },\n  \"additionalDocuments\": [\n    {\n      \"name\": \"Portfolio\",\n      \"url\": \"https://alexjohnson.dev/portfolio\"\n    },\n    {\n      \"name\": \"GitHub Profile\",\n      \"url\": \"https://github.com/alexjohnson\"\n    }\n  ]\n}"}, "url": {"raw": "{{local.mobile}}/employer-posts/1/apply", "host": ["{{local.mobile}}"], "path": ["employer-posts", "1", "apply"]}}}, {"name": "<PERSON> as Interested", "request": {"method": "POST", "header": [], "url": {"raw": "{{local.mobile}}/employer-posts/1/interest", "host": ["{{local.mobile}}"], "path": ["employer-posts", "1", "interest"]}}}]}, {"name": "Job Applications", "item": [{"name": "Create Job Application", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"jobId\": 1,\n  \"applicantId\": 1,\n  \"resumeId\": 1,\n  \"coverLetter\": \"Dear Hiring Manager,\\n\\nI am very interested in the Software Engineer position at your company. With my 3+ years of experience in full-stack development using Java and React, I believe I would be a great fit for this role.\\n\\nI have worked on various web applications and have strong problem-solving skills. I am passionate about creating clean, efficient code and collaborating with teams to deliver high-quality products.\\n\\nThank you for considering my application. I look forward to hearing from you.\\n\\nBest regards,\\nJohn <PERSON>\",\n  \"status\": \"PENDING\",\n  \"appliedAt\": \"2024-01-20T10:00:00\"\n}"}, "url": {"raw": "{{local.mobile}}/job-applications", "host": ["{{local.mobile}}"], "path": ["job-applications"]}}}, {"name": "Get Job Application by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{local.mobile}}/job-applications/1", "host": ["{{local.mobile}}"], "path": ["job-applications", "1"]}}}]}]}, {"name": "Shared APIs", "item": [{"name": "Address", "item": [{"name": "Get All Provinces", "request": {"method": "GET", "header": [], "url": {"raw": "localhost:8080/v1/addresses/provinces", "host": ["localhost"], "port": "8080", "path": ["v1", "addresses", "provinces"]}}}, {"name": "Get Province by Code", "request": {"method": "GET", "header": [], "url": {"raw": "localhost:8080/v1/addresses/provinces/1", "host": ["localhost"], "port": "8080", "path": ["v1", "addresses", "provinces", "1"]}}}, {"name": "Get Districts by Province Code", "request": {"method": "GET", "header": [], "url": {"raw": "localhost:8080/v1/addresses/provinces/1/districts", "host": ["localhost"], "port": "8080", "path": ["v1", "addresses", "provinces", "1", "districts"]}}}, {"name": "Get Wards by District Code", "request": {"method": "GET", "header": [], "url": {"raw": "localhost:8080/v1/addresses/districts/1/wards", "host": ["localhost"], "port": "8080", "path": ["v1", "addresses", "districts", "1", "wards"]}}}, {"name": "Sync Address Data", "request": {"method": "POST", "header": [], "url": {"raw": "localhost:8080/v1/addresses/sync", "host": ["localhost"], "port": "8080", "path": ["v1", "addresses", "sync"]}}}]}, {"name": "File Upload", "item": [{"name": "Upload File", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": []}, {"key": "type", "value": "image", "type": "text"}]}, "url": {"raw": "localhost:8080/v1/files/upload", "host": ["localhost"], "port": "8080", "path": ["v1", "files", "upload"]}}}, {"name": "Upload Multiple Files", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "files", "type": "file", "src": []}, {"key": "type", "value": "image", "type": "text"}]}, "url": {"raw": "localhost:8080/v1/files/upload/multiple", "host": ["localhost"], "port": "8080", "path": ["v1", "files", "upload", "multiple"]}}}]}, {"name": "Lookup", "item": [{"name": "Get All Lookups", "request": {"method": "GET", "header": [], "url": {"raw": "localhost:8080/v1/lookups?type=SKILL", "host": ["localhost"], "port": "8080", "path": ["v1", "lookups"], "query": [{"key": "type", "value": "SKILL"}]}}}, {"name": "Get Lookups by User", "request": {"method": "GET", "header": [], "url": {"raw": "localhost:8080/v1/lookups/user?type=SKILL&userId=1", "host": ["localhost"], "port": "8080", "path": ["v1", "lookups", "user"], "query": [{"key": "type", "value": "SKILL"}, {"key": "userId", "value": "1"}]}}}]}]}]}