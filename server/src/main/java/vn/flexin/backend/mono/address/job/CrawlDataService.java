package vn.flexin.backend.mono.address.job;

import lombok.AllArgsConstructor;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.flexin.backend.mono.address.entity.District;
import vn.flexin.backend.mono.address.entity.Province;
import vn.flexin.backend.mono.address.entity.Ward;
import vn.flexin.backend.mono.address.repository.DistrictRepository;
import vn.flexin.backend.mono.address.repository.ProvinceRepository;
import vn.flexin.backend.mono.address.repository.WardRepository;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.ArrayList;
import java.util.List;

@Service
@AllArgsConstructor
public class CrawlDataService {

    private final ProvinceRepository provinceRepository;
    private final DistrictRepository districtRepository;
    private final WardRepository wardRepository;

//    @Scheduled(cron = "0 0 1 * * ?")
    @Transactional
    public boolean syncData() throws JSONException {
        String url = "https://provinces.open-api.vn/api/?depth=3";
        String jsonData = fetchJsonData(url);
        List<Province> allProvinces = new ArrayList<>();
        List<District> allDistrictes = new ArrayList<>();
        List<Ward> allWards = new ArrayList<>();


        if (jsonData != null) {
            JSONArray provinces = new JSONArray(jsonData);

            for (int i = 0; i < provinces.length(); i++) {
                JSONObject provinceJson = provinces.getJSONObject(i);
                Province province = saveProvince(provinceJson);
                allProvinces.add(province);
                JSONArray districts = provinceJson.getJSONArray("districts");
                for (int j = 0; j < districts.length(); j++) {
                    JSONObject districtJson = districts.getJSONObject(j);
                    District district = saveDistrict(districtJson, province);
                    allDistrictes.add(district);
                    JSONArray wards = districtJson.getJSONArray("wards");
                    for (int k = 0; k < wards.length(); k++) {
                        JSONObject wardJson = wards.getJSONObject(k);
                        Ward ward =saveWard(wardJson, district);
                        allWards.add(ward);
                    }
                }
            }
            provinceRepository.saveAll(allProvinces);
            districtRepository.saveAll(allDistrictes);
            wardRepository.saveAll(allWards);
        }
        return true;
    }

    private String fetchJsonData(String url) {
        HttpClient client = HttpClient.newHttpClient();
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(url))
                .build();

        try {
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            return response.body();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private Province saveProvince(JSONObject provinceJson) throws JSONException {
        Province province = new Province();
        province.setCode(provinceJson.getLong("code"));
        province.setName(provinceJson.getString("name"));
        province.setCodename(provinceJson.getString("codename"));
        province.setDivisionType(provinceJson.getString("division_type"));
        province.setPhoneCode(provinceJson.getInt("phone_code"));
        return province;
    }

    private District saveDistrict(JSONObject districtJson, Province province) throws JSONException {
        District district = new District();
        district.setCode(districtJson.getLong("code"));
        district.setName(districtJson.getString("name"));
        district.setCodename(districtJson.getString("codename"));
        district.setDivisionType(districtJson.getString("division_type"));
        district.setProvince(province);
        return district;
    }

    private Ward saveWard(JSONObject wardJson, District district) throws JSONException {
        Ward ward = new Ward();
        ward.setCode(wardJson.getLong("code"));
        ward.setName(wardJson.getString("name"));
        ward.setCodename(wardJson.getString("codename"));
        ward.setDivisionType(wardJson.getString("division_type"));
        ward.setDistrict(district);
        return ward;
    }
}
