package vn.flexin.backend.mono.contract.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.jpa.domain.Specification;
import vn.flexin.backend.mono.common.dto.BaseFilter;
import vn.flexin.backend.mono.common.repository.param.Condition;
import vn.flexin.backend.mono.common.repository.param.Join;
import vn.flexin.backend.mono.common.repository.param.Operator;
import vn.flexin.backend.mono.common.repository.param.Where;
import vn.flexin.backend.mono.common.util.SpecificationUtil;
import vn.flexin.backend.mono.contract.entity.Contract;
import vn.flexin.backend.mono.contract.entity.PaymentRecord;
import vn.flexin.backend.mono.contract.entity.TimeEntry;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;

@Data
@NoArgsConstructor
public class PaymentRecordFilter extends BaseFilter<PaymentRecord> {

    private Long contractId;

    private LocalDate startDate;

    private LocalDate endDate;
    
    private String status; // pending, approved, rejected

    @Override
    public Specification<PaymentRecord> toSpecification() {
        var condition = new Condition()
                .append(new Where(PaymentRecord.Fields.status, status))
                .append(new Where(PaymentRecord.Fields.date, Operator.GREATER_THAN_OR_EQUAL, LocalDateTime.of(startDate, LocalTime.MIN)))
                .append(new Where(PaymentRecord.Fields.date, Operator.LESS_THAN_OR_EQUAL, LocalDateTime.of(endDate, LocalTime.MAX)));
        if (contractId != null) {
            var whereList = new ArrayList<Where>();
            whereList.add(new Where(Contract.Fields.id, contractId));
            condition.append(new Join(PaymentRecord.Fields.contract, whereList));
        }
        return SpecificationUtil.bySearchQuery(condition);
    }
}