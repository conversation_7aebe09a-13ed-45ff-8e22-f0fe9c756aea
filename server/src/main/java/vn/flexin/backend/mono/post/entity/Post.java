package vn.flexin.backend.mono.post.entity;


import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.Type;
import vn.flexin.backend.mono.common.entity.AbstractAuditingEntity;
import vn.flexin.backend.mono.company.entity.Branch;
import vn.flexin.backend.mono.lookup.entity.Lookup;
import vn.flexin.backend.mono.post.dto.PostRequiredDocument;
import vn.flexin.backend.mono.post.dto.PostWorkingInformation;
import vn.flexin.backend.mono.user.entity.User;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
@Table(name = "t_posts")
public class Post extends AbstractAuditingEntity<Long> implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull
    private String title;

    private String description;

    private String location;

    @ManyToOne(fetch = FetchType.EAGER, cascade = {CascadeType.DETACH, CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH})
    @JoinColumn(name = "employer_id")
    private User employer;

    @OneToOne(fetch = FetchType.EAGER, cascade = {CascadeType.DETACH, CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH})
    @JoinColumn(name = "salary_id")
    private Salary salary;

    @ToString.Exclude
    @ManyToMany(fetch = FetchType.EAGER, cascade = {CascadeType.DETACH, CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH})
    @JoinTable(
            name = "t_post_skills",
            joinColumns = @JoinColumn(name = "post_id"),
            inverseJoinColumns = @JoinColumn(name = "look_up_id")
    )
    private List<Lookup> skills;

    @Type(JsonType.class)
    @Column(columnDefinition = "jsonb")
    private Map<String, Object> experience;

    @ToString.Exclude
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "post", cascade = {CascadeType.DETACH, CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH})
    private List<PostView> postViews;

    @ToString.Exclude
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "post", cascade = {CascadeType.DETACH, CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH})
    private List<PostInterest> postInterests;

    @ToString.Exclude
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "post", cascade = {CascadeType.DETACH, CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH})
    private List<PostApplication> postApplication;

    // get from LookUp with type jobTypes
    private String jobType;

    // get from LookUp with type experienceLevels
    private String experienceLevel;

    private boolean isRemote;

    // get from Lookup with type educationLevels
    private String education;

    private boolean isFeatureJob;

    private String status;

    private LocalDateTime closedAt;

    @ManyToOne(fetch = FetchType.EAGER, cascade = {CascadeType.DETACH, CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH})
    @JoinColumn(name = "branch_id")
    private Branch branch;

    private boolean urgentHiring;

    private LocalDateTime postDate;

    private LocalDateTime expireDate;

    @Type(JsonType.class)
    @Column(columnDefinition = "jsonb")
    private List<PostWorkingInformation> workingInformation;

    private String workType;

    private String positions;

    @Type(JsonType.class)
    @Column(columnDefinition = "jsonb")
    private List<String> benefits;

    @Type(JsonType.class)
    @Column(columnDefinition = "jsonb")
    private List<PostRequiredDocument> requiredDocuments;

    private String rejectionReason;

    public long getPostViewCount() {
        if (postViews == null) return 0;

        return postViews.stream()
                .filter(view -> view.getViewer() != null)
                .map(view -> view.getViewer().getId())
                .distinct()
                .count();
    }
}
