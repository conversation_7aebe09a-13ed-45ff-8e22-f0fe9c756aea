package vn.flexin.backend.mono.post.service.admin.impl;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import vn.flexin.backend.mono.common.dto.PaginationResponse;
import vn.flexin.backend.mono.common.exception.ResourceNotFoundException;
import vn.flexin.backend.mono.common.util.ModelMapperUtils;
import vn.flexin.backend.mono.company.entity.Branch;
import vn.flexin.backend.mono.company.service.mobile.BranchService;
import vn.flexin.backend.mono.lookup.entity.Lookup;
import vn.flexin.backend.mono.lookup.enums.LookupType;
import vn.flexin.backend.mono.lookup.service.LookupService;
import vn.flexin.backend.mono.post.dto.*;
import vn.flexin.backend.mono.post.entity.Post;
import vn.flexin.backend.mono.post.entity.Salary;
import vn.flexin.backend.mono.post.enums.PostStatus;
import vn.flexin.backend.mono.post.repository.PostApplicationRepository;
import vn.flexin.backend.mono.post.repository.PostInterestRepository;
import vn.flexin.backend.mono.post.repository.PostRepository;
import vn.flexin.backend.mono.post.repository.PostViewRepository;
import vn.flexin.backend.mono.post.service.SalaryService;
import vn.flexin.backend.mono.post.service.admin.AdminPostService;

import java.time.LocalDateTime;
import java.time.Month;
import java.time.format.TextStyle;
import java.util.*;

@Service
@AllArgsConstructor
@Slf4j
public class AdminPostServiceImpl implements AdminPostService {

    private final PostViewRepository postViewRepository;
    private final PostRepository postRepository;
    private final SalaryService salaryService;
    private final LookupService lookupService;
    private final PostInterestRepository postInterestRepository;
    private final PostApplicationRepository postApplicationRepository;
    private final BranchService branchService;

    @Override
    @Transactional(readOnly = true)
    public Pair<List<SearchAdminPostResponse>, PaginationResponse> searchPostForAdmin(PostFilter request) {
        Page<Post> posts = postRepository.findAll(request);

        List<SearchAdminPostResponse> postResponses =
                posts.stream().map(this::toSearchPostResponse).toList();

        PaginationResponse paging = new PaginationResponse(request.getLimit(), request.getPage(),
                (int) posts.getTotalElements());

        return Pair.of(postResponses, paging);

    }

    private SearchAdminPostResponse toSearchPostResponse(Post post) {
        SearchAdminPostResponse response = ModelMapperUtils.toObject(post, SearchAdminPostResponse.class);
        response.setSalary(new SalaryDto(post.getSalary()));
        setListSkills(response, post);
        response.setApplicationCount(CollectionUtils.isEmpty(post.getPostApplication()) ? 0 : post.getPostApplication().size());
        response.setViewCount(CollectionUtils.isEmpty(post.getPostViews()) ? 0 : post.getPostViews().size());
        if (post.getBranch() != null && post.getBranch().getCompany() != null) {
            response.setCompanyName(post.getBranch().getCompany().getName());
            response.setCompanyId(post.getBranch().getCompany().getId());
        }
        if (post.getBranch() != null) {
            response.setBranchId(post.getBranch().getId());
            response.setBranchName(post.getBranch().getName());
        }
        if (post.getBranch() != null && post.getBranch().getAddress() != null) {
            var address = post.getBranch().getAddress();
            StringBuilder locationBuilder = new StringBuilder();
            if (address.getDetailAddress() != null) {
                locationBuilder.append(address.getDetailAddress());
            }
            if (address.getWard() != null && address.getWard().getName() != null) {
                if (locationBuilder.length() > 0) locationBuilder.append(", ");
                locationBuilder.append(address.getWard().getName());
            }
            if (address.getDistrict() != null && address.getDistrict().getName() != null) {
                if (locationBuilder.length() > 0) locationBuilder.append(", ");
                locationBuilder.append(address.getDistrict().getName());
            }
            if (address.getProvince() != null && address.getProvince().getName() != null) {
                if (locationBuilder.length() > 0) locationBuilder.append(", ");
                locationBuilder.append(address.getProvince().getName());
            }
            response.setLocation(locationBuilder.toString());
        } else {
            response.setLocation(post.getLocation());
        }
        response.setWorkingInformation(post.getWorkingInformation());
        response.setWorkType(post.getWorkType());
        response.setExperienceLevel(post.getExperienceLevel());
        return response;
    }

    private void setListSkills(SearchAdminPostResponse response, Post post) {
        List<String> skills = new ArrayList<>();
        if(!CollectionUtils.isEmpty(post.getSkills())) {
            skills = post.getSkills().stream().map(Lookup::getValue).toList();
        }
        response.setSkills(skills);
    }

    @Override
    public Post getById(Long id) {
        return postRepository.findById(id).orElseThrow(() -> new ResourceNotFoundException("Post not found."));
    }

    @Override
    @Transactional(readOnly = true)
    public SearchAdminPostResponse getDetailPost(Long id) {
        Post post = getById(id);
        return toDetailPostResponse(post);
    }

    private SearchAdminPostResponse toDetailPostResponse (Post post) {
        SearchAdminPostResponse adminPostResponse = ModelMapperUtils.toObject(post, SearchAdminPostResponse.class);
        adminPostResponse.setSalary(new SalaryDto(post.getSalary()));
        setListSkills(adminPostResponse, post);
        adminPostResponse.setApplicationCount(CollectionUtils.isEmpty(post.getPostApplication()) ? 0 : post.getPostApplication().size());
        adminPostResponse.setViewCount(CollectionUtils.isEmpty(post.getPostViews()) ? 0 : post.getPostViews().size());
        if(post.getBranch() != null && post.getBranch().getCompany() != null) {
            adminPostResponse.setCompanyName(post.getBranch().getCompany().getName());
            adminPostResponse.setCompanyId(post.getBranch().getCompany().getId());
        }
        if (post.getBranch() != null) {
            adminPostResponse.setBranchId(post.getBranch().getId());
            adminPostResponse.setBranchName(post.getBranch().getName());
        }
        if (post.getBranch() != null && post.getBranch().getAddress() != null) {
            var address = post.getBranch().getAddress();
            StringBuilder locationBuilder = new StringBuilder();
            if (address.getDetailAddress() != null) {
                locationBuilder.append(address.getDetailAddress());
            }
            if (address.getWard() != null && address.getWard().getName() != null) {
                if (locationBuilder.length() > 0) locationBuilder.append(", ");
                locationBuilder.append(address.getWard().getName());
            }
            if (address.getDistrict() != null && address.getDistrict().getName() != null) {
                if (locationBuilder.length() > 0) locationBuilder.append(", ");
                locationBuilder.append(address.getDistrict().getName());
            }
            if (address.getProvince() != null && address.getProvince().getName() != null) {
                if (locationBuilder.length() > 0) locationBuilder.append(", ");
                locationBuilder.append(address.getProvince().getName());
            }
            adminPostResponse.setLocation(locationBuilder.toString());
        } else {
            adminPostResponse.setLocation(post.getLocation());
        }
        adminPostResponse.setWorkingInformation(post.getWorkingInformation());
        adminPostResponse.setWorkType(post.getWorkType());
        adminPostResponse.setExperienceLevel(post.getExperienceLevel());
        return adminPostResponse;
    }

    @Override
    @Transactional
    public Long createAdminPost(CreateAdminPostRequest request) {
        Post post = ModelMapperUtils.toObject(request, Post.class);

        Branch branch = branchService.getById(request.getBranchId());
        post.setBranch(branch);

        Salary salary = salaryService.createSalary(request.getSalary());
        post.setSalary(salary);

        if (request.getSkills() != null && !request.getSkills().isEmpty()) {
            List<Lookup> skills = createPostSkills(request.getSkills());
            post.setSkills(skills);
        } else {
            post.setSkills(new ArrayList<>());
        }

        Map<String, Object> experience = createPostExperience(request.getExperience());
        post.setExperience(experience);

        post.setStatus(PostStatus.PENDING.getValue());
        post.setClosedAt(null);

        save(post);

        return post.getId();
    }

    private List<Lookup> createPostSkills(List<String> skills) {
        List<Lookup> skillLookups = lookupService.getAllByType(LookupType.SKILL.getValue());
        List<Lookup> results = new ArrayList<>();
        skills.forEach(skill -> {
            Lookup lookup = skillLookups.stream().filter(item -> item.getValue().equals(skill)).findFirst().orElse(null);
            if (lookup != null) {
                results.add(lookup);
            }
        });
        return results;
    }

    private Map<String, Object> createPostExperience(ExperienceDto experience) {
        Map<String, Object> result = new HashMap<>();
        if (experience != null) {
            result.put("min", experience.getMin());
            result.put("max", experience.getMax());
        }
        return result;
    }

    @Override
    public Post save(Post post) {
        return postRepository.save(post);
    }

    @Override
    @Transactional
    public SearchAdminPostResponse updateAdminPost(UpdateAdminPostRequest request) {
        Post post = getById(request.getId());

        post =  ModelMapperUtils.toObject(request, Post.class);

        if(request.getBranchId() != null){
            Branch branch = branchService.getById(request.getBranchId());
            post.setBranch(branch);
        }

        if(request.getSkills() != null && !request.getSkills().isEmpty()) {
            List<Lookup> skills = createPostSkills(request.getSkills());
            post.setSkills(skills);
        } else if (request.getSkills() != null) {
            post.setSkills(new ArrayList<>());
        }

        if(request.getExperience() != null) {
            Map<String, Object> experience = createPostExperience(request.getExperience());
            post.setExperience(experience);
        }

        if (request.getSalary() != null) {
            Salary salary = post.getSalary();
            salaryService.updateSalary(salary, request.getSalary());
        }

        save(post);

        return getDetailPost(post.getId());
    }

    @Override
    @Transactional
    public void deletePost(Long id) {
        Post post = getById(id);
        postViewRepository.deleteByPostId(id);
        postApplicationRepository.deleteByPostId(id);
        postInterestRepository.deleteByPostId(id);
        postApplicationRepository.deleteByPostId(id);
        postRepository.delete(post);
    }

    @Override
    @Transactional
    public SearchAdminPostResponse approvePost(Long id) {
        Post post = getById(id);
        post.setStatus(PostStatus.ACTIVE.getValue());
        save(post);
        return toDetailPostResponse(post);
    }

    @Override
    @Transactional
    public SearchAdminPostResponse rejectPost(Long id, RejectPostRequest request) {
        Post post = getById(id);
        post.setStatus(PostStatus.REJECTED.getValue());
        post.setRejectionReason(request.getReason());
        save(post);
        return toDetailPostResponse(post);
    }

    @Override
    @Transactional
    public StatisticAdminPostResponse getStatisticsPost() {
        int currentYear = LocalDateTime.now().getYear();
        List<Object[]> results = postRepository.countPostsByMonthAndStatusAndYear(currentYear);

        StatisticAdminPostResponse response = new StatisticAdminPostResponse();
        int totalPosts = 0;
        int totalApplications = 0;

        // Initialize counts
        response.setTotal(totalPosts);
        response.setActive(0);
        response.setExpired(0);
        response.setDraft(0);
        response.setClosed(0);
        response.setTotalApplications(0);

        // Process counts
        Map<Integer, Map<String, Integer>> monthlyStats = new HashMap<>();
        Map<String, Integer> locationStats = new HashMap<>();

        for (Object[] result : results) {
            int month = ((Number) result[0]).intValue();
            String status = (String) result[1];
            String location = (String) result[2];
            int postCount = ((Number) result[3]).intValue();
            int applicationCount = ((Number) result[4]).intValue();

            // Update total counts by status
            switch (status) {
                case "active" -> response.setActive(response.getActive() + postCount);
                case "expired" -> response.setExpired(response.getExpired() + postCount);
                case "draft" -> response.setDraft(response.getDraft() + postCount);
                case "closed" -> response.setClosed(response.getClosed() + postCount);
            }
            totalPosts += postCount;
            totalApplications += applicationCount;

            // Group by month
            monthlyStats.computeIfAbsent(month, k -> new HashMap<>())
                    .merge(status, postCount, Integer::sum);

            // Group by location
            locationStats.merge(location, postCount, Integer::sum);
        }

        response.setTotal(totalPosts);
        response.setTotalApplications(totalApplications);

        // Convert monthly stats to response format
        List<CountMonthResponse> monthStats = monthlyStats.entrySet().stream()
                .map(entry -> {
                    String monthName = Month.of(entry.getKey()).getDisplayName(TextStyle.FULL, Locale.ENGLISH);
                    int monthTotal = entry.getValue().values().stream().mapToInt(Integer::intValue).sum();
                    return new CountMonthResponse(monthName, monthTotal);
                })
                .sorted(Comparator.comparing(CountMonthResponse::getMonth))
                .toList();

        // Convert location stats to response format
        List<CountLocationResponse> locationResponses = locationStats.entrySet().stream()
                .map(entry -> new CountLocationResponse(entry.getKey(), entry.getValue()))
                .sorted(Comparator.comparing(CountLocationResponse::getLocation))
                .toList();

        response.setByMonth(monthStats);
        response.setByLocation(locationResponses);
        return response;
    }


}
