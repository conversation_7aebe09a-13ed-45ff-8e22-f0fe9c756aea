package vn.flexin.backend.mono.address.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "t_provinces")
public class Province {

    @Id
    @Column(name = "code")
    private Long code;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "codename", nullable = false)
    private String codename;

    @Column(name = "division_type", nullable = false)
    private String divisionType;

    @Column(name = "phone_code", nullable = false)
    private Integer phoneCode;

//    @OneToMany(mappedBy = "province", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
//    private List<District> districts;

}
