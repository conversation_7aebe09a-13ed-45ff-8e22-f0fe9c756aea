package vn.flexin.backend.mono.file.service.impl;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.AmazonS3Exception;
import com.amazonaws.services.s3.model.CopyObjectRequest;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import vn.flexin.backend.mono.common.exception.BadRequestException;
import vn.flexin.backend.mono.common.exception.ResourceNotFoundException;
import vn.flexin.backend.mono.file.config.FileConfig;
import vn.flexin.backend.mono.file.entity.File;
import vn.flexin.backend.mono.file.enums.FileType;
import vn.flexin.backend.mono.file.repository.FileRepository;
import vn.flexin.backend.mono.file.service.FileService;

import java.io.IOException;
import java.util.List;

@Service
@AllArgsConstructor
public class AWSFileServiceImpl implements FileService {

    private final AmazonS3 s3Client;
    private final FileConfig fileConfig;
    private final FileRepository fileRepository;

    @Override
    public File save(File file) {
        return fileRepository.save(file);
    }

    @Override
    public void saveAll(List<File> files) {
        fileRepository.saveAll(files);
    }

    @Override
    public void deleteByFilePath(String filePath) {
        fileRepository.deleteByFilePath(filePath);
    }

    @Override
    public String retrieveImagePathByName(String name) {
        File file = getFilePathByFileName(name, fileRepository);
        if (file == null) {
            throw new ResourceNotFoundException("File name not found.");
        }
        if (!FileType.isImageFile(file)) {
            throw new BadRequestException("File is not image.");
        }
        return file.getFilePath();
    }

    @Override
    public void handleUploadFileToCloudProvider(MultipartFile file, String filePath, FileType fileType) {
        String targetBucket;
        switch (fileType) {
            case FileType.AVATAR -> targetBucket = fileConfig.getAvatarBucket();
            case FileType.DOCUMENT -> targetBucket = fileConfig.getDocumentBucket();
            default -> throw new BadRequestException("Unsupported file type");
        }

        String s3FilePath = "%s/%s".formatted(fileType.getType(), filePath);
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentType(file.getContentType());

        try {
            // Upload file lên bucket tương ứng
            PutObjectRequest putRequest = new PutObjectRequest(
                    targetBucket,
                    s3FilePath,
                    file.getInputStream(),
                    metadata
            );
            s3Client.putObject(putRequest);
        } catch (Exception e) {
            throw new BadRequestException("Can not upload file to server.");
        }
    }

    @Override
    public boolean handleDeleteFileOnCloudProvider(File file) {
        String sourceBucket;
        String backupBucket = fileConfig.getBackupBucket();

        if (file.getType() == FileType.AVATAR) {
            sourceBucket = fileConfig.getAvatarBucket();
        } else if (file.getType() == FileType.DOCUMENT) {
            sourceBucket = fileConfig.getDocumentBucket();
        } else {
            throw new BadRequestException("Unsupported file type");
        }

        String s3FilePath = "%s/%s".formatted(file.getType(), file.getFilePath());

        try {
            CopyObjectRequest copyRequest = new CopyObjectRequest(
                    sourceBucket,
                    s3FilePath,
                    backupBucket,
                    s3FilePath
            );
            s3Client.copyObject(copyRequest);

            s3Client.deleteObject(sourceBucket, s3FilePath);
        } catch (AmazonS3Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    public String createFilePath(String modifiedFileName, FileType fileType) {
        String bucketName = fileType == FileType.AVATAR ? FileType.AVATAR.getType() : FileType.DOCUMENT.getType();
        return "https://" + bucketName + ".s3.amazonaws.com" + "/" +
                fileType.getType() + "/" +
                modifiedFileName;
    }
}

