package vn.flexin.backend.mono.file.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import vn.flexin.backend.mono.file.entity.File;

import java.util.Optional;

public interface FileRepository extends JpaRepository<File, Long> {

    @Modifying
    @Query(value = "DELETE FROM File f WHERE f.filePath = :filePath")
    void deleteByFilePath(String filePath);


    Optional<File> findFirstByFileName(String fileName);
}
