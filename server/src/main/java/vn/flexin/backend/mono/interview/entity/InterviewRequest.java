package vn.flexin.backend.mono.interview.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.FieldNameConstants;
import vn.flexin.backend.mono.interview.enums.InterviewRequestStatus;
import vn.flexin.backend.mono.interview.enums.PostType;
import vn.flexin.backend.mono.common.entity.AbstractAuditingEntity;
import vn.flexin.backend.mono.user.entity.User;

import java.time.LocalDateTime;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@FieldNameConstants
@Entity
@Table(name = "t_interview_requests")
public class InterviewRequest extends AbstractAuditingEntity<Long> {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    @JoinColumn(name = "request_user_id")
    private User requestUser;

    @ManyToOne
    @JoinColumn(name = "receive_user_id")
    private User receiveUser;

    private Long postId;

    private String postName;

    @Enumerated(EnumType.STRING)
    private PostType postType;

    private LocalDateTime expiredDate;

    private InterviewRequestStatus status;

    private String rejectNote;

    private String requestNote;
}
