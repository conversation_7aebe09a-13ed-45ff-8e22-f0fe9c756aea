package vn.flexin.backend.mono.jobseekerpost.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.Where;
import vn.flexin.backend.mono.address.entity.Address;
import vn.flexin.backend.mono.interview.entity.InterviewRequest;
import vn.flexin.backend.mono.common.entity.AbstractAuditingEntity;
import vn.flexin.backend.mono.post.enums.PostStatus;
import vn.flexin.backend.mono.user.entity.User;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
@Table(name = "t_job_seeker_posts")
public class JobSeekerPost extends AbstractAuditingEntity<Long>  implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull
    private String title;

    private String industry;

    private String description;

    @ManyToOne(fetch = FetchType.EAGER, cascade = {CascadeType.DETACH, CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH})
    @JoinColumn(name = "job_seeker_id")
    private User jobSeeker;

    @OneToOne(mappedBy = "post", cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "salary_id")
    private JobSeekerSalary salary;

    @ElementCollection
    @CollectionTable(name = "t_job_seeker_skills", joinColumns = @JoinColumn(name = "job_seeker_id"))
    @Column(name = "skill")
    private Set<String> skills = new HashSet<>();

    @ElementCollection
    @CollectionTable(name = "t_job_seeker_languages", joinColumns = @JoinColumn(name = "job_seeker_id"))
    @Column(name = "language")
    private Set<String> languages = new HashSet<>();

    private String educationLevel;

    private String educationDetail;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private Integer workingHourPerDay;

    @ElementCollection
    @CollectionTable(name = "t_job_seeker_working_days", joinColumns = @JoinColumn(name = "job_seeker_working_time_id"))
    @Column(name = "working_day")
    private Set<String> workingDays;

    @ElementCollection
    @CollectionTable(name = "t_job_seeker_working_shifts", joinColumns = @JoinColumn(name = "job_seeker_working_time_id"))
    @Column(name = "working_shift")
    private Set<String> workingShifts;

    @ToString.Exclude
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "post", cascade = {CascadeType.DETACH, CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH})
    private List<JobSeekerExperience> experiences;

    private String jobType;

    private String contractType;

    private String workType;

    @OneToOne
    @JoinColumn(name = "address_id")
    private Address location;

    private boolean isFeatureJob;

    private Integer featureDuration;

    private boolean isEnableEmailNotification;

    private boolean isEnableInformation;

    private boolean isAutoAcceptInterviewInvitation;

    private boolean isReady;

    private LocalDateTime activeDate;

    @Enumerated(EnumType.STRING)
    private PostStatus postStatus;

    @ToString.Exclude
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "post", cascade = {CascadeType.DETACH, CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH})
    private List<JobSeekerPostView> postViews;

    @ToString.Exclude
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "postId",cascade = {CascadeType.DETACH, CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH})
    @Where(clause = "post_type = 'JOB_SEEKER_POST'")
    private List<InterviewRequest> interviewRequests;

    public List<JobSeekerPostView> getPostViews() {
        return this.postViews == null ? new ArrayList<>() : this.postViews;
    }

    public List<InterviewRequest> getInterviewRequests() {
        return this.interviewRequests == null ? new ArrayList<>() : this.interviewRequests;
    }
}
