package vn.flexin.backend.mono.user.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.flexin.backend.mono.address.dto.request.AddressRequest;
import vn.flexin.backend.mono.auth.util.AuthConstant;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateUserRequest {
    @NotBlank(message = "User ID is required")
    private String userId;
    
    private String fullName;
    
    @Email(message = "Invalid email format")
    private String email;
    
    @Pattern(regexp = AuthConstant.PHONE_REGEX, message = "Invalid phone number format")
    private String phoneNumber;
    
    private String deviceId;
    
    private String avatarUrl;
    
    private AddressRequest address;
}
