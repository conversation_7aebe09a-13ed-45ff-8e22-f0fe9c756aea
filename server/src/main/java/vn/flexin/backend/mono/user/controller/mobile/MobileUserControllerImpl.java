package vn.flexin.backend.mono.user.controller.mobile;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.flexin.backend.mono.common.dto.ApiResponse;
import vn.flexin.backend.mono.common.enums.AppStatus;
import vn.flexin.backend.mono.common.exception.ResponseAppStatusException;
import vn.flexin.backend.mono.user.dto.CreateMobileUserRequest;
import vn.flexin.backend.mono.user.dto.CreateUserRequest;
import vn.flexin.backend.mono.user.dto.SearchUserRequest;
import vn.flexin.backend.mono.user.dto.UserDto;
import vn.flexin.backend.mono.user.entity.User;
import vn.flexin.backend.mono.user.service.UserService;

import jakarta.validation.Valid;

import java.util.Collections;
import java.util.List;

@RestController
@RequestMapping("/v1/mobile/users")
@RequiredArgsConstructor
@Slf4j
public class MobileUserControllerImpl implements MobileUserController {

    private final UserService userService;
    
    @Override
    public ResponseEntity<User> createUser(@Valid CreateMobileUserRequest createUserRequest) {
        try {
            User savedUser = userService.createUser(createUserRequest);
            return new ResponseEntity<>(savedUser, new HttpHeaders(), AppStatus.OK.value());
        } catch (ResponseAppStatusException e) {
            throw e;
        } catch (Exception e) {
            throw new ResponseAppStatusException(AppStatus.DATABASE_ERROR, "Can't send email verification", e);
        }
    }

    @Override
    public ResponseEntity<ApiResponse<List<UserDto>>> getAllUsers() {
        List<UserDto> users = userService.getAllUsers();
        return new ResponseEntity<>(ApiResponse.success("Users retrieved successfully", users), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponse<UserDto>> getUserById(Long id) {
        UserDto user = userService.getUserById(id);
        return new ResponseEntity<>(ApiResponse.success("User retrieved successfully", user), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponse<UserDto>> updateUser(Long id, @Valid CreateUserRequest userDto) {
        UserDto updatedUser = userService.updateUser(id, userDto);
        return new ResponseEntity<>(ApiResponse.success("User updated successfully", updatedUser), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponse<Void>> deleteUser(Long id) {
        userService.deleteUser(id);
        return new ResponseEntity<>(ApiResponse.success("User deleted successfully"), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponse<Page<UserDto>>> searchUsers(@Valid SearchUserRequest searchUserRequest) {
        log.info("Searching and filtering users with request: {}", searchUserRequest);
//        Page<UserDto> users = userService.searchUsers(searchUserRequest);
        return new ResponseEntity<>(ApiResponse.success("Users retrieved successfully", Page.empty()), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponse<Void>> acceptJoiningInvitation(Long staffId) {
        userService.acceptJoiningInvitation(staffId);
        return new ResponseEntity<>(ApiResponse.success("Invitation Accepted successfully"), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponse<Void>> rejectJoiningInvitation(Long staffId) {
        userService.rejectJoiningInvitation(staffId);
        return new ResponseEntity<>(ApiResponse.success("Invitation Rejected successfully"), HttpStatus.OK);
    }
} 