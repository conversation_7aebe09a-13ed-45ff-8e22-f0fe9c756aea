package vn.flexin.backend.mono.company.dto.staff;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.jpa.domain.Specification;
import vn.flexin.backend.mono.common.dto.BaseFilter;
import vn.flexin.backend.mono.common.repository.param.Condition;
import vn.flexin.backend.mono.common.repository.param.Join;
import vn.flexin.backend.mono.common.repository.param.Operator;
import vn.flexin.backend.mono.common.repository.param.Where;
import vn.flexin.backend.mono.common.util.Constant;
import vn.flexin.backend.mono.common.util.SpecificationUtil;
import vn.flexin.backend.mono.company.entity.Branch;
import vn.flexin.backend.mono.company.entity.Company;
import vn.flexin.backend.mono.company.entity.Staff;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
public class StaffFilter extends BaseFilter<Staff> {
    private Long branchId;
    private String keyword;

    @JsonProperty("isManager")
    private Boolean isManager;

    @JsonProperty("isPending")
    private Boolean isPending;

    private Boolean isActive;

    @Override
    public String getSortBy() {
        return StringUtils.isEmpty(sortBy) ? Staff.Fields.name : sortBy;
    }

    @Override
    public String getSortOrder() {
        return StringUtils.isEmpty(sortOrder) ? Constant.SORT_ASC : sortOrder;
    }

    @Override
    public Specification<Staff> toSpecification() {
        Condition condition = new Condition();
        if(branchId != null) {
            condition.append(new Join(Staff.Fields.branch, List.of(new Where(Staff.Fields.id, branchId))));
        }

        if (isActive != null) {
            condition.append(new Where(Staff.Fields.isActive, isActive));
        }

        if (keyword != null && !keyword.isEmpty()) {
            condition.append(new Where(Staff.Fields.name, Operator.LIKE, keyword));
        }

        if (keyword != null && !keyword.isEmpty()) {
            condition.append(new Where(Staff.Fields.name, Operator.LIKE, keyword));
        }

        if (isManager != null) {
            condition.append(new Where(Staff.Fields.isManager, isManager));
        }

        if (isPending != null) {
            condition.append(new Where(Staff.Fields.isPending, isPending));
        }

        return SpecificationUtil.bySearchQuery(condition);
    }
}
