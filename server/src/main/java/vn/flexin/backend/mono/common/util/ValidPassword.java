package vn.flexin.backend.mono.common.util;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import jakarta.validation.constraints.Pattern;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.*;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

@Pattern(regexp = ValidPassword.PATTERN,
        message = ValidPassword.INVALID_PASSWORD_MESSAGE
)
@Target({METHOD, FIELD, ANNOTATION_TYPE, PARAMETER})
@Retention(RUNTIME)
@Constraint(validatedBy = {})
@Documented
public @interface ValidPassword {
    public static int MINIMUM_PASSWORD_LENGTH = 10;

    public static java.util.regex.Pattern PASSWORD_PATTERN = java.util.regex.Pattern.compile(ValidPassword.PATTERN);
    String INVALID_PASSWORD_MESSAGE = "Please provide a valid password: 10 characters minimum, include lowercase, uppercase, digit, special character";

    String PATTERN_WITH_SPECIAL_CHARS = "^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[\\\\\"`_|.%!@#&()–\\[{}\\]:;',?/*~$^+=<>]).{10,}$";

    String PATTERN = "^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z]).{10,}$";

    String message() default "Invalid password";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}