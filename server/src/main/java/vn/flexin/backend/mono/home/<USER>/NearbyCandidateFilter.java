package vn.flexin.backend.mono.home.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.jpa.domain.Specification;
import vn.flexin.backend.mono.common.dto.BaseFilter;
import vn.flexin.backend.mono.common.repository.param.Condition;
import vn.flexin.backend.mono.common.repository.param.Operator;
import vn.flexin.backend.mono.common.repository.param.Where;
import vn.flexin.backend.mono.common.util.SpecificationUtil;
import vn.flexin.backend.mono.resume.entity.Resume;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NearbyCandidateFilter extends BaseFilter<Resume> {

    @JsonProperty("status")
    private Boolean status;

    @Override
    public Specification<Resume> toSpecification() {
        var condition = new Condition();
        if (status != null) {
            condition.append(new Where(Resume.Fields.isActive, Operator.EQUAL, status));
        }
        return SpecificationUtil.bySearchQuery(condition);
    }
}