package vn.flexin.backend.mono.common.exception;

import org.springframework.core.NestedExceptionUtils;
import org.springframework.core.NestedRuntimeException;
import org.springframework.http.HttpHeaders;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;
import vn.flexin.backend.mono.common.enums.AppStatus;

import java.util.Collections;
import java.util.Map;

@SuppressWarnings("serial")
public class ResponseAppStatusException extends NestedRuntimeException {

	private final int status;

	@Nullable
	private final String reason;

	/**
	 * Constructor with a response status.
	 * 
	 * @param status the HTTP status (required)
	 */
	public ResponseAppStatusException(AppStatus status) {
		this(status, null);
	}

	/**
	 * Constructor with a response status and a reason to add to the exception
	 * message as explanation.
	 * 
	 * @param status the HTTP status (required)
	 * @param reason the associated reason (optional)
	 */
	public ResponseAppStatusException(AppStatus status, @Nullable String reason) {
		super("");
		Assert.notNull(status, "AppStatus is required");
		this.status = status.value();
		this.reason = reason;
	}

	/**
	 * Constructor with a response status and a reason to add to the exception
	 * message as explanation, as well as a nested exception.
	 * 
	 * @param status the HTTP status (required)
	 * @param reason the associated reason (optional)
	 * @param cause  a nested exception (optional)
	 */
	public ResponseAppStatusException(AppStatus status, @Nullable String reason, @Nullable Throwable cause) {
		super(null, cause);
		Assert.notNull(status, "AppStatus is required");
		this.status = status.value();
		this.reason = reason;
	}

	/**
	 * Constructor with a response status and a reason to add to the exception
	 * message as explanation, as well as a nested exception.
	 * 
	 * @param rawStatusCode the HTTP status code value
	 * @param reason        the associated reason (optional)
	 * @param cause         a nested exception (optional)
	 * @since 5.3
	 */
	public ResponseAppStatusException(int rawStatusCode, @Nullable String reason, @Nullable Throwable cause) {
		super(null, cause);
		this.status = rawStatusCode;
		this.reason = reason;
	}

	/**
	 * Return the HTTP status associated with this exception.
	 * 
	 * @throws IllegalArgumentException in case of an unknown HTTP status code
	 * @since #getRawStatusCode()
	 * @see AppStatus#valueOf(int)
	 */
	public AppStatus getStatus() {
		return AppStatus.valueOf(this.status);
	}

	/**
	 * Return the HTTP status code (potentially non-standard and not resolvable
	 * through the {@link AppStatus} enum) as an integer.
	 * 
	 * @return the HTTP status as an integer value
	 * @since 5.3
	 * @see #getStatus()
	 * @see AppStatus#resolve(int)
	 */
	public int getRawStatusCode() {
		return this.status;
	}

	/**
	 * Return headers associated with the exception that should be added to the
	 * error response, e.g. "Allow", "Accept", etc.
	 * <p>
	 * The default implementation in this class returns an empty map.
	 * 
	 * @since 5.1.11
	 * @deprecated as of 5.1.13 in favor of {@link #getResponseHeaders()}
	 */
	@Deprecated
	public Map<String, String> getHeaders() {
		return Collections.emptyMap();
	}

	/**
	 * Return headers associated with the exception that should be added to the
	 * error response, e.g. "Allow", "Accept", etc.
	 * <p>
	 * The default implementation in this class returns empty headers.
	 * 
	 * @since 5.1.13
	 */
	public HttpHeaders getResponseHeaders() {
		Map<String, String> headers = getHeaders();
		if (headers.isEmpty()) {
			return HttpHeaders.EMPTY;
		}
		HttpHeaders result = new HttpHeaders();
		getHeaders().forEach(result::add);
		return result;
	}

	/**
	 * The reason explaining the exception (potentially {@code null} or empty).
	 */
	@Nullable
	public String getReason() {
		return this.reason;
	}

	@Override
	public String getMessage() {
		AppStatus code = AppStatus.resolve(this.status);
		String msg = (code != null ? code : this.status) + (this.reason != null ? " \"" + this.reason + "\"" : "");
		return NestedExceptionUtils.buildMessage(msg, getCause());
	}

}