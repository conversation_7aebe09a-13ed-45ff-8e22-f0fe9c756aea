package vn.flexin.backend.mono.user.dto;

import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.flexin.backend.mono.address.dto.request.AddressRequest;
import vn.flexin.backend.mono.role.dto.response.RoleResponse;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Set;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserDto {
    private Long id;

    @Size(max = 26)
    private String ulid;
    
    @Size(max = 50, message = "Name must be less than 50 characters")
    private String name;

    private String dateOfBirth;

    @Pattern(regexp = "^(male|female|other)$", message = "Gender must be either 'male', 'female', or 'other'")
    private String gender;

    @Size(max = 50, message = "Email must be less than 50 characters")
    @Email(message = "Email should be valid")
    private String email;
    
    // Admin portal role
    private String role;

    private Boolean isAdminPortal = false;

    private Set<RoleResponse> roles;

    private Set<Long> roleIds;
    
    private String phoneNumber;
    
    private String profilePicture;
    
    private boolean isActive = true;

    private LocalDateTime createdAt;

    private AddressRequest address;
} 