package vn.flexin.backend.mono.resume.repository;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import vn.flexin.backend.mono.common.repository.JpaSpecificationRepository;
import vn.flexin.backend.mono.resume.entity.Resume;
import vn.flexin.backend.mono.resume.entity.ResumeVersionMapping;

import java.util.Optional;

public interface ResumeVersionRepository extends JpaSpecificationRepository<ResumeVersionMapping, Long> {
    @Query("""
        SELECT rvm.original
        FROM ResumeVersionMapping rvm
        WHERE rvm.snapshot.id = :resumeId
    """)
    Optional<Resume> findOriginResume(Long resumeId);

    @Modifying
    @Query("""
        DELETE FROM ResumeVersionMapping rvm
        WHERE rvm.snapshot.id = :resumeId
    """)
    void deleteBySnapshotId(Long resumeId);

    @Query("""
        SELECT rvm.snapshot.id FROM ResumeVersionMapping rvm
    """)
    java.util.List<Long> findAllSnapshotResumeIds();
}
