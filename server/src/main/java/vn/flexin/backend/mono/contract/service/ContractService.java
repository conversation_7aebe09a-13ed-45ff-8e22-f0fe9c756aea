package vn.flexin.backend.mono.contract.service;

import org.springframework.data.util.Pair;
import vn.flexin.backend.mono.common.dto.PaginationResponse;
import vn.flexin.backend.mono.contract.dto.*;
import vn.flexin.backend.mono.contract.entity.Contract;

import java.util.List;

public interface ContractService {
    ContractDetailResponse createContract(ContractRequet requet);
    ContractDetailResponse getContractById(Long id);
    List<ContractDetailResponse> getAllContracts();
    List<ContractDetailResponse> getContractsByEmployerId(Long employerId);
    List<ContractDetailResponse> getContractsByFreelancerId(Long freelancerId);
    List<ContractDetailResponse> getContractsByEmployerIdAndStatus(Long employerId, String status);
    List<ContractDetailResponse> getContractsByFreelancerIdAndStatus(Long freelancerId, String status);
    Pair<List<ContractResponse>, PaginationResponse> searchContracts(ContractFilter filter);
    ContractDetailResponse updateContract(Long id, ContractRequet requet);
    ContractDetailResponse updateContractStatus(Long id, String status, String notes);
    Boolean deleteContract(Long id);
    Contract getContractEntityById(Long id);

    Pair<List<TimeEntryResponse>, PaginationResponse> searchTimeEntry(TimeEntryFilter filter);

    Pair<List<PaymentRecordResponse>, PaginationResponse> searchPaymentRecord(PaymentRecordFilter filter);

    TimeEntryResponse createTimeEntry(TimeEntryRequest request);

    TimeEntryResponse updateTimeEntryStatus(TimeEntryRequest request);

    PaymentRecordResponse createPaymentRecord(PaymentRecordRequest request);

    PaymentRecordResponse updatePaymentRecordStatus(PaymentRecordRequest request);

    Pair<List<ContractMessageResponse>, PaginationResponse> searchMessage(ContractMessageFilter filter);

    ContractMessageResponse updateMessageIsRead(ContractMessageRequest request);
}