package vn.flexin.backend.mono.jobseekerpost.mapper;

import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import vn.flexin.backend.mono.address.dto.response.SimpleAddressResponse;
import vn.flexin.backend.mono.address.service.impl.AddressServiceImpl;
import vn.flexin.backend.mono.common.util.CommonUtil;
import vn.flexin.backend.mono.jobseekerpost.dto.JobSeekerExperienceDto;
import vn.flexin.backend.mono.jobseekerpost.dto.request.CreateJobSeekerPostRequest;
import vn.flexin.backend.mono.jobseekerpost.dto.request.UpdateJobSeekerPostRequest;
import vn.flexin.backend.mono.jobseekerpost.dto.response.JobSeekerDetailResponse;
import vn.flexin.backend.mono.jobseekerpost.dto.response.SearchJobSeekerPostResponse;
import vn.flexin.backend.mono.jobseekerpost.entity.JobSeekerPost;
import vn.flexin.backend.mono.jobseekerpost.entity.JobSeekerSalary;
import vn.flexin.backend.mono.post.dto.SalaryDto;
import vn.flexin.backend.mono.post.enums.PostStatus;

import java.util.HashSet;
import java.util.List;

@Component
@AllArgsConstructor
public class JobSeekerPostMapper {

    private final AddressServiceImpl addressService;


    public JobSeekerPost toEntity(CreateJobSeekerPostRequest request) {
        if (request == null) {
            return null;
        }

        JobSeekerPost post = new JobSeekerPost();
        post.setTitle(request.getTitle());
        post.setDescription(request.getDescription());
        post.setIndustry(request.getIndustry());
        post.setJobType(request.getJobType());
        post.setContractType(request.getContractType());
        post.setWorkType(request.getWorkType());
        post.setEducationLevel(request.getEducationLevel());
        post.setEducationDetail(request.getEducationDetail());
        post.setStartTime(request.getStartTime());
        post.setEndTime(request.getEndTime());
        post.setWorkingHourPerDay(request.getWorkingHourPerDay());
        post.setWorkingDays(request.getWorkingDays());
        post.setWorkingShifts(request.getWorkingShifts());
        post.setFeatureJob(request.isFeatureJob());
        post.setFeatureDuration(request.getFeatureDuration());
        post.setEnableEmailNotification(request.isEnableEmailNotification());
        post.setEnableInformation(request.isEnableInformation());
        post.setAutoAcceptInterviewInvitation(request.isAutoAcceptInterviewInvitation());
        post.setReady(request.isReady());


        post.setLocation(addressService.updateAddress(request.getLocation()));
        post.setSalary(new JobSeekerSalary(request.getSalary(), post));
        post.setSkills(request.getSkills());
        post.setLanguages(request.getLanguages());

        return post;
    }

    public JobSeekerDetailResponse toDetailPostResponse(JobSeekerPost post) {
        if (post == null) {
            return null;
        }

        JobSeekerDetailResponse response = new JobSeekerDetailResponse();

        response.setId(post.getId());
        response.setTitle(post.getTitle());
        response.setIndustry(post.getIndustry());
        response.setDescription(post.getDescription());

        // Map User entity to UserResponse
        response.setJobSeekerId(post.getJobSeeker().getId());

        // Map JobSeekerSalary entity to JobSeekerSalaryResponse
        if (post.getSalary() != null) {
            SalaryDto salaryResponse = new SalaryDto();
            salaryResponse.setId(post.getSalary().getId());
            salaryResponse.setMin(post.getSalary().getMin());
            salaryResponse.setMax(post.getSalary().getMax());
            salaryResponse.setCurrency(post.getSalary().getCurrency());
            salaryResponse.setPeriod(post.getSalary().getPeriod());
            response.setSalary(salaryResponse);
        }

        // Map skills, languages, and other simple fields
        response.setSkills(post.getSkills());
        response.setLanguages(post.getLanguages());
        response.setEducationLevel(post.getEducationLevel());
        response.setEducationDetail(post.getEducationDetail());
        response.setStartTime(post.getStartTime());
        response.setEndTime(post.getEndTime());
        response.setWorkingHourPerDay(post.getWorkingHourPerDay());
        response.setWorkingDays(post.getWorkingDays());
        response.setWorkingShifts(post.getWorkingShifts());

        // Map JobSeekerExperience entities to JobSeekerExperienceResponse
        if (!CollectionUtils.isEmpty(post.getExperiences())) {
            List<JobSeekerExperienceDto> experienceResponses = post.getExperiences().stream()
                    .map(experience -> {
                        JobSeekerExperienceDto experienceResponse = new JobSeekerExperienceDto();
                        experienceResponse.setId(experience.getId());
                        experienceResponse.setIndustry(experience.getIndustry());
                        experienceResponse.setYearOfExperience(experience.getYearOfExperience());
                        return experienceResponse;
                    })
                    .toList();
            response.setExperiences(experienceResponses);
        }

        // Map job type, contract type, work type
        response.setJobType(post.getJobType());
        response.setContractType(post.getContractType());
        response.setWorkType(post.getWorkType());

        // Map Address entity to AddressResponse
        if (post.getLocation() != null) {
            SimpleAddressResponse addressResponse = new SimpleAddressResponse(post.getLocation());
            response.setLocation(addressResponse);
        }

        // Map boolean fields and other simple fields
        response.setFeatureJob(post.isFeatureJob());
        response.setFeatureDuration(post.getFeatureDuration());
        response.setEnableEmailNotification(post.isEnableEmailNotification());
        response.setEnableInformation(post.isEnableInformation());
        response.setAutoAcceptInterviewInvitation(post.isAutoAcceptInterviewInvitation());
        response.setReady(post.isReady());
        response.setActiveDate(post.getActiveDate());
        response.setStatus(post.getPostStatus().getValue());

        return response;
    }

    public void updateEntityFromDto(UpdateJobSeekerPostRequest request, JobSeekerPost post) {
        if (request == null || post == null) {
            return;
        }

        if (request.getTitle() != null) {
            post.setTitle(request.getTitle());
        }
        if (request.getDescription() != null) {
            post.setDescription(request.getDescription());
        }
        if (request.getIndustry() != null) {
            post.setIndustry(request.getIndustry());
        }
        if (request.getJobType() != null) {
            post.setJobType(request.getJobType());
        }
        if (request.getContractType() != null) {
            post.setContractType(request.getContractType());
        }
        if (request.getWorkType() != null) {
            post.setWorkType(request.getWorkType());
        }
        if (request.getLocation() != null) {
            post.setLocation(addressService.updateAddress(request.getLocation()));
        }
        if (request.getSalary() != null) {
            post.setSalary(toSalary(request.getSalary(), post.getSalary(), post));
        }
        if (request.getSkills() != null) {
            post.setSkills(new HashSet<>(request.getSkills()));
        }
        if (request.getLanguages() != null) {
            post.setLanguages(request.getLanguages());
        }
        PostStatus oldStatus = post.getPostStatus();
        PostStatus newStatus = PostStatus.fromString(request.getStatus());
        if (oldStatus == PostStatus.DRAFT && newStatus == PostStatus.ACTIVE) {
            post.setActiveDate(CommonUtil.getCurrentUTCTime());
        }
    }

    private JobSeekerSalary toSalary(SalaryDto dto, JobSeekerSalary entity, JobSeekerPost post) {
        if (entity == null) {
            return new JobSeekerSalary(dto, post);
        }
        entity.setMax(dto.getMax());
        entity.setMin(dto.getMin());
        entity.setCurrency(dto.getCurrency());
        entity.setPeriod(dto.getPeriod());
        return entity;
    }


    public SearchJobSeekerPostResponse toSearchJobSeekerPostResponse(JobSeekerPost entity) {
        SearchJobSeekerPostResponse response = new SearchJobSeekerPostResponse();
        if (entity == null) {
            return response;
        }
        response.setId(entity.getId());
        if (entity.getJobSeeker() != null) {
            response.setJobSeekerId(entity.getJobSeeker().getId());
        }
        response.setTitle(entity.getTitle());
        response.setDescription(entity.getDescription());
        response.setSkills(entity.getSkills() != null ? new HashSet<>(entity.getSkills()) : new HashSet<>());
        response.setWorkingDays(entity.getWorkingDays() != null ? new HashSet<>(entity.getWorkingDays()) : new HashSet<>());
        response.setWorkingShifts(entity.getWorkingShifts() != null ? new HashSet<>(entity.getWorkingShifts()) : new HashSet<>());
        if (entity.getLocation() != null) {
            response.setLocation(new SimpleAddressResponse(entity.getLocation()));
        }

        response.setOpportunityCount(0);
        response.setNewOpportunityCount(0);
        response.setViewCount(entity.getPostViews().size());
        response.setInterviewRequestCount(entity.getInterviewRequests().size());
        // Feature Job
        response.setFeatureJob(entity.isFeatureJob());
        response.setFeatureDuration(entity.getFeatureDuration());

        response.setStatus(entity.getPostStatus().getValue());

        return response;
    }

}