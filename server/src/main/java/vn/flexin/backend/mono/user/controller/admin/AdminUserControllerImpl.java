package vn.flexin.backend.mono.user.controller.admin;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import vn.flexin.backend.mono.common.dto.ApiResponse;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.common.dto.PaginationApiResponseDto;
import vn.flexin.backend.mono.common.util.ModelMapperUtils;
import vn.flexin.backend.mono.user.dto.CreateUserRequest;
import vn.flexin.backend.mono.user.dto.UserDto;
import vn.flexin.backend.mono.user.dto.UserFilter;
import vn.flexin.backend.mono.user.service.UserService;

import jakarta.validation.Valid;
import java.util.List;

@RestController
@RequiredArgsConstructor
@Slf4j
public class AdminUserControllerImpl implements AdminUserController {

    private final UserService userService;

    @Override
    public ResponseEntity<ApiResponse<List<UserDto>>> getAllUsers() {
        log.info("Admin retrieving all users");
        List<UserDto> users = userService.getAllUsers();
        return new ResponseEntity<>(ApiResponse.success("Users retrieved successfully", users), HttpStatus.OK);
    }
    
    @Override
    public ResponseEntity<PaginationApiResponseDto<List<UserDto>>> searchUsers(@Valid UserFilter searchUserRequest) {
        log.info("Admin searching and filtering users with request: {}", searchUserRequest);
        var users = userService.searchUsers(searchUserRequest);
        return new ResponseEntity<>(PaginationApiResponseDto.success("Users retrieved successfully", users.getLeft(), users.getRight()), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponse<UserDto>> getUserById(Long id) {
        log.info("Admin retrieving user with ID: {}", id);
        UserDto user = userService.getUserById(id);
        return new ResponseEntity<>(ApiResponse.success("User retrieved successfully", user), HttpStatus.OK);
    }
    
    @Override
    public ResponseEntity<ApiResponse<UserDto>> createUser(@Valid CreateUserRequest userDto) {
        log.info("Admin creating new user: {}", userDto);
        UserDto createdUser = userService.createUser(userDto);
        return new ResponseEntity<>(ApiResponse.success("User created successfully", createdUser), HttpStatus.CREATED);
    }

    @Override
    public ResponseEntity<ApiResponse<UserDto>> updateUser(Long id, @Valid UserDto userDto) {
        log.info("Admin updating user with ID: {}", id);
        UserDto updatedUser = userService.updateUserByAdmin(id, userDto);
        return new ResponseEntity<>(ApiResponse.success("User updated successfully", updatedUser), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponse<Void>> deleteUser(Long id) {
        log.info("Admin deleting user with ID: {}", id);
        userService.deleteUser(id);
        return new ResponseEntity<>(ApiResponse.success("User deleted successfully"), HttpStatus.OK);
    }
    
    @Override
    public ResponseEntity<ApiResponse<UserDto>> changeUserStatus(Long id, boolean active) {
        log.info("Admin changing status for user ID: {} to active: {}", id, active);
        UserDto user = userService.getUserById(id);
        user.setActive(active);
        UserDto updatedUser = userService.updateUserByAdmin(id, user);
        String message = active ? "User activated successfully" : "User deactivated successfully";
        return new ResponseEntity<>(ApiResponse.success(message, updatedUser), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> assignRoleToUser(Long userId, List<Long> roleIds) {
        userService.assignRoleToUser(userId, roleIds);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE,"Assign role successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> removeRoleOfUser(Long userId, Long roleId) {
        userService.removeRoleOfUser(userId, roleId);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE,"Remove role successfully"));
    }
} 