package vn.flexin.backend.mono.common.util;

import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.keycloak.KeycloakPrincipal;
import org.keycloak.adapters.springsecurity.token.KeycloakAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import vn.flexin.backend.mono.auth.util.AuthConstant;
import vn.flexin.backend.mono.common.exception.message.ErrorMessage;
import vn.flexin.backend.mono.common.exception.BadRequestException;

import java.util.Date;
import java.util.Map;

@Slf4j
public class SecurityUtil {

    // Function to get current user info from Keycloak token
    public static String getCurrentUserUlid() {
        Authentication authentication = getAuthentication();
        if (authentication instanceof KeycloakAuthenticationToken token) {
            KeycloakPrincipal<?> principal = (KeycloakPrincipal<?>) token.getPrincipal();

            return principal.getName();
        }
        return null;
    }

    private static Authentication getAuthentication() {
        return SecurityContextHolder.getContext().getAuthentication();
    }

    public static String generateTempToken(String subject, Map<String, Object> claims, String secretKey) {
        try {
            Date now = new Date();
            Date expiryDate = new Date(now.getTime() + AuthConstant.TIME_15_MINUTES_IN_MILLISECOND);

            return Jwts.builder()
                    .setSubject(subject)
                    .addClaims(claims)
                    .setIssuedAt(now)
                    .setExpiration(expiryDate)
                    .signWith(Keys.hmacShaKeyFor(secretKey.getBytes()))
                    .compact();
        } catch (Exception e) {
            throw new BadRequestException(ErrorMessage.CAN_NOT_GENERATE_TEMP_TOKEN);
        }
    }

}
