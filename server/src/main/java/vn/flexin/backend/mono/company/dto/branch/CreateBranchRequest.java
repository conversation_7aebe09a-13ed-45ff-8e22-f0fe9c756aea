package vn.flexin.backend.mono.company.dto.branch;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import vn.flexin.backend.mono.address.dto.request.AddressRequest;
import vn.flexin.backend.mono.file.dto.FileDto;

import java.util.List;
import java.util.Map;

@Data
public class CreateBranchRequest {
    @Hidden
    private Long id;
    @NotBlank(message = "Branch name can not be blank")
    private String name;
    private AddressRequest address;
    @JsonProperty("isDefault")
    private boolean isDefault;
    private String phoneNumber;
    private List<FileDto> galleryImages;
    private Long managerId;
    @NotNull(message = "Company can not be null")
    private Long companyId;
    private Map<String, Object> additionalData;
}
