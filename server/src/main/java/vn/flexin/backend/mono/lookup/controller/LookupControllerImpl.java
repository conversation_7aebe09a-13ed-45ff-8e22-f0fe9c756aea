package vn.flexin.backend.mono.lookup.controller;

import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.common.dto.PaginationApiResponseDto;
import vn.flexin.backend.mono.lookup.dto.*;
import vn.flexin.backend.mono.lookup.service.LookupService;

import java.util.List;

@RestController
@AllArgsConstructor
public class LookupControllerImpl implements LookupController{

    private final LookupService lookupService;
    @Override
    public ResponseEntity<ApiResponseDto<List<LookupResponse>>> getAllLookups(String type, String dependValue) {
        return ResponseEntity.ok(ApiResponseDto.success(lookupService.getLookupsByType(type, dependValue), "Lookup retrieved successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<List<LookupResponse>>> getAllLookupByUsers(String type, Long userId) {
        return ResponseEntity.ok(ApiResponseDto.success(lookupService.getLookupsByType(type, userId), "Lookup retrieved successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<List<String>>> getAllLookupsType() {
        return ResponseEntity.ok(ApiResponseDto.success(lookupService.getAllTypes(), "Lookup types retrieved successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<LookupResponse>> createUserLookup(CreateUserLookupRequest request) {
        return ResponseEntity.ok(ApiResponseDto.success(
                lookupService.createUserLookup(request),
                "Lookup created successfully"
        ));
    }

    @Override
    public ResponseEntity<ApiResponseDto<LookupResponse>> createAdminLookup(CreateAdminLookupRequest request) {
        return ResponseEntity.ok(ApiResponseDto.success(
                lookupService.createAdminLookup(request),
                "Lookup created successfully"
        ));
    }

    @Override
    public ResponseEntity<PaginationApiResponseDto<List<LookupResponse>>> getLookupsForReview(LookupFilter filters) {
        var result = lookupService.getLookupsForReview(filters);
        return ResponseEntity.ok(PaginationApiResponseDto.success(
                "Lookups pending review retrieved successfully",
                result.getFirst(),
                result.getSecond()
        ));
    }

    @Override
    public ResponseEntity<ApiResponseDto<LookupResponse>> updateLookup(Long id, UpdateLookupRequest request) {
        return ResponseEntity.ok(ApiResponseDto.success(
                lookupService.updateLookup(id, request),
                "Lookup updated successfully"
        ));
    }

}
