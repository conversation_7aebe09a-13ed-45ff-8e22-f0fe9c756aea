package vn.flexin.backend.mono.notification.entity;

import jakarta.persistence.*;
import lombok.*;
import org.springframework.util.CollectionUtils;
import vn.flexin.backend.mono.common.entity.AbstractAuditingEntity;

import java.util.List;


@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "t_sms_transactions")
public class SmsTransaction extends AbstractAuditingEntity<Long> {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String tranId;

    @Column(name = "total_sms")
    private Integer totalSMS;

    private Float totalPrice;

    private String status;

    private String code;

    private String message;

    @ToString.Exclude
    @ManyToMany(fetch = FetchType.LAZY, cascade = {CascadeType.DETACH, CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH})
    @JoinTable(
            name = "t_sms_transaction_logs",
            joinColumns = @JoinColumn(name = "transaction_id"),
            inverseJoinColumns = @JoinColumn(name = "sms_id")
    )
    private List<SmsTransactionLog> logs;

    public SmsTransaction(SpeedSmsResponseData responseData) {
        this.status = responseData.getStatus();
        this.code = responseData.getCode();
        this.message = responseData.getMessage();
        this.totalPrice = responseData.getCost();
        this.totalSMS = responseData.getSms();
        this.tranId = responseData.getTranId();
    }
}
