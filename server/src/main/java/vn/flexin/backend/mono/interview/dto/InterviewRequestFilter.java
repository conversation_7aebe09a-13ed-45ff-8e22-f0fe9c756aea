package vn.flexin.backend.mono.interview.dto;

import lombok.Data;
import org.springframework.data.jpa.domain.Specification;
import vn.flexin.backend.mono.common.dto.BaseFilter;
import vn.flexin.backend.mono.common.repository.param.Condition;
import vn.flexin.backend.mono.common.repository.param.Join;
import vn.flexin.backend.mono.common.repository.param.Operator;
import vn.flexin.backend.mono.common.repository.param.Where;
import vn.flexin.backend.mono.common.util.Constant;
import vn.flexin.backend.mono.common.util.SpecificationUtil;
import vn.flexin.backend.mono.common.util.StringUtils;
import vn.flexin.backend.mono.interview.entity.InterviewRequest;
import vn.flexin.backend.mono.interview.enums.InterviewRequestStatus;
import vn.flexin.backend.mono.interview.enums.PostType;
import vn.flexin.backend.mono.user.entity.User;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class InterviewRequestFilter extends BaseFilter<InterviewRequest> {

    private Long jobSeekerId;
    private Long employerId;
    private InterviewRequestStatus status;
    private PostType postType;
    private LocalDateTime requestDateFrom;
    private LocalDateTime requestDateTo;
    private LocalDateTime expiredDate;

    @Override
    public String getSortBy() {
        return StringUtils.isEmpty(sortBy) ? InterviewRequest.Fields.id : sortBy;
    }

    @Override
    public String getSortOrder() {
        return StringUtils.isEmpty(sortOrder) ? Constant.SORT_ASC : sortOrder;
    }

    @Override
    public Specification<InterviewRequest> toSpecification() {
        var condition = new Condition();

        if (employerId != null) {
            condition.append(new Join(InterviewRequest.Fields.requestUser, List.of(new Where(User.Fields.id, employerId))));
        }

        if (jobSeekerId != null) {
            condition.append(new Join(InterviewRequest.Fields.receiveUser, List.of(new Where(User.Fields.id, jobSeekerId))));
        }

        if (postType != null) {
            condition.append(new Where(InterviewRequest.Fields.postType, postType));
        }

        if (status != null) {
            condition.append(new Where(InterviewRequest.Fields.status, status));
        }

        if (requestDateFrom != null) {
            condition.append(new Where(CREATED_AT, Operator.GREATER_THAN_OR_EQUAL, requestDateFrom));
        }

        if (requestDateTo != null) {
            condition.append(new Where(CREATED_AT, Operator.LESS_THAN_OR_EQUAL, requestDateTo));
        }

        if (expiredDate != null) {
            condition.append(new Where(CREATED_AT, Operator.LESS_THAN_OR_EQUAL, expiredDate));
        }

        return SpecificationUtil.bySearchQuery(condition);
    }
}
