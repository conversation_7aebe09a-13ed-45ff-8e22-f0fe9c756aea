package vn.flexin.backend.mono.application.repository;

import vn.flexin.backend.mono.job.entity.Job;
import vn.flexin.backend.mono.application.entity.JobApplication;
import vn.flexin.backend.mono.resume.entity.Resume;
import vn.flexin.backend.mono.user.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface JobApplicationRepository extends JpaRepository<JobApplication, Long> {
    List<JobApplication> findByJob(Job job);
    List<JobApplication> findByJobSeeker(User jobSeeker);
    List<JobApplication> findByResume(Resume resume);
    List<JobApplication> findByJobAndStatus(Job job, String status);
    List<JobApplication> findByJobSeekerAndStatus(User jobSeeker, String status);
    Optional<JobApplication> findByJobAndJobSeeker(Job job, User jobSeeker);
} 