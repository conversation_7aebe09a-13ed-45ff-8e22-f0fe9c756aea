package vn.flexin.backend.mono.company.dto.staff;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StaffResponse {
    private String id;
    private String name;
    private String photoUrl;
    private String position;
    private String branchName;
    private Long branchId;
    private boolean isManager;
    private boolean isActive;
    private String joinDate;
    private boolean isPending;
}
