package vn.flexin.backend.mono.resume.dto;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.jpa.domain.Specification;
import vn.flexin.backend.mono.common.dto.BaseFilter;
import vn.flexin.backend.mono.common.repository.param.Condition;
import vn.flexin.backend.mono.common.repository.param.Join;
import vn.flexin.backend.mono.common.repository.param.Operator;
import vn.flexin.backend.mono.common.repository.param.Where;
import vn.flexin.backend.mono.common.util.SpecificationUtil;
import vn.flexin.backend.mono.resume.entity.Resume;
import vn.flexin.backend.mono.user.dto.FilterOperator;
import vn.flexin.backend.mono.user.entity.User;

import java.util.List;
import java.util.Set;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldNameConstants
public class ResumeFilter extends BaseFilter<Resume> {

    private Long userId;

    private Set<String> skills;

    private String keyword;

    @Override
    public Specification<Resume> toSpecification() {
        var condition = new Condition();
        if (userId != null) {
            condition.append(new Join(Resume.Fields.user, List.of(new Where(User.Fields.id, userId))));
        }
        if (keyword != null && !keyword.isEmpty()) {
            condition.append(new Where(Resume.Fields.name, Operator.LIKE, keyword));
        }
        return SpecificationUtil.bySearchQuery(condition);
    }
}