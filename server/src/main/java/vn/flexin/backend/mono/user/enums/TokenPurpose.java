package vn.flexin.backend.mono.user.enums;

public enum TokenPurpose {

    REGISTRATION("registration"),
    CREATE_PASSWORD("create_password"),
    SELECT_ROLE ("select_role"),
    RESET_PASSWORD("reset_password");

    private String purpose;

    TokenPurpose(String purpose) {
        this.purpose = purpose;
    }

    public String getPurpose() {
        return this.purpose;
    }

    public static TokenPurpose fromString(String text) {
        for (TokenPurpose purpose : TokenPurpose.values()) {
            if (purpose.purpose.equalsIgnoreCase(text)) {
                return purpose;
            }
        }
        throw new IllegalArgumentException("No constant with text " + text + " found");
    }
}
