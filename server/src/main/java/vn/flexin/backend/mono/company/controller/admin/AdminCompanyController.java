package vn.flexin.backend.mono.company.controller.admin;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.common.dto.CreateObjectResponse;
import vn.flexin.backend.mono.common.dto.PaginationApiResponseDto;
import vn.flexin.backend.mono.company.dto.branch.BranchFilter;
import vn.flexin.backend.mono.company.dto.company.*;
import vn.flexin.backend.mono.company.dto.staff.StaffFilter;

import java.util.List;

@Tag(name = "Admin Companies APIs", description = "Admin Company Management")
@RequestMapping("/v1/admin/companies")
public interface AdminCompanyController {
    @Operation(summary = "Search list companies of admin", description = "Retrieves a paginated list of companies with filtering options. " +
            "filtering options.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Admin company fetched successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "400", description = "Invalid input")
    })
    @PostMapping("/search")
    @PreAuthorize("hasAuthority('company_read')")
    ResponseEntity<PaginationApiResponseDto<List<AdminCompanyResponse>>> searchCompanyForAdmin(@Valid @RequestBody CompanyFilter filters);

    @Operation(summary = "Get detail company", description = "Get detail company")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Admin company fetched successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "404", description = "Company not found"),
    })
    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority('company_read')")
    ResponseEntity<ApiResponseDto<AdminCompanyResponse>> getDetailCompany(@PathVariable("id") Long id);

    @Operation(summary = "Creates a new job company", description = "Creates a new job company")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Company created successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "400", description = "Invalid company data")
    })
    @PostMapping
    @PreAuthorize("hasAuthority('company_create')")
    ResponseEntity<ApiResponseDto<AdminCompanyResponse>> createCompany(@Valid @RequestBody CreateAdminCompanyRequest request);

    @Operation(summary = "Update company", description = "Update company")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Company updated successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "404", description = "Company not found"),
    })
    @PutMapping("/{id}")
    @PreAuthorize("hasAuthority('company_update')")
    ResponseEntity<ApiResponseDto<AdminCompanyResponse>> updateCompany(@PathVariable("id") Long id,
                                                                       @Valid @RequestBody UpdateAdminCompanyRequest request);

    @Operation(summary = "Delete company", description = "Delete company")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Company Delete successfully"),
            @ApiResponse(responseCode = "404", description = "Company not found"),
    })
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('company_delete')")
    ResponseEntity<ApiResponseDto<Boolean>> deleteCompany(@PathVariable("id") Long id);

    @Operation(summary = "Verifies company", description = "Verifies a company profile.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Company verified successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "404", description = "Company not found"),
    })
    @PatchMapping("/{id}/verify")
    @PreAuthorize("hasAuthority('company_verify')")
    ResponseEntity<ApiResponseDto<AdminCompanyResponse>> verifiesCompany(@PathVariable("id") Long id);

    @Operation(summary = "Search list branches of admin", description = "Retrieves a paginated list of branches for a specific company.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Admin branches fetched successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "400", description = "Invalid input")
    })
    @PostMapping("/{companyId}/branches/search")
    @PreAuthorize("hasAuthority('company_read')")
    ResponseEntity<PaginationApiResponseDto<List<SearchAdminBranchResponse>>> searchBranchesForAdmin(@PathVariable("companyId") Long companyId,
                                                                                                     @Valid @RequestBody BranchFilter filters);

    @Operation(summary = "Retrieves detailed information for a specific branch.", description = "Retrieves detailed information for a specific branch.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Admin branch fetched successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "404", description = "Branch not found"),
    })
    @GetMapping("/{companyId}/branches/{branchId}")
    @PreAuthorize("hasAuthority('company_read')")
    ResponseEntity<ApiResponseDto<SearchAdminBranchResponse>> getDetailBranch(@PathVariable("companyId") Long companyId, @PathVariable("branchId") Long branchId);

    @Operation(summary = "Creates a new branch for a company.", description = "Creates a new branch for a company.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Branch created successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "400", description = "Invalid branch data")
    })
    @PostMapping("/{companyId}/branches")
    @PreAuthorize("hasAuthority('company_create_branch')")
    ResponseEntity<ApiResponseDto<SearchAdminBranchResponse>> createBranch(@PathVariable("companyId") Long companyId,
                                                                           @Valid  @RequestBody CreateAdminBranchRequest request);

    @Operation(summary = "Updates an existing branch.", description = "Updates an existing branch.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Branch updated successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "404", description = "Branch not found"),
    })
    @PutMapping("/{companyId}/branches/{branchId}")
    @PreAuthorize("hasAuthority('company_update_branch')")
    ResponseEntity<ApiResponseDto<SearchAdminBranchResponse>> updateBranch(@PathVariable("companyId") Long companyId,
                                                                         @PathVariable("branchId") Long branchId,
                                                                         @Valid @RequestBody UpdateAdminBranchRequest request);

    @Operation(summary = "Delete branch", description = "Delete branch")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Branch Delete successfully"),
            @ApiResponse(responseCode = "404", description = "Branch not found"),
    })
    @DeleteMapping("/{companyId}/branches/{branchId}")
    @PreAuthorize("hasAuthority('company_delete_branch')")
    ResponseEntity<ApiResponseDto<Boolean>> deleteBranch(@PathVariable("companyId") Long companyId,
                                                         @PathVariable("branchId") Long branchId);

    @Operation(summary = "Sets a branch as a default", description = "Sets a branch as the default branch for a company")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Branch set as default successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "404", description = "Branch not found"),
    })
    @PatchMapping("/{companyId}/branches/{branchId}/set-default")
    @PreAuthorize("hasAuthority('company_update_branch')")
    ResponseEntity<ApiResponseDto<SearchAdminBranchResponse>> setDefaultBranch(@PathVariable("companyId") Long companyId,
                                                                            @PathVariable("branchId") Long branchId);

    @Operation(summary = "Search list staffs of admin", description = "Retrieves a paginated list of Staff for a specific company..")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Admin staffs fetched successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "400", description = "Invalid input")
    })
    @PostMapping("/{companyId}/staffs/search")
    @PreAuthorize("hasAuthority('company_read')")
    ResponseEntity<PaginationApiResponseDto<List<SearchAdminStaffResponse>>> searchStaffsForAdmin(@PathVariable(
            "companyId") Long companyId, @Valid @RequestBody StaffFilter filters);

    @Operation(summary = "Retrieves detailed information for a specific branch.", description = "Retrieves detailed information for a specific branch.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Admin team-member fetched successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "404", description = "team-member not found"),
    })
    @GetMapping("/{companyId}/staffs/{memberId}")
    @PreAuthorize("hasAuthority('company_read')")
    ResponseEntity<ApiResponseDto<SearchAdminStaffResponse>> getDetailStaff(@PathVariable("companyId") Long companyId, @PathVariable("memberId") Long memberId);

    @Operation(summary = "Invites a new team member.", description = "Invites a new team member to join the company..")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Team member created successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "400", description = "Invalid Team member data")
    })
    @PostMapping("/{companyId}/staffs/invite")
    @PreAuthorize("hasAuthority('company_invite_staff')")
    ResponseEntity<ApiResponseDto<CreateObjectResponse>> createStaff(@PathVariable("companyId") Long companyId,
                                                                     @Valid @RequestBody CreateAdminStaffRequest request);

    @Operation(summary = "Updates an existing team member.", description = "Updates an existing team member.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Team Member updated successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "404", description = "Team Member not found"),
    })
    @PutMapping("/{companyId}/staffs/{memberId}")
    @PreAuthorize("hasAuthority('company_update_staff')")
    ResponseEntity<ApiResponseDto<Boolean>> updateStaff(@PathVariable("companyId") Long companyId,
                                                        @PathVariable("memberId") Long memberId,
                                                        @Valid @RequestBody UpdateAdminStaffRequest request);

    @Operation(summary = "Removes a team member", description = "Removes a team member from the company")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Team member removed successfully"),
            @ApiResponse(responseCode = "404", description = "Team member not found"),
    })
    @DeleteMapping("/{companyId}/staffs/{memberId}")
    @PreAuthorize("hasAuthority('company_remove_staff')")
    ResponseEntity<ApiResponseDto<Boolean>> deleteTeamMember(@PathVariable("companyId") Long companyId,
                                                            @PathVariable("memberId") Long memberId);

    @Operation(summary = "Updates a team member's manager status.", description = "Updates a team member's manager status.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Manager status updated successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "404", description = "Team member not found")
    })
    @PatchMapping("/{companyId}/staffs/{memberId}/manager-status")
    @PreAuthorize("hasAuthority('company_update_staff')")
    ResponseEntity<ApiResponseDto<Boolean>> updateTeamMemberManagerStatus(
            @PathVariable("companyId") Long companyId,
            @PathVariable("memberId") Long memberId,
            @Valid @RequestBody UpdateManagerStatusRequest request);
}
