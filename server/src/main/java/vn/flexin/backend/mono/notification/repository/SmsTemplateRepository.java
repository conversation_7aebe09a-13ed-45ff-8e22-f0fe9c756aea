package vn.flexin.backend.mono.notification.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import vn.flexin.backend.mono.notification.entity.SmsTemplate;
import vn.flexin.backend.mono.notification.enums.SmsType;

public interface SmsTemplateRepository extends JpaRepository<SmsTemplate, Long> {

    @Query("SELECT template from SmsTemplate template WHERE template.type = :type")
    SmsTemplate findByType(SmsType type);
}
