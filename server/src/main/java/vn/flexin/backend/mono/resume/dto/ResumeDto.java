package vn.flexin.backend.mono.resume.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.flexin.backend.mono.user.dto.BasicUserInfoResponse;
import vn.flexin.backend.mono.user.dto.ContactInfoDto;
import vn.flexin.backend.mono.user.dto.PartTimePreferenceDto;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Data
@NoArgsConstructor
public class ResumeDto {
    private Long id;
    
    @NotBlank(message = "Resume name is required")
    private String name;
    
    private String description;
    
    private Long userId;

    private String ulid;

    @JsonProperty("isActive")
    private boolean isActive = true;

    private LocalDateTime lastUpdateAt;
    
    private Set<String> skills = new HashSet<>();
    
    @Valid
    private List<WorkExperienceDto> workExperiences = new ArrayList<>();
    
    @Valid
    private List<EducationDto> educations = new ArrayList<>();
    
    @Valid
    private ContactInfoDto contactInfo;
    
    @Valid
    private PartTimePreferenceDto partTimePreference;

    @Valid
    private List<LanguageDto> languages;
} 