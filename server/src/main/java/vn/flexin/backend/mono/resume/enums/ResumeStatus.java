package vn.flexin.backend.mono.resume.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import vn.flexin.backend.mono.common.enums.AppStatus;
import vn.flexin.backend.mono.common.exception.ResponseAppStatusException;

import java.util.Arrays;

public enum ResumeStatus {
    DRAFT("draft"),
    ACTIVE("active"),
    DE_ACTIVE("de_active");

    private final String value;

    ResumeStatus(String value) {
        this.value = value;
    }

    public String value() {
        return value;
    }

    public static String getValues() {
        return String.join(", ", Arrays.stream(values()).map(x -> x.name().toLowerCase()).toList());
    }

    @JsonCreator
    public static ResumeStatus fromString(String value) {
        try {
            return valueOf(value.toUpperCase());
        } catch (Exception ex) {
            throw new ResponseAppStatusException(AppStatus.BAD_REQUEST, "Resume Status must be any of [" + getValues() + "]");
        }
    }
}
