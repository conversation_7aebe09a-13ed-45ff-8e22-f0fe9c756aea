package vn.flexin.backend.mono.job.repository.impl;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;
import vn.flexin.backend.mono.job.dto.JobFilter;
import vn.flexin.backend.mono.job.dto.JobSortField;
import vn.flexin.backend.mono.job.entity.Job;
import vn.flexin.backend.mono.job.repository.JobRepositoryCustom;
import vn.flexin.backend.mono.user.dto.FilterOperator;

import java.util.ArrayList;
import java.util.List;

@Repository
public class JobRepositoryCustomImpl implements JobRepositoryCustom {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public Page<Job> searchJobs(String keyword, List<JobFilter> filters, Pageable pageable, JobSortField sortBy, Boole<PERSON> ascending) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Job> query = cb.createQuery(Job.class);
        Root<Job> root = query.from(Job.class);

        // Create predicates for search
        List<Predicate> predicates = new ArrayList<>();

        // Add keyword search if provided
        if (keyword != null && !keyword.trim().isEmpty()) {
            String likePattern = "%" + keyword.toLowerCase() + "%";
            predicates.add(
                cb.or(
                    cb.like(cb.lower(root.get("title")), likePattern),
                    cb.like(cb.lower(root.get("description")), likePattern),
                    cb.like(cb.lower(root.get("location")), likePattern)
                )
            );
        }

        // Add filters if provided
        if (filters != null && !filters.isEmpty()) {
            for (JobFilter filter : filters) {
                String fieldPath = filter.getField().fieldName();
                List<String> values = filter.getValues();
                FilterOperator operator = filter.getOperator();

                if (values != null && !values.isEmpty()) {
                    Path<Object> path = getPath(root, fieldPath);
                    
                    // For FilterOperator.AND, all values must match
                    // For FilterOperator.OR, any value can match (default)
                    if (operator == FilterOperator.AND) {
                        List<Predicate> andPredicates = new ArrayList<>();
                        for (String value : values) {
                            andPredicates.add(cb.equal(path, value));
                        }
                        predicates.add(cb.and(andPredicates.toArray(new Predicate[0])));
                    } else {
                        predicates.add(path.in(values));
                    }
                }
            }
        }

        // Apply predicates to query
        if (!predicates.isEmpty()) {
            query.where(cb.and(predicates.toArray(new Predicate[0])));
        }

        // Apply sorting
        if (sortBy != null) {
            String sortField = sortBy.getFieldName();
            Path<Object> path = getPath(root, sortField);
            
            if (ascending != null && ascending) {
                query.orderBy(cb.asc(path));
            } else {
                query.orderBy(cb.desc(path));
            }
        } else {
            // Default sort by id descending
            query.orderBy(cb.desc(root.get("id")));
        }

        // Execute query with pagination
        TypedQuery<Job> typedQuery = entityManager.createQuery(query);
        typedQuery.setFirstResult((int) pageable.getOffset());
        typedQuery.setMaxResults(pageable.getPageSize());
        List<Job> jobs = typedQuery.getResultList();

        // Count total results
        CriteriaQuery<Long> countQuery = cb.createQuery(Long.class);
        Root<Job> countRoot = countQuery.from(Job.class);
        countQuery.select(cb.count(countRoot));
        
        if (!predicates.isEmpty()) {
            countQuery.where(cb.and(predicates.toArray(new Predicate[0])));
        }
        
        Long total = entityManager.createQuery(countQuery).getSingleResult();

        return new PageImpl<>(jobs, pageable, total);
    }

    private Path<Object> getPath(Root<Job> root, String fieldPath) {
        String[] parts = fieldPath.split("\\.");
        Path<Object> path = root.get(parts[0]);
        
        for (int i = 1; i < parts.length; i++) {
            path = path.get(parts[i]);
        }
        
        return path;
    }
} 