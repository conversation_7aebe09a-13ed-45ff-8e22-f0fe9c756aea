package vn.flexin.backend.mono.jobseekerpost.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class JobSeekerStatisticPostResponse {
    private Integer viewCount;
    private Integer interviewRequestCount;
    private Integer jobOpportunityCount;
    private Map<String, Integer> lastSevenDaysViewCount;
}
