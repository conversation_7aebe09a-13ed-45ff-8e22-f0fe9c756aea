package vn.flexin.backend.mono.permission.controller.impl;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.common.dto.PaginationApiResponseDto;
import vn.flexin.backend.mono.permission.controller.AdminPermissionController;
import vn.flexin.backend.mono.permission.dto.PermissionFilter;
import vn.flexin.backend.mono.permission.dto.response.PermissionResponse;
import vn.flexin.backend.mono.permission.service.PermissionService;

import java.util.List;

@RestController
@Slf4j
@AllArgsConstructor
public class AdminPermissionControllerImpl implements AdminPermissionController {

    private final PermissionService adminPermissionService;

    @Override
    public ResponseEntity<PaginationApiResponseDto<List<PermissionResponse>>> searchPermissions(PermissionFilter permissionFilter) {
        var responses = adminPermissionService.searchPermissions(permissionFilter);
        return ResponseEntity.ok(PaginationApiResponseDto.success("Successfully get list permissions", responses.getLeft(), responses.getRight()));
    }

    @Override
    public ResponseEntity<ApiResponseDto<PermissionResponse>> getPermissionById(Long id) {
        return ResponseEntity.ok(ApiResponseDto.success(adminPermissionService.getPermissionDetail(id), "Get permission successfully"));
    }
}
