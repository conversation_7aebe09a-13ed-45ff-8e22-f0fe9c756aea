package vn.flexin.backend.mono.common.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.Collections;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Slf4j
public class ObjectMapperUtils {
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    public static String getValue(String key, String source) {
        try {
            JsonNode node = OBJECT_MAPPER.readTree(source);
            return node.path(key).asText();
        } catch (Exception ex) {
            log.error(String.format("Can not get value of %s in source %s", key, source));
            return null;
        }
    }

    public static Map<String, Object> toMap(String data) {
        try {
            return OBJECT_MAPPER.readValue(data, new TypeReference<>() {});
        } catch (JsonProcessingException e) {
            return Collections.emptyMap();
        }
    }
}
