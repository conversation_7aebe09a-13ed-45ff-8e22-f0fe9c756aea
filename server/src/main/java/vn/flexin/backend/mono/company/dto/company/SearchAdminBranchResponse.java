package vn.flexin.backend.mono.company.dto.company;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.flexin.backend.mono.address.dto.response.AddressResponse;
import vn.flexin.backend.mono.file.dto.FileDto;
import vn.flexin.backend.mono.user.dto.BasicUserInfoResponse;

import java.util.List;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = false)

public class SearchAdminBranchResponse {
    private Long id;
    private String name;
    private AddressResponse address;
    private int staffCount;
    private int activeJobPostings;
    @JsonProperty("isDefault")
    private boolean isDefault;
    @JsonProperty("isActive")
    private boolean isActive;
    private BasicUserInfoResponse manager;
    private String phoneNumber;
    private List<FileDto> galleryImages;
    private Map<String, Object> additionalData;

}
