package vn.flexin.backend.mono.notification.dto.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.flexin.backend.mono.notification.enums.TransactionStatus;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SmsWebHookRequest {
    String channel;
    String tranId;
    Integer status;
    LocalDateTime deliveryTime;
    String phone;

    public String getStatusValue() {
        switch (this.status) {
            case 0 -> {
                return TransactionStatus.SENT.value();
            }
            case 1 -> {
                return TransactionStatus.SUCCESS.value();
            }
            default -> {
                return TransactionStatus.FAILED.value();
            }
        }
    }
}
