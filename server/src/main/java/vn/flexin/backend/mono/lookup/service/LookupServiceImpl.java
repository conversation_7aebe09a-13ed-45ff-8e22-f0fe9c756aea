package vn.flexin.backend.mono.lookup.service;

import lombok.AllArgsConstructor;
import org.keycloak.representations.idm.UserRepresentation;
import org.springframework.stereotype.Service;
import vn.flexin.backend.mono.auth.service.keycloak.UserKeycloakService;
import vn.flexin.backend.mono.common.dto.PaginationResponse;
import vn.flexin.backend.mono.common.exception.ForbiddenException;
import vn.flexin.backend.mono.common.exception.ResourceNotFoundException;
import vn.flexin.backend.mono.common.exception.message.ErrorMessage;
import vn.flexin.backend.mono.common.util.StringUtils;
import vn.flexin.backend.mono.lookup.dto.*;
import vn.flexin.backend.mono.lookup.entity.Lookup;
import vn.flexin.backend.mono.lookup.repository.LookupRepository;
import vn.flexin.backend.mono.user.entity.User;
import vn.flexin.backend.mono.user.service.UserService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.util.Pair;
import java.util.List;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class LookupServiceImpl implements LookupService{

    private final LookupRepository lookupRepository;
    private final UserKeycloakService userKeycloakService;
    private final UserService userService;

    public List<String> getAllTypes() {
        return lookupRepository.findAll().stream()
                .filter(Lookup::isActive)
                .map(Lookup::getType)
                .collect(Collectors.toList());
    }

    public List<LookupResponse> getLookupsByType(String type, String dependValue) {
        return getAllByType(type).stream()
                .filter(Lookup::isActive)
                .filter(lookup ->
                        StringUtils.isEmpty(dependValue) ||
                        (!StringUtils.isEmpty(dependValue)
                        && (lookup.getDependValue() == null
                            || lookup.getDependValue().equals(dependValue)
                        ))
                )
                .map(lookup -> LookupResponse.builder()
                        .id(lookup.getId())
                        .displayText(lookup.getDisplayText())
                        .displayTextEng(lookup.getDisplayTextEng())
                        .value(lookup.getValue())
                        .type(lookup.getType())
                        .isActive(lookup.isActive())
                        .additionalDataJson(lookup.getAdditionalDataJson())
                        .build())
                .toList();
    }

    @Override
    public List<LookupResponse> getLookupsByType(String type, Long userId) {
        UserRepresentation userRepresentation = userKeycloakService.getCurrentUser();
        User user = userService.getUserByUlid(userRepresentation.getUsername());
        if (!user.getId().equals(userId)) {
            throw new ForbiddenException();
        }

        //TODO temporary not use userId, waiting for clearing spec
        return getAllByType(type).stream()
                .filter(Lookup::isActive)
                .map(lookup -> LookupResponse.builder()
                        .id(lookup.getId())
                        .displayText(lookup.getDisplayText())
                        .displayTextEng(lookup.getDisplayTextEng())
                        .type(lookup.getType())
                        .isActive(lookup.isActive())
                        .additionalDataJson(lookup.getAdditionalDataJson())
                        .build())
                .collect(Collectors.toList());
    }

    @Override
    public List<Lookup> getAllByType(String type) {
        return lookupRepository.findByType(type);
    }

    @Override
    public LookupResponse createUserLookup(CreateUserLookupRequest request) {
        Lookup lookup = new Lookup();
        lookup.setDisplayText(request.getDisplayText());
        lookup.setDisplayTextEng(request.getDisplayTextEng());
        lookup.setType(request.getType());
        lookup.setActive(false);

        lookup = lookupRepository.save(lookup);

        return LookupResponse.builder()
                .id(lookup.getId())
                .displayText(lookup.getDisplayText())
                .displayTextEng(lookup.getDisplayTextEng())
                .type(lookup.getType())
                .isActive(lookup.isActive())
                .build();
    }

    @Override
    public LookupResponse createAdminLookup(CreateAdminLookupRequest request) {
        Lookup lookup = new Lookup();
        lookup.setDisplayText(request.getDisplayText());
        lookup.setDisplayTextEng(request.getDisplayTextEng());
        lookup.setType(request.getType());
        lookup.setActive(true);
        lookup.setAdditionalDataJson(request.getAdditionalDataJson());

        lookup = lookupRepository.save(lookup);

        return LookupResponse.builder()
                .id(lookup.getId())
                .displayText(lookup.getDisplayText())
                .displayTextEng(lookup.getDisplayTextEng())
                .type(lookup.getType())
                .isActive(lookup.isActive())
                .additionalDataJson(lookup.getAdditionalDataJson())
                .build();
    }

    @Override
    @Transactional(readOnly = true)
    public Pair<List<LookupResponse>, PaginationResponse> getLookupsForReview(LookupFilter filters) {
        Page<Lookup> lookups = lookupRepository.findAll(filters);

        List<LookupResponse> responses = lookups.getContent().stream()
                .map(lookup -> LookupResponse.builder()
                        .id(lookup.getId())
                        .displayText(lookup.getDisplayText())
                        .displayTextEng(lookup.getDisplayTextEng())
                        .type(lookup.getType())
                        .isActive(lookup.isActive())
                        .additionalDataJson(lookup.getAdditionalDataJson())
                        .build())
                .collect(Collectors.toList());

        PaginationResponse paging = new PaginationResponse(
                filters.getLimit(),
                filters.getPage(),
                (int) lookups.getTotalElements()
        );

        return Pair.of(responses, paging);
    }

    @Override
    @Transactional
    public LookupResponse updateLookup(Long id, UpdateLookupRequest request) {
        Lookup lookup = lookupRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException(ErrorMessage.LOOKUP_NOT_FOUND));

        if (request.getDisplayText() != null) {
            lookup.setDisplayText(request.getDisplayText());
        }

        if (request.getDisplayTextEng() != null) {
            lookup.setDisplayTextEng(request.getDisplayTextEng());
        }

        if (request.getIsActive() != null) {
            lookup.setActive(request.getIsActive());
        }

        lookup = lookupRepository.save(lookup);

        return LookupResponse.builder()
                .id(lookup.getId())
                .displayText(lookup.getDisplayText())
                .displayTextEng(lookup.getDisplayTextEng())
                .type(lookup.getType())
                .isActive(lookup.isActive())
                .additionalDataJson(lookup.getAdditionalDataJson())
                .build();
    }


}
