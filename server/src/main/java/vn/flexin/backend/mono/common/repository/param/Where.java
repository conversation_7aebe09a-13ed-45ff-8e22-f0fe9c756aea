package vn.flexin.backend.mono.common.repository.param;

import lombok.Data;

@Data
public class Where {
    private String property;
    private Object value;
    private Operator operator;
    private Complex complex;

    public Where(String property, Object value) {
        this.property = property;
        this.value = value;
        this.operator = Operator.EQUAL;
    }

    public Where(String property, Operator operator, Object value) {
        this.property = property;
        this.value = value;
        this.operator = operator;
    }


    public Where(Complex complex, Object value) {
        this.complex = complex;
        this.value = value;
    }

}
