package vn.flexin.backend.mono.payment.service.personal;

import jakarta.transaction.Transactional;
import org.apache.commons.lang3.tuple.Pair;
import vn.flexin.backend.mono.common.dto.PaginationResponse;
import vn.flexin.backend.mono.payment.dto.request.PaymentTransactionRequest;
import vn.flexin.backend.mono.payment.dto.request.PersonalTransactionFilter;
import vn.flexin.backend.mono.payment.dto.request.RedeemPointRequest;
import vn.flexin.backend.mono.payment.dto.response.PersonalTransactionResponse;
import vn.flexin.backend.mono.payment.dto.response.PersonalWalletResponse;
import vn.flexin.backend.mono.payment.entity.personal.PersonalPaymentRequest;

import java.util.List;

public interface MobilePersonalWalletService {
    PersonalWalletResponse getWallet();

    void redeemPoints(RedeemPointRequest request);

    void createPaymentRequest(PaymentTransactionRequest dto);

    void updatePaymentStatus(String thirdPartyTransactionId, String status);

    Pair<List<PersonalTransactionResponse>, PaginationResponse> getPersonalTransactions(PersonalTransactionFilter request);
}
