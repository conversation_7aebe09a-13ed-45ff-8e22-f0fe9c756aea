package vn.flexin.backend.mono.user.repository.user;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import vn.flexin.backend.mono.user.dto.UserFilter;
import vn.flexin.backend.mono.user.dto.UserSortField;
import vn.flexin.backend.mono.user.entity.User;

import java.util.List;

public interface UserRepositoryCustom {
    Page<User> searchUsers(String keyword, List<UserFilter> filters, Pageable pageable, UserSortField sortBy, Boolean ascending);
}
