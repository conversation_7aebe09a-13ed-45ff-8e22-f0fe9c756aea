package vn.flexin.backend.mono.user.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateRoleRequest {
    private Long id;

    @NotBlank(message = "Role is required")
    @Pattern(regexp = "^(jobSeeker|employer)$", message = "Role must be either 'jobSeeker' or 'employer'")
    private String role;
}
