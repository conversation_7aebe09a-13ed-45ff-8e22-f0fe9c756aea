package vn.flexin.backend.mono.home.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import vn.flexin.backend.mono.common.dto.PaginationApiResponseDto;
import vn.flexin.backend.mono.home.dto.*;
import vn.flexin.backend.mono.home.service.EmployerHomeService;

import java.util.List;

@RestController
@RequiredArgsConstructor
@Slf4j
public class EmployerHomeControllerImpl implements EmployerHomeController {

    private final EmployerHomeService employerHomeService;

    @Override
    public ResponseEntity<PaginationApiResponseDto<List<OutstandingCandidateDto>>> searchOutstandingCandidate(Long branchId, OutstandingCandidateFilter filters) {
        var candidates = employerHomeService.searchOutstandingCandidate(branchId, filters);
        return new ResponseEntity<>(PaginationApiResponseDto.success("Outstanding Candidate retrieved successfully", candidates.getLeft(), candidates.getRight()), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<PaginationApiResponseDto<List<SuitableCandidateDto>>> searchSuitableCandidate(Long branchId, SuitableCandidateFilter filters) {
        var candidates = employerHomeService.searchSuitableCandidate(branchId, filters);
        return new ResponseEntity<>(PaginationApiResponseDto.success("Suitable Candidate retrieved successfully", candidates.getLeft(), candidates.getRight()), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<PaginationApiResponseDto<List<NearbyCandidateDto>>> searchNearbyCandidate(Long branchId, NearbyCandidateFilter filters) {
        var candidates = employerHomeService.searchNearbyCandidate(branchId, filters);
        return new ResponseEntity<>(PaginationApiResponseDto.success("Nearby Candidate retrieved successfully", candidates.getLeft(), candidates.getRight()), HttpStatus.OK);
    }
}