package vn.flexin.backend.mono.common.util;

import com.nimbusds.jose.JWSObject;
import com.nimbusds.jose.JWSVerifier;
import com.nimbusds.jose.crypto.RSASSAVerifier;
import com.nimbusds.jose.jwk.JWK;
import com.nimbusds.jose.jwk.JWKSet;
import com.nimbusds.jwt.JWTClaimsSet;
import vn.flexin.backend.mono.common.exception.BadRequestException;

import java.net.URL;
import java.security.interfaces.RSAPublicKey;

public class JwtUtils {

    private JwtUtils(){}

    public static String getSubjectFromToken(String token, String keycloakPublicKeyUrl) throws Exception {
        RSAPublicKey publicKey = loadPublicKeyFromJWKS(keycloakPublicKeyUrl);

        JWSObject jwsObject = JWSObject.parse(token);
        JWSVerifier verifier = new RSASSAVerifier(publicKey);

        if (!jwsObject.verify(verifier)) {
            throw new BadRequestException("Token không hợp lệ hoặc đã bị thay đổi");
        }

        JWTClaimsSet claimsSet = JWTClaimsSet.parse(jwsObject.getPayload().toJSONObject());

        return claimsSet.getSubject();
    }

    private static RSAPublicKey loadPublicKeyFromJWKS(String keycloakPublicKeyUrl) throws Exception {
        JWKSet jwkSet = JWKSet.load(new URL(keycloakPublicKeyUrl));

        JWK jwk = jwkSet.getKeys().stream()
                .filter(k -> "RSA".equals(k.getKeyType().getValue()))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("Không tìm thấy RSA key"));

        return jwk.toRSAKey().toRSAPublicKey();
    }
}

