package vn.flexin.backend.mono.company.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.security.core.parameters.P;
import vn.flexin.backend.mono.common.repository.JpaSpecificationRepository;
import vn.flexin.backend.mono.company.dto.staff.StaffFilter;
import vn.flexin.backend.mono.company.entity.Staff;

import java.util.List;
import java.util.Optional;

public interface StaffRepository extends JpaSpecificationRepository<Staff, Long> {
    @Query("SELECT s FROM Staff s " +
            "JOIN s.branch b " +
            "JOIN b.company c " +
            "WHERE c.id = :companyId AND b.isActive = true AND s.isActive = true")
    List<Staff> findAllStaffByCompanyId(@Param("companyId") Long companyId);

    @Query("SELECT s FROM Staff s WHERE s.branch.id = :branchId AND s.id = :staffId")
    Optional<Staff> findStaffInBranch(@Param("branchId") Long branchId, @Param("staffId") Long staffId);

    @Query("SELECT s FROM Staff s " +
            "WHERE s.branch.id = :branchId AND s.user.id = :userId")
    Optional<Staff> findByBranchIdAndUserId(@Param("branchId") Long branchId,
                                            @Param("userId") Long userId);

    @Query("SELECT s FROM Staff s WHERE s.branch.id IN (:branchIds)")
    List<Staff> findAllByBranchIdIn(@Param("branchIds") List<Long> branchIds);

    @Modifying
    @Query("DELETE FROM Staff staff WHERE staff.branch.id = :branchId")
    void deleteAllByBranchId(Long branchId);

    @Query("""
        SELECT staff FROM Staff staff
        WHERE staff.user.ulid = :userId
            AND staff.branch.id = :branchId
    """)
    Optional<Staff> findByBranchIdAndUserUlid(@Param("branchId")Long branchId,
                                    @Param("userId") String userId);

}
