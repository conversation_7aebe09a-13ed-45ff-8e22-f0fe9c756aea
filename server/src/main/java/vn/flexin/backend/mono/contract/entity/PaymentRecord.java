package vn.flexin.backend.mono.contract.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import jakarta.persistence.*;
import vn.flexin.backend.mono.contract.dto.PaymentRecordResponse;

import java.time.LocalDateTime;

@Entity
@Table(name = "t_payment_records")
@EntityListeners(AuditingEntityListener.class)
@Data
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
public class PaymentRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private LocalDateTime date;

    @Column(nullable = false)
    private Double amount;

    @Column(columnDefinition = "TEXT")
    private String description;

    @Column(nullable = false)
    private String status = "pending"; // pending, completed

    @Column(nullable = false)
    private String paymentMethod;

    private String transactionId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "contract_id", nullable = false)
    @JsonIgnore
    private Contract contract;

    @CreatedDate
    private LocalDateTime createdAt;

    @LastModifiedDate
    private LocalDateTime updatedAt;

    public PaymentRecordResponse toResponse() {
        PaymentRecordResponse response = new PaymentRecordResponse();
        response.setId(id);
        response.setDate(date);
        response.setAmount(amount);
        response.setDescription(description);
        response.setStatus(status);
        response.setPaymentMethod(paymentMethod);
        response.setCreatedAt(createdAt);
        response.setUpdatedAt(updatedAt);
        response.setTransactionId(transactionId);
        return response;
    }
} 