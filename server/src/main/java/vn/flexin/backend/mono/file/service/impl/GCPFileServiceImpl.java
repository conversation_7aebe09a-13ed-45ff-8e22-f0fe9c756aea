package vn.flexin.backend.mono.file.service.impl;
//
//import com.google.cloud.storage.BlobId;
//import com.google.cloud.storage.BlobInfo;
//import com.google.cloud.storage.Storage;
//import lombok.AllArgsConstructor;
//import org.springframework.stereotype.Service;
//import org.springframework.web.multipart.MultipartFile;
//import vn.flexin.backend.mono.common.exception.BadRequestException;
//import vn.flexin.backend.mono.common.exception.ResourceNotFoundException;
//import vn.flexin.backend.mono.file.config.FileConfig;
//import vn.flexin.backend.mono.file.entity.File;
//import vn.flexin.backend.mono.file.enums.FileType;
//import vn.flexin.backend.mono.file.repository.FileRepository;
//import vn.flexin.backend.mono.file.service.FileService;
//
//import java.io.IOException;
//import java.util.List;
//
//@Service
//@AllArgsConstructor
//public class GCPFileServiceImpl implements FileService {
//
//    private final Storage storage;
//
//    private final FileConfig fileConfig;
//
//    private final FileRepository fileRepository;
//
//    @Override
//    public File save(File file) {
//        return fileRepository.save(file);
//    }
//
//    @Override
//    public void saveAll(List<File> files) {
//        fileRepository.saveAll(files);
//    }
//    public void saveAll(List<File> files) {
//        fileRepository.saveAll(files);
//    }
//
//    @Override
//    public boolean handleDeleteFileOnCloudProvider(String filePath) {
//        try {
//            BlobId blobId = BlobId.of(fileConfig.getBucketName(), filePath);
//            return storage.delete(blobId);
//        } catch (Exception e) {
//            return false;
//        }
//    }
//
//    @Override
//    public void deleteByFilePath(String filePath) {
//        fileRepository.deleteByFilePath(filePath);
//    }
//
//    @Override
//    public String retrieveImagePathByName(String name) {
//        File file = getFilePathByFileName(name, fileRepository);
//        if (file == null) {
//            throw new ResourceNotFoundException("File name not found.");
//        }
//        if (!FileType.isImageFile(file)) {
//            throw new BadRequestException("File is not image.");
//        }
//        return file.getFilePath();
//    }
//
//    @Override
//    public void handleUploadFileToCloudProvider(MultipartFile file, String filePath, FileType fileType) {
//        filePath = "%s/%s".formatted(fileType.getType(), filePath);
//        BlobId blobId = BlobId.of(fileConfig.getBucketName(), filePath);
//        BlobInfo blobInfo = BlobInfo.newBuilder(blobId).setContentType(file.getContentType()).build();
//        try{
//            storage.create(blobInfo, file.getBytes());
//        } catch (IOException e) {
//            throw new BadRequestException("Can not upload file to server.");
//        }
//    }
//
//    public String createFilePath(String modifiedFileName, FileType fileType) {
//        return "https://storage.googleapis.com" + "/" +
//                fileConfig.getBucketName() + "/" +
//                fileType.getType() + "/" +
//                modifiedFileName;
//    }
//}
