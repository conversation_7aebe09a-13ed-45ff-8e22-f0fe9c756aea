package vn.flexin.backend.mono.common.enums;

import lombok.Getter;

@Getter
public enum ErrorEnum {
    SYSTEM_ERROR(400, "System  error"),
    MISSING_DEVICE_ID_HEADER(401, "Missing DeviceId header"),
    MISSING_AUTHORIZATION_HEADER(402, "Missing authorization header"),
    UNAUTHORIZED_USER(403, "Unauthorized user"),
    UNAUTHORIZED_DEVICE(404, "Unauthorized device"),
    LOGIN_ON_NEW_DEVICE(405, "Login on new device");

    private final int code;
    private final String message;

    ErrorEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }


}
