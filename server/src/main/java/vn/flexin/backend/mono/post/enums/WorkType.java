package vn.flexin.backend.mono.post.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.Getter;
import vn.flexin.backend.mono.common.enums.AppStatus;
import vn.flexin.backend.mono.common.exception.ResponseAppStatusException;

import java.util.Arrays;

@Getter
public enum WorkType {
    ONSITE,
    REMOTE,
    HYBRID;

    public static String getValues() {
        return String.join(", ", Arrays.stream(values()).map(x -> x.name().toLowerCase()).toList());
    }

    @JsonCreator
    public static WorkType fromString(String value) {
        try {
            return valueOf(value);
        } catch (Exception ex) {
            throw new ResponseAppStatusException(AppStatus.BAD_REQUEST, "Work type must be any of [" + getValues() + "]");
        }
    }
}
