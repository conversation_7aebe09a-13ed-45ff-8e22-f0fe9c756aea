package vn.flexin.backend.mono.user.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.flexin.backend.mono.address.dto.response.AddressResponse;
import vn.flexin.backend.mono.user.entity.User;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class BasicUserInfoResponse {
    private Long id;
    private String ulid;
    private String name;
    private String email;
    private String phoneNumber;
    private String profilePicture;
    private AddressResponse address;

    public BasicUserInfoResponse(User user) {
        this.id = user.getId();
        this.ulid = user.getUlid();
        this.email = user.getEmail();
        this.name = user.getName();
        this.phoneNumber = user.getPhoneNumber();
        this.profilePicture = user.getProfilePicture();
        if (user.getAddress() != null) {
            this.address = new AddressResponse(user.getAddress());
        }
    }
}
