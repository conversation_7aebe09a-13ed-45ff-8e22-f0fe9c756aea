package vn.flexin.backend.mono.auth.service.admin.impl;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import io.jsonwebtoken.security.SignatureException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.keycloak.representations.idm.UserRepresentation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import vn.flexin.backend.mono.address.entity.Address;
import vn.flexin.backend.mono.address.service.AddressService;
import vn.flexin.backend.mono.auth.dto.*;
import vn.flexin.backend.mono.auth.service.admin.AdminAuthService;
import vn.flexin.backend.mono.auth.service.keycloak.AdminKeycloakService;
import vn.flexin.backend.mono.auth.util.AuthConstant;
import vn.flexin.backend.mono.common.exception.message.ErrorMessage;
import vn.flexin.backend.mono.common.exception.ResourceNotFoundException;
import vn.flexin.backend.mono.common.exception.UnauthorizedException;
import vn.flexin.backend.mono.common.util.SecurityUtil;
import vn.flexin.backend.mono.notification.service.EmailService;
import vn.flexin.backend.mono.user.dto.CurrentUserResponse;
import vn.flexin.backend.mono.user.dto.UserDto;
import vn.flexin.backend.mono.user.entity.User;
import vn.flexin.backend.mono.user.enums.TokenPurpose;
import vn.flexin.backend.mono.user.repository.user.UserRepository;
import vn.flexin.backend.mono.user.service.UserService;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class AdminAuthServiceImpl implements AdminAuthService {

    private final UserService userService;
    private final AdminKeycloakService keycloakService;
    private final PasswordEncoder passwordEncoder;
    private final UserRepository userRepository;
    private final EmailService emailService;
    private final AdminKeycloakService adminKeycloakService;
    private final AddressService addressService;

    @Value("${jwt.temp-token.secret}")
    private String secretKey;


    @Override
    public void forgotPassword(AdminForgotPasswordRequest request) {
        // find email
        User user = userRepository.findByEmail(request.getEmail())
                .orElseThrow(() -> new ResourceNotFoundException(ErrorMessage.USER_NOT_FOUND));

        Map<String, Object> claims = new HashMap<>();
        claims.put(AuthConstant.PURPOSE, TokenPurpose.RESET_PASSWORD);
        claims.put(AuthConstant.EMAIL, request.getEmail()); // Add list

        // Generate and save temporary token with email
        String token =  SecurityUtil.generateTempToken(request.getEmail(), claims, secretKey);

        emailService.sendForgotPasswordEmail(user, token);
    }

    // Reset password
    public void resetPassword(String token, String email, String newPassword) {
        verifyTempToken(token, email, TokenPurpose.RESET_PASSWORD);

        User user = userService.getAdminByEmail(email);
        if (user == null || !user.isActive()) {
            throw new ResourceNotFoundException(ErrorMessage.USER_NOT_FOUND);
        }

        user.setPassword(passwordEncoder.encode(newPassword));
        userService.save(user);
        keycloakService.updatePassword(user.getKeycloakUserId(), user.getPassword());
    }

    public boolean verifyTempToken(String token, String email, TokenPurpose purpose) {
        try {
            Claims claims = Jwts.parserBuilder()
                    .setSigningKey(Keys.hmacShaKeyFor(secretKey.getBytes()))
                    .build()
                    .parseClaimsJws(token)
                    .getBody();

            String subject = claims.getSubject();
            String tokenEmail = claims.get(AuthConstant.EMAIL, String.class);
            String tokenPurpose = claims.get(AuthConstant.PURPOSE, String.class);
            Date expiryDate = claims.getExpiration();

            // Validate the token's claims
            return !subject.equals(email) ||
                    !tokenEmail.equals(email) ||
                    !tokenPurpose.equals(purpose.toString()) ||
                    isTokenExpired(expiryDate);
        } catch (SignatureException e) {
            log.error("Invalid or expired token", e);
            return true;  // Token signature is invalid
        } catch (Exception e) {
            log.error("Error verifying temporary token", e);
            return true;  // Other errors such as malformed token
        }
    }

    private boolean isTokenExpired(Date expiryDate) {
        return expiryDate != null && expiryDate.before(new Date());
    }

    @Override
    public TokenDto refreshToken(RefreshTokenRequest refreshTokenRequest) {
        JwtToken token = keycloakService.refreshToken(refreshTokenRequest.getRefreshToken());

        return TokenDto.builder()
                .token(token.getAccess_token())
                .refreshToken(refreshTokenRequest.getRefreshToken())
                .build();
    }

    @Override
    public void logout() {
        UserRepresentation currentUser = keycloakService.getCurrentUser();
        User user = userService.getUserByUlid(currentUser.getUsername());

        keycloakService.logoutUser(user.getKeycloakUserId());
    }

    @Override
    public LoginResponse login(AdminLoginRequest loginRequest) {
        try {

            // Get user by email
            UserDto userDto = userService.getUserByEmail(loginRequest.getEmail());
            if (userDto == null) {
                log.warn("Login failed: User not found with email: {}", loginRequest.getEmail());
                throw new UnauthorizedException("Invalid email or password");
            }

            // Check isAdminPortal permission for admin-portal client
            if (userDto.getIsAdminPortal() == null || !userDto.getIsAdminPortal()) {
                log.warn("Login failed: User {} does not have admin portal access", loginRequest.getEmail());
                throw new UnauthorizedException("Access denied: You do not have permission to access admin portal");
            }
            log.info("Admin portal access verified for user: {}", loginRequest.getEmail());

            CurrentUserResponse userResponse = new CurrentUserResponse(userDto);

            // Get token from Keycloak
            JwtToken token = adminKeycloakService.getToken(userDto.getUlid(), loginRequest.getPassword());

            return LoginResponse.builder()
                    .accessToken(token.getAccess_token())
                    .refreshToken(token.getRefresh_token())
                    .tokenType("Bearer")
                    .user(userResponse)
                    .build();
        } catch (UnauthorizedException e) {
            throw e;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("Admin login error: ", e);
            throw new UnauthorizedException("Invalid email or password: " + e.getMessage());
        }
    }

    @Override
    public void changePassword(ChangePasswordRequest changePasswordRequest) {
        try {
            log.info("Change password request");

            // Validate new password and confirm password match
            if (!changePasswordRequest.getNewPassword().equals(changePasswordRequest.getConfirmPassword())) {
                throw new UnauthorizedException("New password and confirm password do not match");
            }

            // Get current logged-in user
            UserRepresentation currentUser = keycloakService.getCurrentUser();
            User user = userService.getUserByUlid(currentUser.getUsername());

            if (user == null || !user.isActive()) {
                throw new UnauthorizedException("User not found or inactive");
            }

            // Verify current password
            if (!passwordEncoder.matches(changePasswordRequest.getCurrentPassword(), user.getPassword())) {
                log.warn("Change password failed: Current password is incorrect for user: {}", user.getEmail());
                throw new UnauthorizedException("Current password is incorrect");
            }

            // Update password in database
            user.setPassword(passwordEncoder.encode(changePasswordRequest.getNewPassword()));
            userService.save(user);

            // Update password in Keycloak
            keycloakService.updatePassword(user.getKeycloakUserId(), changePasswordRequest.getNewPassword());

            log.info("Password changed successfully for user: {}", user.getEmail());

        } catch (UnauthorizedException e) {
            throw e;
        } catch (Exception e) {
            log.error("Change password error: ", e);
            throw new UnauthorizedException("Failed to change password: " + e.getMessage());
        }
    }

    @Override
    public void updateProfile(AdminUpdateProfileRequest updateProfileRequest) {
        try {
            log.info("Admin update profile request");

            // Get current logged-in user
            UserRepresentation currentUser = keycloakService.getCurrentUser();
            User user = userService.getUserByUlid(currentUser.getUsername());

            if (user == null || !user.isActive()) {
                throw new UnauthorizedException("User not found or inactive");
            }

            // Update name if provided
            if (updateProfileRequest.getName() != null && !updateProfileRequest.getName().trim().isEmpty()) {
                user.setName(updateProfileRequest.getName().trim());
            }

            // Update phone number if provided
            if (updateProfileRequest.getPhoneNumber() != null && !updateProfileRequest.getPhoneNumber().trim().isEmpty()) {
                user.setPhoneNumber(updateProfileRequest.getPhoneNumber().trim());
            }

            // Update date of birth if provided
            if (updateProfileRequest.getDateOfBirth() != null && !updateProfileRequest.getDateOfBirth().trim().isEmpty()) {
                try {
                    // Parse dateOfBirth string (support ISO datetime format)
                    java.time.LocalDate dateOfBirth = parseDateOfBirth(updateProfileRequest.getDateOfBirth());
                    user.setDateOfBirth(dateOfBirth);
                } catch (Exception e) {
                    log.warn("Invalid date format for dateOfBirth: {}", updateProfileRequest.getDateOfBirth());
                    throw new UnauthorizedException("Invalid date format for date of birth");
                }
            }

            // Update gender if provided
            if (updateProfileRequest.getGender() != null && !updateProfileRequest.getGender().trim().isEmpty()) {
                user.setGender(updateProfileRequest.getGender().trim());
            }

            // Update address if provided
            if (updateProfileRequest.getAddress() != null) {
                if (user.getAddress() != null) {
                    // Update existing address
                    updateProfileRequest.getAddress().setId(user.getAddress().getId());
                    Address updatedAddress = addressService.updateAddress(updateProfileRequest.getAddress());
                    user.setAddress(updatedAddress);
                } else {
                    // Create new address
                    Address newAddress = addressService.createAddress(updateProfileRequest.getAddress());
                    user.setAddress(newAddress);
                }
            }

            // Save updated user
            userService.save(user);

            log.info("Profile updated successfully for admin user: {}", user.getEmail());

        } catch (UnauthorizedException e) {
            throw e;
        } catch (Exception e) {
            log.error("Update profile error: ", e);
            throw new UnauthorizedException("Failed to update profile: " + e.getMessage());
        }
    }

    private java.time.LocalDate parseDateOfBirth(String dateOfBirthStr) {
        if (dateOfBirthStr == null) return null;
        
        try {
            // Try parsing as ISO datetime first
            java.time.LocalDateTime dateTime = java.time.LocalDateTime.parse(dateOfBirthStr.replace("Z", ""));
            return dateTime.toLocalDate();
        } catch (Exception e) {
            try {
                // Try parsing as LocalDate
                return java.time.LocalDate.parse(dateOfBirthStr);
            } catch (Exception ex) {
                // Throw exception if can't parse
                throw new UnauthorizedException("Invalid date format");
            }
        }
    }

} 