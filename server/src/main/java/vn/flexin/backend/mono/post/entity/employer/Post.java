package vn.flexin.backend.mono.post.entity.employer;


import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.Type;
import vn.flexin.backend.mono.common.entity.AbstractAuditingEntity;
import vn.flexin.backend.mono.company.entity.Branch;
import vn.flexin.backend.mono.post.dto.PostRequiredDocument;
import vn.flexin.backend.mono.post.enums.*;
import vn.flexin.backend.mono.user.entity.User;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
@Table(name = "t_posts")
public class Post extends AbstractAuditingEntity<Long> implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.EAGER, cascade = {CascadeType.DETACH, CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH})
    @JoinColumn(name = "employer_id")
    private User employer;

    // Step 1

    @ManyToOne(fetch = FetchType.EAGER, cascade = {CascadeType.DETACH, CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH})
    @JoinColumn(name = "branch_id")
    private Branch branch;

    // Step 2

    @NotNull
    private String title;

    @Enumerated(EnumType.STRING)
    private WorkType workType;

    private String description;

    @OneToOne(fetch = FetchType.EAGER, cascade = {CascadeType.DETACH, CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH})
    @JoinColumn(name = "salary_id")
    private Salary salary;

    private Integer positions;

    private Integer minExperience;

    private Integer maxExperience;

    // Step 3

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private Integer workingHourPerDay;

    @Type(JsonType.class)
    @Column(columnDefinition = "jsonb")
    private Set<PostWorkDay> workingDays;

    @Type(JsonType.class)
    @Column(columnDefinition = "jsonb")
    private Set<PostWorkShift> workingShifts;

    // Step 4

    @Type(JsonType.class)
    @Column(columnDefinition = "jsonb")
    private Set<String> skills = new HashSet<>();


    @Type(JsonType.class)
    @Column(columnDefinition = "jsonb")
    private List<String> benefits;

    @Type(JsonType.class)
    @Column(columnDefinition = "jsonb")
    private List<PostRequiredDocument> requiredDocuments;

    // Step 5

    private boolean isFeatureJob = false;

    @Enumerated(EnumType.STRING)
    private FeatureDuration featureDuration;

    private boolean urgentHiring = false;

    private boolean receiveNotifyNewApplication = false;

    private boolean showContactInformation = false;

    private boolean autoApproveApplication = false;

    private LocalDateTime activeDate;

    @Enumerated(EnumType.STRING)
    private JobType jobType;

    @Enumerated(EnumType.STRING)
    private PostStatus status;

    @ToString.Exclude
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "post", cascade = {CascadeType.DETACH, CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH})
    private List<PostView> postViews;

    @ToString.Exclude
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "post", cascade = {CascadeType.DETACH, CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH})
    private List<PostInterest> postInterests;

    @ToString.Exclude
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "post", cascade = {CascadeType.DETACH, CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH})
    private List<PostApplication> postApplication;

    public long getPostViewCount() {
        if (postViews == null) return 0;

        return postViews.stream()
                .filter(view -> view.getViewer() != null)
                .map(view -> view.getViewer().getId())
                .distinct()
                .count();
    }
}
