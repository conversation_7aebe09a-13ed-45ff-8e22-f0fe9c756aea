package vn.flexin.backend.mono.user.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.flexin.backend.mono.common.entity.AbstractAuditingEntity;
import vn.flexin.backend.mono.resume.entity.Resume;

@Entity
@Table(name = "t_resume_contact_infos")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ContactInfo extends AbstractAuditingEntity<Long> {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String phoneNumber;

    private String email;

    private String address;

    private String city;

    private String state;

    private String zipCode;

    private String country;

    private String linkedInUrl;

    private String portfolioUrl;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "resume_id")
    @JsonIgnore
    private Resume resume;
} 