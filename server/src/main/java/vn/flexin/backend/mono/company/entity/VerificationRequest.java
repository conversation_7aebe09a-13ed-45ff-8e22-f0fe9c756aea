package vn.flexin.backend.mono.company.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import vn.flexin.backend.mono.common.entity.AbstractAuditingEntity;
import vn.flexin.backend.mono.company.enums.CompanyVerifyStatus;
import vn.flexin.backend.mono.user.entity.User;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Entity
@Table(name = "t_company_verification_requests")
@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class VerificationRequest extends AbstractAuditingEntity<Long> implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull
    @ManyToOne
    @JoinColumn(name = "company_id")
    private Company company;

    @NotNull
    @ManyToOne
    @JoinColumn(name = "request_user_id")
    private User requestUser;

    @ManyToOne
    @JoinColumn(name = "verify_user_id")
    private User verifyUser;

    @NotBlank
    private String status;

    private String rejectReason;

    @ToString.Exclude
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "request", cascade = {CascadeType.DETACH, CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH})
    private List<VerificationRequestDocument> documents;

    public VerificationRequest(Company company, User requestUser) {
        this.company = company;
        this.requestUser = requestUser;
        this.status = CompanyVerifyStatus.REQUESTED.value();
    }
}
