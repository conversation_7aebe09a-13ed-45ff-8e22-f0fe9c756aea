package vn.flexin.backend.mono.company.dto.company;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

@Data
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
public class UpdateAdminStaffRequest {

    @NotNull
    private Long branchId;

    @NotBlank
    private String position;

    @NotNull
    private Boolean isManager;

}
