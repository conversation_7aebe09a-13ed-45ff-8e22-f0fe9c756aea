package vn.flexin.backend.mono.interview.service.impl;

import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import vn.flexin.backend.mono.application.dto.*;
import vn.flexin.backend.mono.interview.dto.*;
import vn.flexin.backend.mono.interview.entity.Interview;
import vn.flexin.backend.mono.interview.repository.InterviewFeedbackRepository;
import vn.flexin.backend.mono.interview.repository.InterviewRepository;
import vn.flexin.backend.mono.interview.service.InterviewService;
import vn.flexin.backend.mono.common.exception.message.ErrorMessage;
import vn.flexin.backend.mono.common.dto.PaginationResponse;
import vn.flexin.backend.mono.post.repository.PostRepository;
import vn.flexin.backend.mono.user.service.UserService;

import java.util.List;

@Service
@RequiredArgsConstructor
public class InterviewServiceImpl implements InterviewService {
    private final InterviewRepository interviewRepository;
    private final UserService userService;
    private final PostRepository postRepository;
    private final InterviewFeedbackRepository interviewFeedbackRepository;

    @Override
    public InterviewResponse createInterview(InterviewRequest request) {
        var interview = request.toEntity();
        interview.setEmployer(userService.getById(request.getEmployerId()));
        interview.setJobSeeker(userService.getById(request.getJobSeekerId()));

        if (request.getPostId() != null) {
            interview.setPost(postRepository.findById(request.getPostId())
                    .orElseThrow(() -> new EntityNotFoundException("Post not found")));
        }

        return interviewRepository.save(interview).toResponse();
    }

    @Override
    public InterviewResponse getInterviewById(Long id) {
        return interviewRepository.findById(id).map(Interview::toResponse)
                .orElseThrow(() -> new EntityNotFoundException(ErrorMessage.INTERVIEW_NOT_FOUND));
    }

    @Override
    public Pair<List<InterviewResponse>, PaginationResponse> searchInterviews(InterviewFilter filters) {
        var page = interviewRepository.findAll(filters);
        var responses = page.stream().map(Interview::toResponse).toList();
        PaginationResponse paging = new PaginationResponse(filters.getLimit(), filters.getPage(), (int) page.getTotalElements());
        return Pair.of(responses, paging);
    }

    @Override
    public InterviewResponse updateInterview(InterviewRequest updatedInterview) {
        var interview = interviewRepository.findById(updatedInterview.getId())
                .orElseThrow(() -> new EntityNotFoundException(ErrorMessage.INTERVIEW_NOT_FOUND));
        interview.setScheduledTime(updatedInterview.getScheduledTime());
        interview.setDurationMinutes(updatedInterview.getDurationMinutes());
        interview.setStatus(updatedInterview.getStatus());
        interview.setNotes(updatedInterview.getNotes());

        if (updatedInterview.getPostId() != null) {
            interview.setPost(postRepository.findById(updatedInterview.getPostId())
                    .orElseThrow(() -> new EntityNotFoundException("Post not found")));
        }

        return interviewRepository.save(interview).toResponse();
    }

    @Override
    public InterviewResponse updateInterviewStatus(Long id, String status) {
        var interview = interviewRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException(ErrorMessage.INTERVIEW_NOT_FOUND));
        interview.setStatus(status);
        return interviewRepository.save(interview).toResponse();
    }

    @Override
    public Boolean deleteInterview(Long id) {
        var interview = interviewRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException(ErrorMessage.INTERVIEW_NOT_FOUND));
        interviewRepository.delete(interview);
        return true;
    }

    @Override
    public InterviewFeedbackResponse createInterviewFeedback(InterviewFeedbackRequest request) {
        var interview = interviewRepository.findById(request.getInterviewId()).orElseThrow(() -> new EntityNotFoundException(ErrorMessage.INTERVIEW_NOT_FOUND));
        var feedback = request.toEntity();
        feedback.setInterview(interview);
        return interviewFeedbackRepository.save(feedback).toResponse();
    }


}