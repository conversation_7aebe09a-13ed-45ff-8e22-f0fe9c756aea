package vn.flexin.backend.mono.post.service.admin.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import vn.flexin.backend.mono.common.dto.CreateObjectResponse;
import vn.flexin.backend.mono.post.dto.filters.JobSeekerPostFilter;
import vn.flexin.backend.mono.post.dto.request.jobseeker.CreateJobSeekerPostRequest;
import vn.flexin.backend.mono.post.dto.request.jobseeker.UpdateJobSeekerPostRequest;
import vn.flexin.backend.mono.post.dto.response.jobseeker.JobSeekerDetailResponse;
import vn.flexin.backend.mono.post.dto.response.jobseeker.JobSeekerStatisticPostResponse;
import vn.flexin.backend.mono.post.dto.response.jobseeker.SearchJobSeekerPostResponse;
import vn.flexin.backend.mono.post.service.admin.AdminJobSeekerPostService;
import vn.flexin.backend.mono.post.service.mobile.JobSeekerPostService;

import java.util.List;

@Service
@RequiredArgsConstructor
public class AdminJobSeekerPostServiceImpl implements AdminJobSeekerPostService {
    private final JobSeekerPostService jobSeekerPostService;

    @Override
    public CreateObjectResponse createPost(CreateJobSeekerPostRequest request) {
        return jobSeekerPostService.createPost(request);
    }

    @Override
    public void updatePost(UpdateJobSeekerPostRequest request) {
        jobSeekerPostService.updatePost(request);
    }

    @Override
    public JobSeekerDetailResponse getDetailPost(Long id) {
        return jobSeekerPostService.getDetailPost(id);
    }

    @Override
    public void deletePost(Long id) {
        jobSeekerPostService.deletePost(id);
    }

    @Override
    public JobSeekerStatisticPostResponse getStatistics() {
        // TODO: Implement admin-specific statistics if needed
        return new JobSeekerStatisticPostResponse();
    }

    @Override
    public List<SearchJobSeekerPostResponse> searchJobSeekerPost(JobSeekerPostFilter filters) {
        return jobSeekerPostService.searchJobSeekerPost(filters).getLeft();
    }
} 