package vn.flexin.backend.mono.company.dto.company;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Getter
@Setter
public class SearchAdminCompanyRequest {
    private String keyword;
    private String industry;

    @JsonProperty("isVerified")
    private Boolean isVerified;

    @JsonProperty("isIndividual")
    private Boolean isIndividual;

    private String sortBy;

    private String sortOrder;
    private Integer page = 0;
    private Integer limit = 10;

}
