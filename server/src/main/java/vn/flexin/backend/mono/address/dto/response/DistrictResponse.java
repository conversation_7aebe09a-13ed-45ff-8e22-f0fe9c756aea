package vn.flexin.backend.mono.address.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.flexin.backend.mono.address.entity.District;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DistrictResponse {
    private Long code;
    private String name;
    private String codename;
    private String divisionType;
    private Long provinceCode;
    private List<WardResponse> wards;

    public DistrictResponse(District district) {
        this.code = district.getCode();
        this.name = district.getName();
        this.codename = district.getCodename();
        this.divisionType = district.getDivisionType();
        if (district.getProvince() != null) {
            this.provinceCode = district.getProvince().getCode();
        }
    }
}