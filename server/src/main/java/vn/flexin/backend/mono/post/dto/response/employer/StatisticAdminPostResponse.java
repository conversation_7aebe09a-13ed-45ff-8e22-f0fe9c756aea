package vn.flexin.backend.mono.post.dto.response.employer;

import lombok.Data;

import java.util.List;

@Data
public class StatisticAdminPostResponse {
    private int total;
    private int active;
    private int expired;
    private int draft;
    private int closed;
    private int totalApplications;
    private int newApplications;
    //private List<CountResponse> byCategory;
    private List<CountLocationResponse> byLocation;
    private List<CountMonthResponse> byMonth;

}
