package vn.flexin.backend.mono.common.util;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class ValidEnumValidator implements
        ConstraintValidator<ValidEnum, String> {
    private List<String> values;

    @Override
    public void initialize(ValidEnum constraintAnnotation) {
        values = Arrays.stream(constraintAnnotation.anyOf()).
                map(String::toLowerCase).collect(Collectors.toList());
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null) {
            return true;
        }
        return values.contains(value.toLowerCase());
    }
}
