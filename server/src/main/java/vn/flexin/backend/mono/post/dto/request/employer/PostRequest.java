package vn.flexin.backend.mono.post.dto.request.employer;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.flexin.backend.mono.address.dto.request.AddressRequest;
import vn.flexin.backend.mono.post.enums.JobType;
import vn.flexin.backend.mono.post.enums.PostWorkDay;
import vn.flexin.backend.mono.post.enums.PostWorkShift;
import vn.flexin.backend.mono.post.enums.WorkType;
import vn.flexin.backend.mono.post.dto.PostRequiredDocument;
import vn.flexin.backend.mono.post.dto.SalaryDto;
import vn.flexin.backend.mono.post.enums.PostStatus;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Data
@EqualsAndHashCode(callSuper = false)
public class PostRequest {
    @NotNull
    private Long employerId;

    private Long branchId;

    private WorkType workType;

    @NotNull
    private JobType jobType;

    @NotBlank
    private String title;

    private String description;

    private AddressRequest location;

    private Integer minExperience;

    private Integer maxExperience;

    private Integer positions;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private Integer workingHourPerDay;

    @Valid
    @NotNull
    private SalaryDto salary;

    private Set<String> skills;

    private Set<PostWorkDay> workingDays;

    private Set<PostWorkShift> workingShifts;

    private List<String> benefits;

    private List<PostRequiredDocument> requiredDocuments;

    @JsonProperty("isFeatureJob")
    private boolean isFeatureJob = false;

    private String featureDuration;

    private boolean urgentHiring = false;

    private boolean receiveNotifyNewApplication = false;

    private boolean showContactInformation = false;

    private boolean autoApproveApplication = false;

    private LocalDateTime activeDate;

    private PostStatus status;

}
