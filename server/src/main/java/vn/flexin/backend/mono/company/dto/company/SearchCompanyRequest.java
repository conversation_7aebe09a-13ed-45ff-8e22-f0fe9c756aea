package vn.flexin.backend.mono.company.dto.company;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
public class SearchCompanyRequest {
    private Long userId;
    private Integer page;
    private Integer limit;
    private String query;
    private String industry;
    private String sortBy;
    private String sortOrder;
}
