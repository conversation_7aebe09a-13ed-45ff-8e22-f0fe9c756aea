package vn.flexin.backend.mono.permission.service.impl;

import jakarta.persistence.EntityNotFoundException;
import jakarta.ws.rs.NotFoundException;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;
import org.keycloak.representations.idm.RoleRepresentation;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.flexin.backend.mono.auth.service.keycloak.AdminKeycloakService;
import vn.flexin.backend.mono.common.exception.message.ErrorMessage;
import vn.flexin.backend.mono.common.dto.CreateObjectResponse;
import vn.flexin.backend.mono.common.dto.PaginationResponse;
import vn.flexin.backend.mono.common.exception.BadRequestException;
import vn.flexin.backend.mono.common.exception.ResourceNotFoundException;
import vn.flexin.backend.mono.common.util.ModelMapperUtils;
import vn.flexin.backend.mono.permission.dto.PermissionFilter;
import vn.flexin.backend.mono.permission.dto.request.CreatePermissionRequest;
import vn.flexin.backend.mono.permission.dto.request.UpdatePermissionRequest;
import vn.flexin.backend.mono.permission.dto.response.PermissionResponse;
import vn.flexin.backend.mono.permission.entity.Permission;
import vn.flexin.backend.mono.role.entity.Role;
import vn.flexin.backend.mono.permission.repository.PermissionRepository;
import vn.flexin.backend.mono.role.repository.RoleRepository;
import vn.flexin.backend.mono.permission.service.PermissionService;

import java.util.List;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class PermissionServiceImpl implements PermissionService {

    private final PermissionRepository permissionRepository;
    private final RoleRepository roleRepository;
    private final AdminKeycloakService adminKeycloakService;


    @Override
    public CreateObjectResponse createPermission(CreatePermissionRequest request) {
        validatePermissionName(request.getName());

        Permission permission = new Permission();
        var existedPermission = permissionRepository.findByName(request.getName());
        if(existedPermission.isPresent()) {
            throw new BadRequestException(ErrorMessage.PERMISSION_ALREADY_EXISTED);
        }
        permission.setName(request.getName());
        permission.setDescription(request.getDescription());

        String keycloakId = createPermissionInKeycloak(request);
        permission.setKeycloakId(keycloakId);

        Permission savedPermission = permissionRepository.save(permission);
        return new CreateObjectResponse(savedPermission.getId());
    }

    @Override
    public void updatePermission(UpdatePermissionRequest request) {
        Permission permission = findById(request.getId());

        if (!permission.getName().equals(request.getName())) {
            validatePermissionName(request.getName());
        }

        permission.setName(request.getName());
        permission.setDescription(request.getDescription());

        request.setKeycloakId(permission.getKeycloakId());
        updatePermissionInKeycloak(request);

        permissionRepository.save(permission);
    }

    @Override
    public void deletePermission(Long id) {
        Permission permission = findById(id);

        if (isPermissionUsedByAnyRole(id)) {
            throw new BadRequestException(ErrorMessage.PERMISSION_ALREADY_IN_USED);
        }

        deletePermissionFromKeycloak(permission.getKeycloakId());
        permissionRepository.delete(permission);
    }

    @Override
    @Transactional
    public void deletePermissions(List<Long> ids) {
        List<Permission> permissions = permissionRepository.findAllById(ids);

        for (Permission permission : permissions) {
            // Check if the permission is being used by any role
            if (isPermissionUsedByAnyRole(permission.getId())) {
                throw new BadRequestException(ErrorMessage.PERMISSION_ALREADY_IN_USED);
            }
            deletePermissionFromKeycloak(permission.getKeycloakId());
            // Delete the permission
            permissionRepository.delete(permission);
        }
    }

    public Permission findById(Long id) {
        return permissionRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException(ErrorMessage.PERMISSION_NOT_FOUND));
    }

    @Override
    public List<PermissionResponse> getAllPermissions() {
        return permissionRepository.findAll().stream()
                .map(this::mapToPermissionResponse)
                .collect(Collectors.toList());
    }

    @Override
    public Pair<List<PermissionResponse>, PaginationResponse> searchPermissions(PermissionFilter permissionFilter) {
        Page<Permission> permissions = permissionRepository.findAll(permissionFilter);
        List<PermissionResponse> responses = permissions.getContent().stream().map(this::toPermissionResponse).collect(Collectors.toList());
        PaginationResponse paginationResponse = new PaginationResponse(permissionFilter.getLimit(), permissionFilter.getPage(),(int) permissions.getTotalElements());
        return Pair.of(responses, paginationResponse);
    }

    private PermissionResponse toPermissionResponse(Permission permission) {
        PermissionResponse response = new PermissionResponse();
        ModelMapperUtils.map(permission, response);
        response.setUpdatedAt(permission.getLastModifiedAt());

        return response;
    }

    @Override
    public PermissionResponse getPermissionDetail(Long id) {
        Permission permission = findById(id);
        return mapToPermissionResponse(permission);
    }

    public void assignPermissionToRole(Long roleId, Long permissionId) {
        Role role = roleRepository.findById(roleId)
                .orElseThrow(() -> new EntityNotFoundException("Role not found"));
        Permission permission = permissionRepository.findById(permissionId)
                .orElseThrow(() -> new EntityNotFoundException("Permission not found"));

        if (!role.getPermissions().contains(permission)) {
            role.getPermissions().add(permission);
            roleRepository.save(role);
        }
    }

    public void removePermissionFromRole(Long roleId, Long permissionId) {
        Role role = roleRepository.findById(roleId)
                .orElseThrow(() -> new EntityNotFoundException("Role not found"));
        Permission permission = permissionRepository.findById(permissionId)
                .orElseThrow(() -> new EntityNotFoundException("Permission not found"));

        if (role.getPermissions().remove(permission)) {
            roleRepository.save(role);
        }
    }

    @Override
    public boolean isPermissionUsedByAnyRole(Long permissionId) {
        return roleRepository.existsByPermissionsId(permissionId);
    }

    @Override
    public List<PermissionResponse> getPermissionsByRoleId(Long roleId) {
        Role role = roleRepository.findById(roleId)
                .orElseThrow(() -> new EntityNotFoundException(ErrorMessage.ROLE_NOT_FOUND));
        return role.getPermissions().stream()
                .map(this::mapToPermissionResponse)
                .collect(Collectors.toList());
    }

    private void validatePermissionName(String name) {
        if (permissionRepository.existsByName(name)) {
            throw new IllegalArgumentException(ErrorMessage.PERMISSION_ALREADY_EXISTED);
        }
    }

    private String createPermissionInKeycloak(CreatePermissionRequest request) {
        try {
            RoleRepresentation newRole = adminKeycloakService.createPermission(request);
            return newRole.getId();
        } catch (NotFoundException ex) {
            throw new ResourceNotFoundException(ErrorMessage.PERMISSION_NOT_FOUND);
        } catch (BadRequestException e) {
            throw new BadRequestException(e.getMessage());
        } catch (Exception e) {
            throw new BadRequestException(ErrorMessage.CAN_NOT_CREATE_PERMISSION);
        }
    }

    private void updatePermissionInKeycloak(UpdatePermissionRequest request) {
        try {
            adminKeycloakService.updatePermission(request);
        } catch (NotFoundException ex) {
            throw new ResourceNotFoundException(ErrorMessage.PERMISSION_NOT_FOUND);
        } catch (BadRequestException e) {
            throw new BadRequestException(e.getMessage());
        } catch (Exception e) {
            throw new BadRequestException(ErrorMessage.CAN_NOT_UPDATE_ROLE);
        }
    }

    private void deletePermissionFromKeycloak(String keycloakId) {
        try {
            adminKeycloakService.deletePermission(keycloakId);
        } catch (NotFoundException ex) {
            throw new ResourceNotFoundException(ErrorMessage.PERMISSION_NOT_FOUND);
        } catch (BadRequestException e) {
            throw new BadRequestException(e.getMessage());
        } catch (Exception e) {
            throw new BadRequestException(ErrorMessage.CAN_NOT_DELETE_PERMISSION);
        }
    }

    private PermissionResponse mapToPermissionResponse(Permission permission) {
        return PermissionResponse.builder()
                .id(permission.getId())
                .name(permission.getName())
                .description(permission.getDescription())
                .createdAt(permission.getCreatedAt())
                .updatedAt(permission.getLastModifiedAt())
                .build();
    }
}
