package vn.flexin.backend.mono.resume.controller.mobile;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.common.dto.CreateObjectResponse;
import vn.flexin.backend.mono.resume.dto.*;
import vn.flexin.backend.mono.resume.service.ResumeService;
import vn.flexin.backend.mono.user.dto.ContactInfoDto;
import vn.flexin.backend.mono.user.dto.PartTimePreferenceDto;

import java.util.List;

@RestController
@RequiredArgsConstructor
public class MobileResumeControllerImpl implements MobileResumeController {

    private final ResumeService resumeService;

    @Override
    public ResponseEntity<ApiResponseDto<CreateObjectResponse>> createResume(@Valid ResumeDto resumeDto) {
        Long id = resumeService.createResume(resumeDto);
        return new ResponseEntity<>(ApiResponseDto.success(new CreateObjectResponse(id) ,"Resume created successfully"), HttpStatus.CREATED);
    }

    @Override
    public ResponseEntity<ApiResponseDto<ResumeDto>> getResumeById(Long id) {
        ResumeDto resume = resumeService.getResumeById(id);
        return new ResponseEntity<>(ApiResponseDto.success(resume,"Resume retrieved successfully"), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponseDto<List<SearchResumeResponse>>> getResumesByUserId(Long userId) {
        List<SearchResumeResponse> resumes = resumeService.getResumesByUserId(userId);
        return new ResponseEntity<>(ApiResponseDto.success("Resumes retrieved successfully", resumes), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponseDto<List<SearchResumeResponse>>> getMyResumes(Boolean isActive) {
        List<SearchResumeResponse> resumes = resumeService.getMyResumes(isActive);
        return new ResponseEntity<>(ApiResponseDto.success("Resumes retrieved successfully", resumes), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponseDto<List<SearchResumeResponse>>> getActiveResumesByUserId(Long userId) {
        List<SearchResumeResponse> resumes = resumeService.getActiveResumesByUserId(userId);
        return new ResponseEntity<>(ApiResponseDto.success("Active resumes retrieved successfully", resumes), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponseDto<ResumeDto>> updateResume(Long id, @Valid ResumeDto resumeDto) {
        ResumeDto updatedResume = resumeService.updateResume(id, resumeDto);
        return new ResponseEntity<>(ApiResponseDto.success("Resume updated successfully", updatedResume), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> deleteResume(Long id) {
        resumeService.deleteResume(id);
        return new ResponseEntity<>(ApiResponseDto.success("Resume deleted successfully", Boolean.TRUE), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponseDto<WorkExperienceDto>> addWorkExperience(Long resumeId, @Valid WorkExperienceDto workExperienceDto) {
        WorkExperienceDto createdWorkExperience = resumeService.addWorkExperience(resumeId, workExperienceDto);
        return new ResponseEntity<>(ApiResponseDto.success("Work experience added successfully", createdWorkExperience), HttpStatus.CREATED);
    }

    @Override
    public ResponseEntity<ApiResponseDto<WorkExperienceDto>> updateWorkExperience(Long id, @Valid WorkExperienceDto workExperienceDto) {
        WorkExperienceDto updatedWorkExperience = resumeService.updateWorkExperience(id, workExperienceDto);
        return new ResponseEntity<>(ApiResponseDto.success("Work experience updated successfully", updatedWorkExperience), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> deleteWorkExperience(Long id) {
        resumeService.deleteWorkExperience(id);
        return new ResponseEntity<>(ApiResponseDto.success("Work experience deleted successfully", Boolean.TRUE), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponseDto<EducationDto>> addEducation(Long resumeId, @Valid EducationDto educationDto) {
        EducationDto createdEducation = resumeService.addEducation(resumeId, educationDto);
        return new ResponseEntity<>(ApiResponseDto.success("Education added successfully", createdEducation), HttpStatus.CREATED);
    }

    @Override
    public ResponseEntity<ApiResponseDto<EducationDto>> updateEducation(Long id, @Valid EducationDto educationDto) {
        EducationDto updatedEducation = resumeService.updateEducation(id, educationDto);
        return new ResponseEntity<>(ApiResponseDto.success("Education updated successfully", updatedEducation), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> deleteEducation(Long id) {
        resumeService.deleteEducation(id);
        return new ResponseEntity<>(ApiResponseDto.success("Education deleted successfully", Boolean.TRUE), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponseDto<ContactInfoDto>> updateContactInfo(Long resumeId, @Valid ContactInfoDto contactInfoDto) {
        ContactInfoDto updatedContactInfo = resumeService.updateContactInfo(resumeId, contactInfoDto);
        return new ResponseEntity<>(ApiResponseDto.success("Contact info updated successfully", updatedContactInfo), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponseDto<PartTimePreferenceDto>> updatePartTimePreference(Long resumeId, @Valid PartTimePreferenceDto partTimePreferenceDto) {
        PartTimePreferenceDto updatedPartTimePreference = resumeService.updatePartTimePreference(resumeId, partTimePreferenceDto);
        return new ResponseEntity<>(ApiResponseDto.success("Part time preference updated successfully", updatedPartTimePreference), HttpStatus.OK);
    }

//    @Override
//    public ResponseEntity<PaginationApiResponseDto<List<SearchResumeResponse>>> searchResumes(@Valid ResumeFilter resumeFilter) {
//        Pair<PaginationResponse, List<SearchResumeResponse>> response = resumeService.searchResumes(resumeFilter);
//        return new ResponseEntity<>(PaginationApiResponseDto.success("Resumes retrieved successfully", response.getRight(), response.getLeft()), HttpStatus.OK);
//    }
} 