package vn.flexin.backend.mono.company.dto.company;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.flexin.backend.mono.file.dto.FileDto;

@Data
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
public class CreateVRDocumentRequest {

    @NotBlank(message = "Document name can not blank.")
    private String name;

    private String description;

    @NotBlank(message = "Document file path can not blank.")
    private FileDto file;
}
