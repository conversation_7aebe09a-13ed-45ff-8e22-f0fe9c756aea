package vn.flexin.backend.mono.address.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import vn.flexin.backend.mono.address.entity.Ward;

import java.util.List;
import java.util.Optional;

public interface WardRepository extends JpaRepository<Ward, Long> {
    List<Ward> findByDistrictCode(Long districtCode);

    @Query("""
        SELECT w
        FROM Ward w
        WHERE w.district.code = :districtCode
            AND (:name IS NULL OR (w.name like %:name% OR w.codename like %:name%))
    """)
    List<Ward> findByDistrictCodeAndName(Long districtCode, String name);

    Optional<Ward> findByCode(Long wardCode);
}
