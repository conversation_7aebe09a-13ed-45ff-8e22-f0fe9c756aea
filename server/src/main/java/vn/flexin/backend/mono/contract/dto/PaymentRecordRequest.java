package vn.flexin.backend.mono.contract.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.flexin.backend.mono.contract.entity.PaymentRecord;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PaymentRecordRequest {

    private Long id;

    private Long contractId;

    @NotNull(message = "Date is required", groups = create.class)
    private LocalDateTime date;

    @NotNull(message = "amount is required", groups = create.class)
    @Positive(message = "amount must be positive", groups = create.class)
    private Double amount;

    private String description;

    @NotBlank(message = "status type is required", groups = {create.class, update.class})
    private String status; // pending, completed

    @NotBlank(message = "PaymentMethod is required", groups = create.class)
    private String paymentMethod;

    @NotBlank(message = "transactionId is required", groups = update.class)
    private String transactionId;

    public PaymentRecord toEntity() {
        PaymentRecord paymentRecord = new PaymentRecord();
        paymentRecord.setId(id);
        paymentRecord.setDate(date);
        paymentRecord.setAmount(amount);
        paymentRecord.setDescription(description);
        paymentRecord.setStatus(status);
        paymentRecord.setPaymentMethod(paymentMethod);
        paymentRecord.setTransactionId(transactionId);
        return paymentRecord;
    }

    public interface create {
    }

    public interface update {
    }
} 