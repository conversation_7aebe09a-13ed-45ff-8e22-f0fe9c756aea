package vn.flexin.backend.mono.post.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import vn.flexin.backend.mono.post.entity.employer.PostView;

public interface PostViewRepository extends JpaRepository<PostView, Long> {
    @Modifying
    @Query("DELETE FROM PostView pv WHERE pv.post.id = :id")
    void deleteByPostId(@Param("id")Long id);
}