package vn.flexin.backend.mono.application.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class InterviewResponse {
    private Long id;

    private Long employerId;

    private String employerName;

    private Long jobSeekerId;

    private String jobSeekerName;

    private LocalDateTime scheduledTime;

    private Integer durationMinutes;

    private String status;

    private String meetingLink;

    private String meetingId;

    private String meetingPassword;

    private String notes;

    private LocalDateTime startedAt;

    private LocalDateTime endedAt;

    private String cancellationReason;

    private String feedback;

    private Integer employerRating;

    private Integer jobSeekerRating;

    private LocalDateTime createdAt;

    private String createdBy;

    private String lastModifiedBy;

    private LocalDateTime lastModifiedAt = LocalDateTime.now();

    private Long postId;
} 