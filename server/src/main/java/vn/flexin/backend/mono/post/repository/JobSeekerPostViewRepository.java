package vn.flexin.backend.mono.post.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import vn.flexin.backend.mono.post.entity.jobseeker.JobSeekerPostView;

import java.util.List;

public interface JobSeekerPostViewRepository extends JpaRepository<JobSeekerPostView, Long> {
    JobSeekerPostView findByViewer_IdAndPost_Id(Long id, Long id1);

    int countAllByPost_IdIn(List<Long> postIds);

    void deleteAllByPost_Id(Long id);
}
