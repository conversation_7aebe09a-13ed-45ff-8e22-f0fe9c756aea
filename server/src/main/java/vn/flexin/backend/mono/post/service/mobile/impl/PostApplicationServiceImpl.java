package vn.flexin.backend.mono.post.service.mobile.impl;

import lombok.AllArgsConstructor;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.flexin.backend.mono.common.exception.BadRequestException;
import vn.flexin.backend.mono.common.exception.ResourceNotFoundException;
import vn.flexin.backend.mono.post.entity.employer.Post;
import vn.flexin.backend.mono.post.entity.employer.PostApplication;
import vn.flexin.backend.mono.post.enums.PostApplicationStatus;
import vn.flexin.backend.mono.post.repository.PostApplicationRepository;
import vn.flexin.backend.mono.post.service.mobile.PostApplicationService;
import vn.flexin.backend.mono.post.service.mobile.PostService;
import vn.flexin.backend.mono.resume.entity.Resume;
import vn.flexin.backend.mono.resume.service.ResumeService;
import vn.flexin.backend.mono.user.entity.User;
import vn.flexin.backend.mono.user.service.UserService;

import java.util.Optional;

@AllArgsConstructor
@Service
@Transactional
public class PostApplicationServiceImpl implements PostApplicationService {
    private final UserService userService;
    private final PostApplicationRepository postApplicationRepository;
    private final ResumeService resumeService;
    private final PostService postService;

    @Override
    public PostApplication getPostApplicationById(Long id) {

        PostApplication postApplication = postApplicationRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Post application not found"));

        Resume snapshotResume = postApplication.getResume();
        Resume originalResume = resumeService.getOriginalResumeById(snapshotResume.getId());

        if(snapshotResume.getLastModifiedAt().isBefore(originalResume.getLastModifiedAt())) {
            postApplication.setCanUpdateResume(true);
        }

        return postApplication;
    }

    @Override
    public void createPostApplication(Long postId, Long resumeId) {
        Post post = postService.getById(postId);

        User currentLoginUser = userService.getCurrentLoginUser();

        Resume resume = resumeService.getResumeEntityById(resumeId);

        Optional<PostApplication> existPostApplication = postApplicationRepository.findPostApplicationByUserIdAndPostId(currentLoginUser.getId(), postId);
        if(existPostApplication.isPresent()) {
            throw new BadRequestException("You have already applied for this position");
        }

        Long snapshotResumeId = resumeService.snapshotResume(resumeId);
        Resume snapshotResume = resumeService.getResumeEntityById(snapshotResumeId);

        if(!resume.getUser().getId().equals(currentLoginUser.getId())) {
            throw new AccessDeniedException("You can only use your own resume");
        }

        PostApplication postApplication = new PostApplication();
        postApplication.setPost(post);
        postApplication.setResume(snapshotResume);
        postApplication.setStatus(PostApplicationStatus.APPLY.getValue());
        postApplicationRepository.save(postApplication);
    }

    @Override
    public void deletePostApplication(Long postApplicationId) {
        PostApplication postApplication = getPostApplicationById(postApplicationId);

        if(!postApplication.getStatus().equals(PostApplicationStatus.APPLY.getValue())) {
            throw new BadRequestException("You can only delete post application when status is APPLY");
        }

        User currentLoginUser = userService.getCurrentLoginUser();

        if(!postApplication.getResume().getUser().getId().equals(currentLoginUser.getId())) {
            throw new AccessDeniedException("You can only delete your own post application");
        }

        postApplicationRepository.delete(postApplication);
        resumeService.deleteSnapshotResume(postApplication.getResume().getId());
    }

    @Override
    public void updatePostApplicationStatus(Long id, String status) {
        PostApplication postApplication = getPostApplicationById(id);

        postApplication.setStatus(status);
        postApplicationRepository.save(postApplication);
    }

    @Override
    public void updatePostApplicationResume(Long id, Long resumeId) {
        PostApplication postApplication = getPostApplicationById(id);
        Resume currentOriginResume = resumeService.getOriginalResumeById(postApplication.getResume().getId());
        Resume currentSnapshotResume = postApplication.getResume();

        if(!postApplication.getStatus().equals(PostApplicationStatus.APPLY.getValue())) {
            throw new BadRequestException("You can only update post application when status is APPLY");
        }

        User currentLoginUser = userService.getCurrentLoginUser();

        Resume updateResume = resumeService.getResumeEntityById(resumeId);

        if(!updateResume.getUser().getId().equals(currentLoginUser.getId())) {
            throw new AccessDeniedException("You can only update your own post application");
        }

        if(currentOriginResume.getId().equals(resumeId) && currentSnapshotResume.getLastModifiedAt().equals(updateResume.getLastModifiedAt())) {
            throw new BadRequestException("You can not update post application with the same resume");
        }

        resumeService.deleteSnapshotResume(postApplication.getResume().getId());

        Long snapshotResumeId = resumeService.snapshotResume(resumeId);
        Resume snapshotResume = resumeService.getResumeEntityById(snapshotResumeId);

        postApplication.setResume(snapshotResume);
        postApplicationRepository.save(postApplication);
    }
}
