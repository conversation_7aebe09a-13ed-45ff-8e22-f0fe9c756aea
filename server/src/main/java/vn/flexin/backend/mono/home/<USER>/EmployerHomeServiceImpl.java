package vn.flexin.backend.mono.home.service;

import lombok.AllArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.flexin.backend.mono.common.dto.PaginationResponse;
import vn.flexin.backend.mono.company.entity.Branch;
import vn.flexin.backend.mono.company.repository.BranchRepository;
import vn.flexin.backend.mono.home.dto.*;
import vn.flexin.backend.mono.lookup.entity.Lookup;
import vn.flexin.backend.mono.post.repository.PostRepository;
import vn.flexin.backend.mono.resume.repository.ResumeRepository;
import vn.flexin.backend.mono.role.dto.response.RoleResponse;
import vn.flexin.backend.mono.user.entity.User;
import vn.flexin.backend.mono.user.repository.user.UserRepository;


import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Transactional
@AllArgsConstructor
public class EmployerHomeServiceImpl implements EmployerHomeService {

    private final UserRepository userRepository;
    private final ResumeRepository resumeRepository;
    private final PostRepository postRepository;
    private final BranchRepository branchRepository;

    @Override
    public Pair<List<OutstandingCandidateDto>, PaginationResponse> searchOutstandingCandidate(Long branchId, OutstandingCandidateFilter filters) {
        var posts = postRepository.findByBranchId(branchId);

        // Lấy danh sách kỹ năng từ các bài đăng
        Set<String> skills = posts.stream()
                .flatMap(post -> post.getSkills().stream())
                .map(Lookup::getValue) // Giả sử `Lookup::getValue` chứa tên kỹ năng
                .collect(Collectors.toSet());

        // Lọc các resume theo danh sách kỹ năng
        var resumes = resumeRepository.findBySkillsIn(skills);

        // Map các resume thành DTO
        var candidatesResponses = resumes.stream()
                .map(resume -> {
                    User user = resume.getUser(); // Extract the User from Resume
                    return mapToDto(user); // Map User to OutstandingCandidateDto
                })
                .toList();

        // Tạo PaginationResponse
        PaginationResponse paginationResponse = new PaginationResponse(filters.getLimit(),
                filters.getPage(), (int) resumes.size());

        return Pair.of(candidatesResponses, paginationResponse);
    }

    @Override
    public Pair<List<SuitableCandidateDto>, PaginationResponse> searchSuitableCandidate(Long branchId, SuitableCandidateFilter filters) {
        var posts = postRepository.findByBranchId(branchId);

        Set<String> skills = posts.stream()
                .flatMap(post -> post.getSkills().stream())
                .map(Lookup::getValue)
                .collect(Collectors.toSet());

        var resumes = resumeRepository.findBySkillsIn(skills);

        // Map resumes to SuitableCandidateDto
        var candidatesResponses = resumes.stream()
                .map(resume -> {
                    User user = resume.getUser();
                    SuitableCandidateDto candidateDto = mapToSuitableDto(user);

                    // Include availableTimeSlots from PartTimePreference
                    if (resume.getPartTimePreference() != null) {
                        candidateDto.setAvailableTimeSlots(resume.getPartTimePreference().getAvailableTimeSlots());
                        candidateDto.setMinHourlyRate(resume.getPartTimePreference().getMinHourlyRate());
                    }

                    candidateDto.setDescription(resume.getDescription()) ;
                    candidateDto.setLastModifiedAt(resume.getLastModifiedAt());

                    return candidateDto;
                })
                .toList();

        // Tạo PaginationResponse
        PaginationResponse paginationResponse = new PaginationResponse(filters.getLimit(),
                filters.getPage(), (int) resumes.size());

        return Pair.of(candidatesResponses, paginationResponse);
    }

    @Override
    public Pair<List<NearbyCandidateDto>, PaginationResponse> searchNearbyCandidate(Long companyId, NearbyCandidateFilter filters) {
        // Retrieve branches by companyId
        var branches = branchRepository.findByCompanyId(companyId);

        // Extract addresses of the branches
        Set<String> branchAddresses = Collections.emptySet();

        var resumes = resumeRepository.findByPreferredLocationsIn(branchAddresses);

        var candidatesResponses = resumes.stream()
                .map(resume -> {
                    User user = resume.getUser();
                    NearbyCandidateDto candidateDto = mapToNearbyDto(user);

                    // Include availableTimeSlots from PartTimePreference
                    if (resume.getPartTimePreference() != null) {
                        candidateDto.setAvailableTimeSlots(resume.getPartTimePreference().getAvailableTimeSlots());
                        candidateDto.setMinHourlyRate(resume.getPartTimePreference().getMinHourlyRate());
                    }

                    candidateDto.setDescription(resume.getDescription()) ;
                    candidateDto.setLastModifiedAt(resume.getLastModifiedAt());

                    return candidateDto;
                })
                .toList();

        PaginationResponse paginationResponse = new PaginationResponse(filters.getLimit(),
                filters.getPage(), (int) resumes.size());

        return Pair.of(candidatesResponses, paginationResponse);
    }

    private OutstandingCandidateDto mapToDto(User user) {
        OutstandingCandidateDto candidateDto = new OutstandingCandidateDto();
        candidateDto.setId(user.getId());
        candidateDto.setName(user.getName());
        candidateDto.setRole(user.getRole());
        candidateDto.setPhoneNumber(user.getPhoneNumber());
        candidateDto.setProfilePicture(user.getProfilePicture());
        candidateDto.setActive(user.isActive());
        candidateDto.setUlid(user.getUlid());
        candidateDto.setCreatedAt(user.getCreatedAt());

        Set<RoleResponse> roles = user.getRoles().stream().map(role -> new RoleResponse(role.getId(), role.getName())).collect(Collectors.toSet());
        candidateDto.setRoles(roles);
        return candidateDto;
    }

    private SuitableCandidateDto mapToSuitableDto(User user) {
        SuitableCandidateDto candidateDto = new SuitableCandidateDto();
        candidateDto.setId(user.getId());
        candidateDto.setName(user.getName());
        candidateDto.setProfilePicture(user.getProfilePicture());
        candidateDto.setActive(user.isActive());
        candidateDto.setUlid(user.getUlid());
        candidateDto.setCreatedAt(user.getCreatedAt());

        return candidateDto;
    }

    private NearbyCandidateDto mapToNearbyDto(User user) {
        NearbyCandidateDto candidateDto = new NearbyCandidateDto();
        candidateDto.setId(user.getId());
        candidateDto.setName(user.getName());
        candidateDto.setProfilePicture(user.getProfilePicture());
        candidateDto.setActive(user.isActive());
        candidateDto.setUlid(user.getUlid());
        candidateDto.setCreatedAt(user.getCreatedAt());

        return candidateDto;
    }

} 