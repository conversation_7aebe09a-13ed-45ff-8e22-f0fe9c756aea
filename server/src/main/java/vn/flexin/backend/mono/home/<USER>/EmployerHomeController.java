package vn.flexin.backend.mono.home.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.flexin.backend.mono.common.dto.PaginationApiResponseDto;
import vn.flexin.backend.mono.home.dto.*;

import java.util.List;

@Tag(name = "Home Employer", description = "Home employer endpoints")
@RequestMapping("/v1/home/<USER>")
public interface EmployerHomeController {
    @Operation(summary = "Outstanding candidate list employer home", description = "Retrieves a paginated list of Outstanding candidate with filtering options.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Outstanding candidate fetched successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "400", description = "Invalid input")
    })
    @PostMapping("/outstanding-candidate/{branchId}")
    ResponseEntity<PaginationApiResponseDto<List<OutstandingCandidateDto>>> searchOutstandingCandidate(@PathVariable(
            "branchId") Long branchId, @Valid @RequestBody OutstandingCandidateFilter filters);

    @Operation(summary = "Suitable candidate list employer home", description = "Retrieves a paginated list of Suitable candidate with filtering options.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Suitable candidate fetched successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "400", description = "Invalid input")
    })
    @PostMapping("/suitable-candidate/{branchId}")
    ResponseEntity<PaginationApiResponseDto<List<SuitableCandidateDto>>> searchSuitableCandidate(@PathVariable(
            "branchId") Long branchId, @Valid @RequestBody SuitableCandidateFilter filters);

    @Operation(summary = "Nearby candidate list employer home", description = "Retrieves a paginated list of Nearby" +
            " candidate with filtering options.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Nearby candidate fetched successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "400", description = "Invalid input")
    })
    @PostMapping("/nearby-candidate/{branchId}")
    ResponseEntity<PaginationApiResponseDto<List<NearbyCandidateDto>>> searchNearbyCandidate(@PathVariable(
            "branchId") Long branchId, @Valid @RequestBody NearbyCandidateFilter filters);

}
