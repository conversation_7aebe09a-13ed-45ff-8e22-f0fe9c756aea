package vn.flexin.backend.mono.user.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import jakarta.persistence.*;
import vn.flexin.backend.mono.common.entity.AbstractAuditingEntity;
import vn.flexin.backend.mono.resume.entity.Resume;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

@Entity
@Table(name = "t_part_time_preferences")
@EntityListeners(AuditingEntityListener.class)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PartTimePreference extends AbstractAuditingEntity<Long> {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private Double minHourlyRate;

    private Integer maxHoursPerWeek;

    @ElementCollection
    @CollectionTable(name = "t_part_time_preference_days", joinColumns = @JoinColumn(name = "preference_id"))
    @Column(name = "available_day")
    private Set<String> availableDays = new HashSet<>();

    @ElementCollection
    @CollectionTable(name = "t_part_time_preference_time_slots", joinColumns = @JoinColumn(name = "preference_id"))
    @Column(name = "time_slot")
    private Set<String> availableTimeSlots = new HashSet<>();

    @ElementCollection
    @CollectionTable(name = "t_part_time_preference_job_types", joinColumns = @JoinColumn(name = "preference_id"))
    @Column(name = "job_type")
    private Set<String> preferredJobTypes = new HashSet<>();

    @ElementCollection
    @CollectionTable(name = "t_part_time_preference_locations", joinColumns = @JoinColumn(name = "preference_id"))
    @Column(name = "location")
    private Set<String> preferredLocations = new HashSet<>();

    private Boolean remoteOnly = false;

    private Boolean isStudent;

    private String studyMajor;

    private Integer maxTravelDistance;

    @Column(columnDefinition = "TEXT")
    private String additionalNotes;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "resume_id")
    @JsonIgnore
    private Resume resume;
} 