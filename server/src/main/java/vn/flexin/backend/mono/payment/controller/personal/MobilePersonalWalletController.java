package vn.flexin.backend.mono.payment.controller.personal;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.common.dto.PaginationApiResponseDto;
import vn.flexin.backend.mono.payment.dto.request.PaymentTransactionRequest;
import vn.flexin.backend.mono.payment.dto.request.PersonalTransactionFilter;
import vn.flexin.backend.mono.payment.dto.request.RedeemPointRequest;
import vn.flexin.backend.mono.payment.dto.response.PersonalTransactionResponse;
import vn.flexin.backend.mono.payment.dto.response.PersonalWalletResponse;

import java.util.List;

@Tag(name = "Personal wallet APIs", description = "Personal wallet and payment transaction End points")
@RequestMapping("/v1/mobile/payment/personal")
public interface MobilePersonalWalletController {
    @Operation(
            summary = "Get wallet information for a user",
            description = "Retrieves wallet details for the specified user."
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Wallet information retrieved successfully"),
            @ApiResponse(responseCode = "404", description = "User not found")
    })
    @GetMapping("/me")
    ResponseEntity<ApiResponseDto<PersonalWalletResponse>> getWalletInfo();

    @Operation(
            summary = "Redeem points",
            description = "Allows a user to redeem points from their wallet."
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Points redeemed successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PostMapping("/redeem")
    ResponseEntity<ApiResponseDto<Boolean>> redeemPoints(@RequestBody RedeemPointRequest request);

    @Operation(
            summary = "Request payment",
            description = "Creates a payment request for the specified user."
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Payment request created successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PostMapping("/payment-request")
    ResponseEntity<ApiResponseDto<Boolean>> requestPayment(@RequestBody PaymentTransactionRequest request);

    @Operation(
            summary = "Update payment status",
            description = "Updates the status of a payment request based on transaction ID."
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Payment status updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    ResponseEntity<ApiResponseDto<Boolean>> updatePaymentStatus( @RequestParam String thirdPartyTransactionId,
                                                                 @RequestParam String status);

    @Operation(
            summary = "Get list transaction of user's wallet",
            description = "Get list transaction of user's wallet"
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Get list transactions successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PostMapping("/transactions")
    ResponseEntity<PaginationApiResponseDto<List<PersonalTransactionResponse>>> getListTransaction(@RequestBody PersonalTransactionFilter request);
}
