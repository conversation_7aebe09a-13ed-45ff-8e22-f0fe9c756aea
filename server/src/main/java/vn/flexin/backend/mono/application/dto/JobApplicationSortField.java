package vn.flexin.backend.mono.application.dto;

public enum JobApplicationSortField {
    ID("id"),
    JOB_ID("job.id"),
    JOB_TITLE("job.title"),
    JOB_SEEKER_ID("jobSeeker.id"),
    JO<PERSON>_SEEKER_NAME("jobSeeker.name"),
    STATUS("status"),
    CREATED_AT("createdAt"),
    UPDATED_AT("updatedAt");
    
    private final String fieldName;
    
    JobApplicationSortField(String fieldName) {
        this.fieldName = fieldName;
    }
    
    public String getFieldName() {
        return fieldName;
    }
} 