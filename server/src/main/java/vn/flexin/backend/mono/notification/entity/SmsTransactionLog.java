package vn.flexin.backend.mono.notification.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import vn.flexin.backend.mono.common.entity.AbstractAuditingEntity;
import vn.flexin.backend.mono.notification.enums.TransactionStatus;

import java.time.LocalDateTime;

@Getter
@Setter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "t_sms_transaction_logs")
public class SmsTransactionLog extends AbstractAuditingEntity<Long> {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "transaction_id")
    private SmsTransaction transaction;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "sms_id")
    private Sms sms;

    private String status;

    private LocalDateTime receiveTime;

    public SmsTransactionLog(SmsTransaction transaction, Sms sms) {
        this.transaction = transaction;
        this.sms = sms;
        this.status = sms.isSuccess() ? TransactionStatus.SUCCESS.value() : TransactionStatus.FAILED.value();
    }
}
