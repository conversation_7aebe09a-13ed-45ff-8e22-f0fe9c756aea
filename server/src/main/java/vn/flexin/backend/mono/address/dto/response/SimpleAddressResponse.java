package vn.flexin.backend.mono.address.dto.response;

import lombok.Data;
import vn.flexin.backend.mono.address.entity.Address;

@Data
public class SimpleAddressResponse {
    private Long id;
    private Long provinceCode;
    private Long districtCode;
    private Long wardCode;
    private String detailAddress;
    private String provinceName;
    private String districtName;
    private String wardName;

    public SimpleAddressResponse(Address address) {
        this.id = address.getId();
        if (address.getProvince() != null) {
            this.provinceCode = address.getProvince().getCode();
            this.provinceName = address.getProvince().getName();
        }
        if (address.getDistrict() != null) {
            this.districtCode = address.getDistrict().getCode();
            this.districtName = address.getDistrict().getName();
        }
        if (address.getWard() != null) {
            this.wardCode = address.getWard().getCode();
            this.wardName = address.getWard().getName();
        }
        this.detailAddress = address.getDetailAddress();
    }
}
