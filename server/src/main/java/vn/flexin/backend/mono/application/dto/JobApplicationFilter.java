package vn.flexin.backend.mono.application.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.flexin.backend.mono.user.dto.FilterOperator;

import jakarta.validation.constraints.NotNull;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class JobApplicationFilter {
    @NotNull
    private JobApplicationFilterField field;
    
    private List<String> values;
    
    private FilterOperator operator;
} 