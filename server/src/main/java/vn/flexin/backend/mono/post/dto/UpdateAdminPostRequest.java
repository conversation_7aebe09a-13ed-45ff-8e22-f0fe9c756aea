package vn.flexin.backend.mono.post.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.validation.Valid;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Getter
@Setter
@EqualsAndHashCode(callSuper = false)
public class UpdateAdminPostRequest {
    @Hidden
    private Long id;

    private String title;

    private String description;

    private String location;

    private Long branchId;

    @Valid
    private SalaryDto salary;

    private String status;

    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
    private LocalDateTime postDate;

    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
    private LocalDateTime expireDate;

    @JsonProperty("isFeatureJob")
    private boolean isFeatureJob;

    @JsonProperty("urgentHiring")
    private boolean urgentHiring;

    private List<PostWorkingInformation> workingInformation;

    private String workType;

    private String jobType;

    @Valid
    private ExperienceDto experience;

    private String positions;

    private List<String> skills;

    private List<String> benefits;

    private List<PostRequiredDocument> requiredDocuments;

    public Boolean getIsFeatureJob() {
        return isFeatureJob;
    }

    public Boolean getUrgentHiring() {
        return urgentHiring;
    }

    private String experienceLevel;
}
