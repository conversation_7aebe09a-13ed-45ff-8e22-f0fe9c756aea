package vn.flexin.backend.mono.file.utils;

import org.springframework.http.MediaTypeFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.MimeType;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class FileUtil {

    private FileUtil() {
        throw new IllegalStateException("Utility class");
    }
    private static final String PNG_IMAGE_FILE_TYPE = "image/png";
    private static final String JPEG_IMAGE_FILE_TYPE = "image/jpeg";
    private static final String GIF_IMAGE_FILE_TYPE = "image/gif";
    private static final String SVG_IMAGE_FILE_TYPE = "image/svg+xml";
    private static final String MP3_FILE_TYPE = "audio/mpeg";
    private static final String MP4_FILE_TYPE = "video/mp4";
    private static final String PDF_FILE_TYPE = "application/pdf";

    private static final String WORD_FILE_TYPE = "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
    private static final String OLD_WORD_FILE_TYPE = "application/msword";
    private static final String EXCEL_FILE_TYPE = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
    private static final String OLD_EXCEL_FILE_TYPE = "application/vnd.ms-excel";
    private static final String POWER_POINT_FILE_TYPE = "application/vnd.openxmlformats-officedocument.presentationml.presentation";
    private static final String OLD_POWER_POINT_FILE_TYPE = "application/vnd.ms-powerpoint";

    private static final Map<String, String> CONTENT_TYPES = new ConcurrentHashMap<>();
    
    private static final List<String> fileTypes = new ArrayList<>();

    private static final List<MimeType> mimeTypes;

    static {
        CONTENT_TYPES.put(".mp4", MP4_FILE_TYPE);
        CONTENT_TYPES.put(".mp3", MP3_FILE_TYPE);
        CONTENT_TYPES.put(".jpg", JPEG_IMAGE_FILE_TYPE);
        CONTENT_TYPES.put(".jpeg", JPEG_IMAGE_FILE_TYPE);
        CONTENT_TYPES.put(".png", PNG_IMAGE_FILE_TYPE);
        CONTENT_TYPES.put(".gif", GIF_IMAGE_FILE_TYPE);
        CONTENT_TYPES.put(".svg", SVG_IMAGE_FILE_TYPE);
        CONTENT_TYPES.put(".pdf", PDF_FILE_TYPE);
        CONTENT_TYPES.put(".docx", WORD_FILE_TYPE);
        CONTENT_TYPES.put(".doc", OLD_WORD_FILE_TYPE);
        CONTENT_TYPES.put(".xlsx", EXCEL_FILE_TYPE);
        CONTENT_TYPES.put(".xls", OLD_EXCEL_FILE_TYPE);
        CONTENT_TYPES.put(".pptx", POWER_POINT_FILE_TYPE);
        CONTENT_TYPES.put(".ppt", OLD_POWER_POINT_FILE_TYPE);
        
        fileTypes.add(PNG_IMAGE_FILE_TYPE);
        fileTypes.add(JPEG_IMAGE_FILE_TYPE);
        fileTypes.add(GIF_IMAGE_FILE_TYPE);
        fileTypes.add(WORD_FILE_TYPE);
        fileTypes.add(MP4_FILE_TYPE);
        fileTypes.add(MP3_FILE_TYPE);
        fileTypes.add(PDF_FILE_TYPE);
        fileTypes.add(OLD_WORD_FILE_TYPE);
        fileTypes.add(EXCEL_FILE_TYPE);
        fileTypes.add(OLD_EXCEL_FILE_TYPE);
        fileTypes.add(POWER_POINT_FILE_TYPE);
        fileTypes.add(OLD_POWER_POINT_FILE_TYPE);

        mimeTypes = fileTypes.stream().map(MimeType::valueOf).toList();
        
    }

    public static boolean validateFileType(MultipartFile file) {
        var fileType = MediaTypeFactory.getMediaType(file.getOriginalFilename());
        if (CollectionUtils.isEmpty(mimeTypes)) {
            return true;
        }
        return fileType.filter(mimeTypes::contains).isPresent();
    }

    public static String getFileType(MultipartFile file) {
        String path = file.getOriginalFilename();
        if (path == null || path.isEmpty()) {
            return null;
        }
        String extension = path.substring(path.lastIndexOf("."));
        if (extension.isEmpty()) {
            return null;
        }
        return CONTENT_TYPES.get(extension);
    }
}
