package vn.flexin.backend.mono.permission.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.jpa.domain.Specification;
import vn.flexin.backend.mono.common.dto.BaseFilter;
import vn.flexin.backend.mono.common.repository.param.Condition;
import vn.flexin.backend.mono.common.repository.param.Operator;
import vn.flexin.backend.mono.common.repository.param.Where;
import vn.flexin.backend.mono.common.util.SpecificationUtil;
import vn.flexin.backend.mono.permission.entity.Permission;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PermissionFilter extends BaseFilter<Permission> {

    private String keyword;
    private String module;
    private String action;

    @Override
    public Specification<Permission> toSpecification() {
        Condition condition = new Condition();
        if (module != null && !module.isEmpty()) {
            condition.append(new Where(Permission.Fields.name, Operator.LIKE, module));
        }
        if (action != null && !action.isEmpty()) {
            condition.append(new Where(Permission.Fields.name, Operator.LIKE, action));
        }

        if (keyword != null && !keyword.isEmpty()) {
            condition.append(new Where(Permission.Fields.name, Operator.LIKE, keyword));
        }
        return SpecificationUtil.bySearchQuery(condition);
    }
}
