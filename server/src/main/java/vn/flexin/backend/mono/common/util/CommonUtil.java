package vn.flexin.backend.mono.common.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.keycloak.representations.idm.UserRepresentation;
import org.springframework.data.util.Pair;
import org.springframework.web.servlet.support.RequestContext;

import java.security.SecureRandom;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static vn.flexin.backend.mono.common.util.ValidPassword.MINIMUM_PASSWORD_LENGTH;
import static vn.flexin.backend.mono.common.util.ValidPassword.PASSWORD_PATTERN;

public class CommonUtil {

    private static final SecureRandom SECURE_RANDOM = new SecureRandom();

    private static final Random RANDOM = new Random();

    static {
        SECURE_RANDOM.nextBytes(new byte[64]);
    }

    public static String genOtp() {
        // Using numeric values
        String numbers = "0123456789";

        char[] otp = new char[6];

        for (int i = 0; i < 6; i++) {
            otp[i] = numbers.charAt(RANDOM.nextInt(numbers.length()));
        }
        return new String(otp);
    }

    public static String formatPhoneNumber(String phone) {
        if (vn.flexin.backend.mono.common.util.StringUtils.isEmpty(phone)) {
            return "";
        }
        if (phone.startsWith("84")) {
            return phone;
        }
        if (phone.startsWith("0")) {
            return phone.replaceFirst("0", "84");
        }
        return phone;
    }

    private static final Pattern EMAIL_PATTERN = Pattern.compile(ValidEmail.PATTERN);

    public static boolean isValidPassword(final String password) {
        Matcher matcher = PASSWORD_PATTERN.matcher(password);
        return matcher.matches();
    }

    public static boolean isValidEmail(final String email) {
        Matcher matcher = EMAIL_PATTERN.matcher(email);
        return matcher.matches();
    }

    public static Date endOfDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }

    public static Date startOfDay(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    public static String toJsonString(Object object) throws JsonProcessingException {
        return new ObjectMapper().writeValueAsString(object);
    }

    public static <T> T fromJsonString(String json, TypeReference<T> type) throws JsonProcessingException {
        return new ObjectMapper().readValue(json, type);
    }

    public static int getRandomNumber(int min, int max) {
        Random rand = new Random();
        int range = max - min + 1;
        return min + rand.nextInt(range);
    }

    private static final char[] SPECIAL_CHARS = new char[]{'!', '@', '#', '&', '(', ')', '–'};

    public static String getRandomSpecialChar() {
        int randomNumber = getRandomNumber(0, 6);
        return String.valueOf(SPECIAL_CHARS[randomNumber]);
    }

    public static String getRandomLowercaseLetter() {
        return Character.toString((char) getRandomNumber(97, 122));
    }

    public static String getRandomUppercaseLetter() {
        return Character.toString((char) getRandomNumber(65, 90));
    }

    public static String getRandomDigit() {
        return Character.toString((char) getRandomNumber(48, 57));
    }

    public static String generatePassword() {
        StringBuilder password = new StringBuilder();
        for (int i = 0; i < MINIMUM_PASSWORD_LENGTH; i++) {
            if (i % 4 == 0) {
                password.append(getRandomLowercaseLetter());
            }
            if (i % 4 == 1) {
                password.append(getRandomUppercaseLetter());
            }
            if (i % 4 == 2) {
                password.append(getRandomDigit());
            }
            if (i % 4 == 3) {
                password.append(getRandomSpecialChar());
            }
        }
        return password.toString();
    }


    public static String getFirstName(String fullName) {
        if (fullName == null) {
            return null;
        }
        String[] nameParts = fullName.split(" ");
        if (nameParts.length == 0) {
            return "";
        }
        String firstName = "";
        for (int i = 0; i < nameParts.length; i++) {
            if (!StringUtils.isBlank(nameParts[i])) {
                firstName = nameParts[i].trim();
                break;
            }
        }
        return firstName;
    }

    public static String getLastName(String fullName) {
        if (fullName == null) {
            return null;
        }
        String[] nameParts = fullName.split(" ");
        if (nameParts.length == 0) {
            return "";
        }
        String lastName = "";
        for (int i = nameParts.length - 1; i >= 0; i--) {
            if (!StringUtils.isBlank(nameParts[i])) {
                lastName = nameParts[i].trim();
                break;
            }
        }
        return lastName;
    }

    public static LocalDateTime getCurrentUTCTime() {
        return ZonedDateTime.now(Constant.UTC_ZONE).toLocalDateTime();
    }

    public static Pair<String, String> getDeviceIdAndPhoneNumber(UserRepresentation currentUser) {
        Map<String, List<String>> customAttributes = currentUser.getAttributes();
        String deviceId = customAttributes != null && customAttributes.containsKey("deviceId") ?
                customAttributes.get("deviceId").getFirst() : "";
        String phoneNumber = customAttributes != null && customAttributes.containsKey("phoneNumber") ?
                customAttributes.get("phoneNumber").getFirst() : "";
        return Pair.of(deviceId, phoneNumber);
    }

    public static String generateRandomAlphanumericString() {
        return RandomStringUtils.random(20, 0, 0, true, true, (char[])null, SECURE_RANDOM);
    }

    public static String getDateTimeByFormat(LocalDateTime time, String format) {
        try
        {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
            return time.format(formatter);
        }
        catch (Exception ex)
        {
            return null;
        }
    }

    public static LocalDateTime convertTimestampToLocalDateTime(String timestamp) {
        try {
            long epochSeconds = Long.parseLong(timestamp);

            Instant instant = Instant.ofEpochSecond(epochSeconds);

            return LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
        } catch (NumberFormatException e) {
            return null;
        }
    }
}
