package vn.flexin.backend.mono.notification.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import vn.flexin.backend.mono.common.entity.AbstractAuditingEntity;
import vn.flexin.backend.mono.notification.enums.SmsType;

@Getter
@Setter
@ToString
@Entity
@NoArgsConstructor
@Table(name = "t_sms_templates")
public class SmsTemplate extends AbstractAuditingEntity<Long> {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String content;

    private String contentEng;

    @Enumerated(EnumType.STRING)
    private SmsType type;

}
