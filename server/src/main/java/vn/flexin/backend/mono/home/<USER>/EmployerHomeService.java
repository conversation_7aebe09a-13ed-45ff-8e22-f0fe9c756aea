package vn.flexin.backend.mono.home.service;

import org.apache.commons.lang3.tuple.Pair;
import vn.flexin.backend.mono.auth.dto.SelectRoleRequest;
import vn.flexin.backend.mono.common.dto.PaginationResponse;
import vn.flexin.backend.mono.home.dto.*;
import vn.flexin.backend.mono.user.dto.*;
import vn.flexin.backend.mono.user.entity.User;
import vn.flexin.backend.mono.user.entity.UserDevice;

import java.util.List;

public interface EmployerHomeService {
    Pair<List<OutstandingCandidateDto>, PaginationResponse> searchOutstandingCandidate(Long branchId, OutstandingCandidateFilter filters);

    Pair<List<SuitableCandidateDto>, PaginationResponse> searchSuitableCandidate(Long branchId, SuitableCandidateFilter filters);

    Pair<List<NearbyCandidateDto>, PaginationResponse> searchNearbyCandidate(Long branchId,
                                                                             NearbyCandidateFilter filters);
}

