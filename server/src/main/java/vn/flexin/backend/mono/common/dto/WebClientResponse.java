package vn.flexin.backend.mono.common.dto;

import lombok.*;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WebClientResponse {

    private HttpHeaders headers;
    private MediaType contentType;
    private HttpStatus httpStatus;
    private String errorMessage;
    private Object data;
}
