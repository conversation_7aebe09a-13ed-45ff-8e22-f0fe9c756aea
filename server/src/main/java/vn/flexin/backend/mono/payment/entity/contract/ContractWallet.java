package vn.flexin.backend.mono.payment.entity.contract;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import vn.flexin.backend.mono.common.entity.AbstractAuditingEntity;

import java.math.BigDecimal;

@Getter
@Setter
//@Entity
//@Table(name = "t_contract_wallets")
public class ContractWallet extends AbstractAuditingEntity<Long> {
//    @Id
//    @GeneratedValue(strategy = GenerationType.IDENTITY)
//    private Long id;
//
//    @Column(nullable = false)
//    private String contractId;
//
//    @Column(nullable = false)
//    private BigDecimal balance;
}
