package vn.flexin.backend.mono.job.service;

import vn.flexin.backend.mono.job.dto.JobDto;
import vn.flexin.backend.mono.job.dto.SearchJobRequest;
import vn.flexin.backend.mono.common.exception.ResourceNotFoundException;
import vn.flexin.backend.mono.job.entity.Job;
import vn.flexin.backend.mono.user.entity.User;
import vn.flexin.backend.mono.job.repository.JobRepository;
import vn.flexin.backend.mono.user.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class JobServiceImpl implements JobService {

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private UserService userService;

    @Override
    public JobDto createJob(JobDto jobDto) {
        User employer = userService.getUserEntityById(jobDto.getEmployerId());
        
        Job job = new Job();
        job.setTitle(jobDto.getTitle());
        job.setDescription(jobDto.getDescription());
        job.setEmployer(employer);
        job.setLocation(jobDto.getLocation());
        job.setRemote(jobDto.isRemote());
        job.setHourlyRate(jobDto.getHourlyRate());
        job.setJobType(jobDto.getJobType());
        job.setHoursPerWeek(jobDto.getHoursPerWeek());
        job.setWorkDays(jobDto.getWorkDays());
        job.setRequiredSkills(jobDto.getRequiredSkills());
        job.setStartDate(jobDto.getStartDate());
        job.setEndDate(jobDto.getEndDate());
        job.setStatus(jobDto.getStatus());
        job.setFeatured(jobDto.isFeatured());
        
        Job savedJob = jobRepository.save(job);
        return mapToDto(savedJob);
    }

    @Override
    public JobDto getJobById(Long id) {
        Job job = getJobEntityById(id);
        return mapToDto(job);
    }

    @Override
    public List<JobDto> getAllJobs() {
        List<Job> jobs = jobRepository.findAll();
        return jobs.stream().map(this::mapToDto).collect(Collectors.toList());
    }

    @Override
    public List<JobDto> getJobsByEmployerId(Long employerId) {
        User employer = userService.getUserEntityById(employerId);
        List<Job> jobs = jobRepository.findByEmployer(employer);
        return jobs.stream().map(this::mapToDto).collect(Collectors.toList());
    }

    @Override
    public List<JobDto> getJobsByStatus(String status) {
        List<Job> jobs = jobRepository.findByStatus(status);
        return jobs.stream().map(this::mapToDto).collect(Collectors.toList());
    }

    @Override
    public List<JobDto> getJobsByEmployerIdAndStatus(Long employerId, String status) {
        User employer = userService.getUserEntityById(employerId);
        List<Job> jobs = jobRepository.findByEmployerAndStatus(employer, status);
        return jobs.stream().map(this::mapToDto).collect(Collectors.toList());
    }

    @Override
    public List<JobDto> searchJobs(String keyword) {
        List<Job> jobs = jobRepository.searchJobs(keyword);
        return jobs.stream().map(this::mapToDto).collect(Collectors.toList());
    }

    @Override
    public List<JobDto> findJobsByLocation(String location) {
        List<Job> jobs = jobRepository.findByLocation(location);
        return jobs.stream().map(this::mapToDto).collect(Collectors.toList());
    }

    @Override
    public List<JobDto> findJobsByJobType(String jobType) {
        List<Job> jobs = jobRepository.findByJobType(jobType);
        return jobs.stream().map(this::mapToDto).collect(Collectors.toList());
    }

    @Override
    public List<JobDto> findJobsByHourlyRateRange(Double minRate, Double maxRate) {
        List<Job> jobs = jobRepository.findByHourlyRateRange(minRate, maxRate);
        return jobs.stream().map(this::mapToDto).collect(Collectors.toList());
    }

    @Override
    public JobDto updateJob(Long id, JobDto jobDto) {
        Job job = getJobEntityById(id);
        
        job.setTitle(jobDto.getTitle());
        job.setDescription(jobDto.getDescription());
        job.setLocation(jobDto.getLocation());
        job.setRemote(jobDto.isRemote());
        job.setHourlyRate(jobDto.getHourlyRate());
        job.setJobType(jobDto.getJobType());
        job.setHoursPerWeek(jobDto.getHoursPerWeek());
        job.setWorkDays(jobDto.getWorkDays());
        job.setRequiredSkills(jobDto.getRequiredSkills());
        job.setStartDate(jobDto.getStartDate());
        job.setEndDate(jobDto.getEndDate());
        job.setStatus(jobDto.getStatus());
        job.setFeatured(jobDto.isFeatured());
        
        Job updatedJob = jobRepository.save(job);
        return mapToDto(updatedJob);
    }

    @Override
    public void deleteJob(Long id) {
        Job job = getJobEntityById(id);
        jobRepository.delete(job);
    }

    @Override
    public Job getJobEntityById(Long id) {
        return jobRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Job", "id", id));
    }

    @Override
    public Page<JobDto> searchJobs(SearchJobRequest searchJobRequest) {
        Integer pageNumber = searchJobRequest.getPageNumber();
        Integer numberElementInPage = searchJobRequest.getPageSize();
        String keyword = searchJobRequest.getKeyword();
        List<vn.flexin.backend.mono.job.dto.JobFilter> filters = searchJobRequest.getFilters();
        vn.flexin.backend.mono.job.dto.JobSortField sortBy = searchJobRequest.getSortBy();
        Boolean ascending = searchJobRequest.getAscending();
        
        Pageable pageable = PageRequest.of(pageNumber - 1, numberElementInPage);
        
        Page<Job> jobPage = jobRepository.searchJobs(keyword, filters, pageable, sortBy, ascending);
        
        return jobPage.map(this::mapToDto);
    }

    private JobDto mapToDto(Job job) {
        JobDto jobDto = new JobDto();
        jobDto.setId(job.getId());
        jobDto.setTitle(job.getTitle());
        jobDto.setDescription(job.getDescription());
        jobDto.setEmployerId(job.getEmployer().getId());
        jobDto.setEmployerName(job.getEmployer().getName());
        jobDto.setLocation(job.getLocation());
        jobDto.setRemote(job.isRemote());
        jobDto.setHourlyRate(job.getHourlyRate());
        jobDto.setJobType(job.getJobType());
        jobDto.setHoursPerWeek(job.getHoursPerWeek());
        jobDto.setWorkDays(job.getWorkDays());
        jobDto.setRequiredSkills(job.getRequiredSkills());
        jobDto.setStartDate(job.getStartDate());
        jobDto.setEndDate(job.getEndDate());
        jobDto.setStatus(job.getStatus());
        jobDto.setFeatured(job.isFeatured());
        return jobDto;
    }
} 