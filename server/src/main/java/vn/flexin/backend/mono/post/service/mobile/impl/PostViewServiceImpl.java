package vn.flexin.backend.mono.post.service.mobile.impl;

import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.flexin.backend.mono.common.exception.ResourceNotFoundException;
import vn.flexin.backend.mono.post.entity.employer.Post;
import vn.flexin.backend.mono.post.entity.employer.PostView;
import vn.flexin.backend.mono.post.repository.PostRepository;
import vn.flexin.backend.mono.post.repository.PostViewRepository;
import vn.flexin.backend.mono.post.service.mobile.PostViewService;
import vn.flexin.backend.mono.user.entity.User;
import vn.flexin.backend.mono.user.repository.user.UserRepository;
import vn.flexin.backend.mono.user.service.UserService;

@AllArgsConstructor
@Service
@Transactional
public class PostViewServiceImpl implements PostViewService {
    private final PostViewRepository postViewRepository;
    private final PostRepository postRepository;
    private final UserRepository userRepository;
    private final UserService userService;


    @Override
    public void createPostView(Long postId) {
        Post post = postRepository.findById(postId)
                .orElseThrow(() -> new ResourceNotFoundException("Post not found"));

        User currentLoginUser = userService.getCurrentLoginUser();
        User viewer = userRepository.findById(currentLoginUser.getId())
                .orElseThrow(() -> new ResourceNotFoundException("Viewer not found"));

        PostView postView = new PostView();
        postView.setPost(post);
        postView.setViewer(viewer);

        postViewRepository.save(postView);
    }
}
