package vn.flexin.backend.mono.address.controller.impl;

import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import vn.flexin.backend.mono.address.controller.AddressController;
import vn.flexin.backend.mono.address.dto.response.DistrictResponse;
import vn.flexin.backend.mono.address.dto.response.ProvinceResponse;
import vn.flexin.backend.mono.address.dto.response.WardResponse;
import vn.flexin.backend.mono.address.job.CrawlDataService;
import vn.flexin.backend.mono.address.service.AddressService;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.common.exception.BadRequestException;

import java.util.List;

@RestController
@AllArgsConstructor
public class AddressControllerImpl implements AddressController {
    private final AddressService addressService;
    private final CrawlDataService crawlDataService;

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> syncData() {
        try {
            boolean success = crawlDataService.syncData();
            return ResponseEntity.ok(ApiResponseDto.success(success, "Data synchronization completed successfully"));
        } catch (Exception e) {
            throw new BadRequestException(e.getMessage());
        }
    }

    @Override
    public ResponseEntity<ApiResponseDto<List<ProvinceResponse>>> getAllProvinces(String name) {
        List<ProvinceResponse> provinces = addressService.getAllProvinces(name);
        return ResponseEntity.ok(ApiResponseDto.success(provinces, null));
    }

    @Override
    public ResponseEntity<ApiResponseDto<ProvinceResponse>> getProvinceByCode(Long code) {
        try {
            ProvinceResponse province = addressService.getProvinceByCode(code);
            return ResponseEntity.ok(ApiResponseDto.success(province, null));
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @Override
    public ResponseEntity<ApiResponseDto<List<DistrictResponse>>> getDistrictsByProvinceCode(Long provinceCode, String name) {
        try {
            List<DistrictResponse> districts = addressService.getDistrictsByProvinceCode(provinceCode, name);
            return ResponseEntity.ok(ApiResponseDto.success(districts, null));
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @Override
    public ResponseEntity<ApiResponseDto<DistrictResponse>> getDistrictByCode(Long code) {
        try {
            DistrictResponse district = addressService.getDistrictByCode(code);
            return ResponseEntity.ok(ApiResponseDto.success(district, null));
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }

    @Override
    public ResponseEntity<ApiResponseDto<List<WardResponse>>> getWardsByDistrictCode(Long districtCode, String name) {
        try {
            List<WardResponse> wards = addressService.getWardsByDistrictCode(districtCode, name);
            return ResponseEntity.ok(ApiResponseDto.success(wards, null));
        } catch (RuntimeException e) {
            return ResponseEntity.notFound().build();
        }
    }
}
