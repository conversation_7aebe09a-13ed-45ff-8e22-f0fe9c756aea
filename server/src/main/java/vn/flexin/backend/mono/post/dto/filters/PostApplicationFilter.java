package vn.flexin.backend.mono.post.dto.filters;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.springframework.data.jpa.domain.Specification;
import vn.flexin.backend.mono.common.dto.BaseFilter;
import vn.flexin.backend.mono.common.repository.param.Condition;
import vn.flexin.backend.mono.common.repository.param.Join;
import vn.flexin.backend.mono.common.repository.param.Operator;
import vn.flexin.backend.mono.common.repository.param.Where;
import vn.flexin.backend.mono.common.util.SpecificationUtil;
import vn.flexin.backend.mono.post.entity.Post;
import vn.flexin.backend.mono.post.entity.PostApplication;
import vn.flexin.backend.mono.resume.entity.Resume;
import vn.flexin.backend.mono.user.entity.User;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class PostApplicationFilter extends BaseFilter<PostApplication> {

    private Long postId;
    private Long userId;
    private String status;

    @Override
    public Specification<PostApplication> toSpecification() {

        var condition = new Condition();

        if(userId != null){
            var nestedJoin = new Join(Resume.Fields.user, List.of(new Where(User.Fields.id, userId)));
            condition.append(new Join(PostApplication.Fields.resume, nestedJoin));
        }
        if(postId != null){
            condition.append(new Join(PostApplication.Fields.post, List.of(new Where(Post.Fields.id, postId))));
        }
        if(status != null){
            condition.append(new Where(PostApplication.Fields.status, Operator.EQUAL, status));
        }

        return SpecificationUtil.bySearchQuery(condition);
    }
}
