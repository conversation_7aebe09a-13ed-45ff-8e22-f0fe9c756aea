package vn.flexin.backend.mono.post.service.mobile.impl;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import vn.flexin.backend.mono.common.dto.CreateObjectResponse;
import vn.flexin.backend.mono.common.dto.PaginationResponse;
import vn.flexin.backend.mono.common.exception.BadRequestException;
import vn.flexin.backend.mono.common.exception.ForbiddenException;
import vn.flexin.backend.mono.common.exception.ResourceNotFoundException;
import vn.flexin.backend.mono.common.util.ModelMapperUtils;
import vn.flexin.backend.mono.company.dto.branch.BranchResponse;
import vn.flexin.backend.mono.company.entity.Branch;
import vn.flexin.backend.mono.company.repository.BranchRepository;
import vn.flexin.backend.mono.payment.entity.personal.PersonalWallet;
import vn.flexin.backend.mono.payment.repository.personal.PersonalWalletRepository;
import vn.flexin.backend.mono.post.dto.*;
import vn.flexin.backend.mono.post.dto.filters.PostApplicationFilter;
import vn.flexin.backend.mono.post.dto.filters.PostInterestFilter;
import vn.flexin.backend.mono.post.dto.filters.SearchPostFilter;
import vn.flexin.backend.mono.post.dto.request.CreatePostRequest;
import vn.flexin.backend.mono.post.dto.request.employer.PostRequest;
import vn.flexin.backend.mono.post.dto.request.employer.UpdatePostRequest;
import vn.flexin.backend.mono.post.dto.response.employer.DetailPostResponse;
import vn.flexin.backend.mono.post.dto.response.employer.PostApplicationResponse;
import vn.flexin.backend.mono.post.dto.response.employer.SearchPostResponse;
import vn.flexin.backend.mono.post.dto.response.employer.StatisticPostResponse;
import vn.flexin.backend.mono.post.entity.employer.Post;
import vn.flexin.backend.mono.post.entity.employer.PostApplication;
import vn.flexin.backend.mono.post.entity.employer.PostView;
import vn.flexin.backend.mono.post.entity.employer.Salary;
import vn.flexin.backend.mono.post.enums.FeatureDuration;
import vn.flexin.backend.mono.post.enums.PostStatus;
import vn.flexin.backend.mono.post.repository.PostApplicationRepository;
import vn.flexin.backend.mono.post.repository.PostInterestRepository;
import vn.flexin.backend.mono.post.repository.PostRepository;
import vn.flexin.backend.mono.post.repository.PostViewRepository;
import vn.flexin.backend.mono.post.service.mobile.PostService;
import vn.flexin.backend.mono.post.service.mobile.PostViewService;
import vn.flexin.backend.mono.post.service.mobile.SalaryService;
import vn.flexin.backend.mono.user.dto.BasicUserInfoResponse;
import vn.flexin.backend.mono.user.entity.User;
import vn.flexin.backend.mono.user.repository.user.UserRepository;
import vn.flexin.backend.mono.user.service.UserService;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

@Service
@AllArgsConstructor
@Slf4j
@Transactional
public class PostServiceImpl implements PostService {
    private final BranchRepository branchRepository;
    private final UserRepository userRepository;

    private final PostViewRepository postViewRepository;
    private final PostRepository postRepository;
    private final UserService userService;
    private final SalaryService salaryService;
    private final PostViewService postViewService;
    private final PostInterestRepository postInterestRepository;
    private final PostApplicationRepository postApplicationRepository;
    private final PersonalWalletRepository personalWalletRepository;

    @Override
    @Transactional(readOnly = true)
    public Pair<List<SearchPostResponse>, PaginationResponse> searchPostForEmployer(SearchPostFilter filters) {
        var posts = postRepository.findAll(filters);
        List<SearchPostResponse> responses = posts.stream().map(this::toSearchPostResponse).toList();
        PaginationResponse paging = new PaginationResponse(filters.getLimit(), filters.getPage(), (int) posts.getTotalElements());
        return Pair.of(responses, paging);
    }

    private SearchPostResponse toSearchPostResponse(Post post) {
        SearchPostResponse response = ModelMapperUtils.toObject(post, SearchPostResponse.class);
        response.setEmployerId(post.getEmployer().getId());
        response.setSalary(new SalaryDto(post.getSalary()));
        response.setSkills(new ArrayList<>(post.getSkills()));
        response.setApplicantCount(CollectionUtils.isEmpty(post.getPostApplication()) ? 0 : post.getPostApplication().size());
        response.setViewCount(post.getPostViewCount());
        response.setWorkingDays(post.getWorkingDays());
        response.setWorkingShifts(post.getWorkingShifts());
        response.setWorkingHourPerDay(post.getWorkingHourPerDay());
        response.setWorkType(post.getWorkType());
        response.setJobType(post.getJobType());
        return response;
    }

    @Override
    @Transactional
    public CreateObjectResponse createEmployerPost(CreatePostRequest postDto) {
        Salary salary = new Salary(postDto.getSalary());
        salary = salaryService.save(salary);

        Post post = new Post();
        setPostData(post, postDto, salary);

        save(post);

        if (post.isFeatureJob()) {
            PersonalWallet personalWallet = personalWalletRepository.findByUser_Id(postDto.getEmployerId())
                    .orElseThrow(() -> new ResourceNotFoundException("Wallet Information not found"));
            Integer point = post.getFeatureDuration().getRedeemPoint();
            if (personalWallet.getPoint() < point) {
                throw new BadRequestException("Not enough points in wallet");
            }
            personalWallet.setPoint(personalWallet.getPoint() - point);
            personalWalletRepository.save(personalWallet);
        }

        return new CreateObjectResponse(post.getId());
    }

    @Override
    @Transactional
    public Post save(Post post) {
        return postRepository.save(post);
    }

    @Override
    @Transactional
    public void updateEmployerPost(UpdatePostRequest postDto) {
        Post post = getById(postDto.getId());

        validatePermissionUser(post.getEmployer());

        Salary salary = post.getSalary();
        salaryService.updateSalary(salary, postDto.getSalary());

        setPostData(post, postDto, salary);

        save(post);
    }

    private void setPostData(Post post, PostRequest postDto, Salary salary) {
        post.setEmployer(getUser(postDto.getEmployerId()));
        post.setBranch(getBranch(postDto.getBranchId()));
        post.setSalary(salary);
        post.setTitle(postDto.getTitle());
        post.setWorkType(postDto.getWorkType());
        post.setDescription(postDto.getDescription());
        post.setPositions(postDto.getPositions());
        post.setMinExperience(postDto.getMinExperience());
        post.setMaxExperience(postDto.getMaxExperience());
        post.setStartTime(postDto.getStartTime());
        post.setEndTime(postDto.getEndTime());
        post.setWorkingHourPerDay(postDto.getWorkingHourPerDay());
        post.setWorkingDays(postDto.getWorkingDays());
        post.setWorkingShifts(postDto.getWorkingShifts());
        post.setSkills(postDto.getSkills());
        post.setBenefits(postDto.getBenefits());
        post.setRequiredDocuments(postDto.getRequiredDocuments());
        post.setFeatureJob(postDto.isFeatureJob());
        post.setFeatureDuration(FeatureDuration.fromString(postDto.getFeatureDuration()));
        post.setUrgentHiring(postDto.isUrgentHiring());
        post.setReceiveNotifyNewApplication(postDto.isReceiveNotifyNewApplication());
        post.setShowContactInformation(postDto.isShowContactInformation());
        post.setAutoApproveApplication(postDto.isAutoApproveApplication());
        post.setActiveDate(postDto.getActiveDate());
        post.setJobType(postDto.getJobType());
        post.setStatus(postDto.getStatus());
    }

    private User getUser(Long id) {
        return userRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + id));
    }

    private Branch getBranch(Long id) {
        return branchRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Branch not found with ID: " + id));
    }

    @Override
    public Post getById(Long id) {
        return postRepository.findById(id).orElseThrow(() -> new ResourceNotFoundException("Post not found."));
    }

    @Override
    public DetailPostResponse getDetailPost(Long id) {
        Post post = getById(id);
        postViewService.createPostView(id);
        return toDetailPostResponse(post);
    }

    private DetailPostResponse toDetailPostResponse (Post post) {
        DetailPostResponse response = ModelMapperUtils.toObject(post, DetailPostResponse.class);
        response.setFeatureDuration(post.getFeatureDuration().getValue());
        response.setEmployer(new BasicUserInfoResponse(post.getEmployer()));
        response.setBranch(new BranchResponse(post.getBranch()));
        setSalaryResponse(response, post);
        response.setViewCount(post.getPostViewCount());
        response.setApplicationCount(CollectionUtils.isEmpty(post.getPostApplication()) ? 0L : post.getPostApplication().size());

        return response;
    }

    @Override
    @Transactional
    public void deletePost(Long id) {
        Post post = getById(id);
        validatePermissionUser(post.getEmployer());
        postViewRepository.deleteByPostId(id);
        postInterestRepository.deleteByPostId(id);
        postApplicationRepository.deleteByPostId(id);
        postRepository.delete(post);
    }

    private void validatePermissionUser(User employer) {
        User user = userService.getCurrentLoginUser();
        if (!Objects.equals(user.getId(), employer.getId())) {
            throw new ForbiddenException();
        }
    }

    @Override
    @Transactional
    public void togglePost(Long id) {
        Post post = getById(id);
        validatePermissionUser(post.getEmployer());

        post.setFeatureJob(!post.isFeatureJob());

        save(post);
    }

    @Override
    @Transactional
    public void updateStatusPost(Long id, PostStatus postStatus) {
        Post post = getById(id);
        validatePermissionUser(post.getEmployer());

        post.setStatus(postStatus);

        save(post);
    }

    @Override
    @Transactional
    public DetailPostResponse duplicatePost(Long id) {
        User user = userService.getCurrentLoginUser();
        Post originalPost = getById(id);

        if (!Objects.equals(user.getId(), originalPost.getEmployer().getId())) {
            throw new ForbiddenException();
        }
        Post duplicatePost = new Post();
        duplicatePost.setEmployer(originalPost.getEmployer());
        duplicatePost.setBranch(originalPost.getBranch());
        duplicatePost.setSalary(originalPost.getSalary());
        duplicatePost.setTitle(originalPost.getTitle());
        duplicatePost.setWorkType(originalPost.getWorkType());
        duplicatePost.setDescription(originalPost.getDescription());
        duplicatePost.setPositions(originalPost.getPositions());
        duplicatePost.setMinExperience(originalPost.getMinExperience());
        duplicatePost.setMaxExperience(originalPost.getMaxExperience());
        duplicatePost.setStartTime(originalPost.getStartTime());
        duplicatePost.setEndTime(originalPost.getEndTime());
        duplicatePost.setWorkingHourPerDay(originalPost.getWorkingHourPerDay());
        duplicatePost.setWorkingDays(originalPost.getWorkingDays());
        duplicatePost.setWorkingShifts(originalPost.getWorkingShifts());
        duplicatePost.setSkills(originalPost.getSkills());
        duplicatePost.setBenefits(originalPost.getBenefits());
        duplicatePost.setRequiredDocuments(originalPost.getRequiredDocuments());
        duplicatePost.setFeatureJob(originalPost.isFeatureJob());
        duplicatePost.setFeatureDuration(originalPost.getFeatureDuration());
        duplicatePost.setUrgentHiring(originalPost.isUrgentHiring());
        duplicatePost.setReceiveNotifyNewApplication(originalPost.isReceiveNotifyNewApplication());
        duplicatePost.setShowContactInformation(originalPost.isShowContactInformation());
        duplicatePost.setAutoApproveApplication(originalPost.isAutoApproveApplication());
        duplicatePost.setActiveDate(originalPost.getActiveDate());
        duplicatePost.setJobType(originalPost.getJobType());
        duplicatePost.setStatus(originalPost.getStatus());

        duplicatePost.setPostViews(Collections.emptyList());
        duplicatePost.setPostApplication(Collections.emptyList());
        duplicatePost.setPostInterests(Collections.emptyList());

        duplicatePost = postRepository.save(duplicatePost);

        return toDetailPostResponse(duplicatePost);
    }

    @Override
    public StatisticPostResponse getPostStatistic(Long id) {
        Post post = getByIdForStatistic(id);
        User user = userService.getCurrentLoginUser();

        if (!Objects.equals(user.getId(), post.getEmployer().getId())) {
            throw new ForbiddenException();
        }

        List<PostView> views = post.getPostViews();
        List<PostApplication> applicants = post.getPostApplication();
        StatisticPostResponse response = new StatisticPostResponse();
        response.setId(post.getId());
        setDataStatisticViews(response, views);
        setDataStatisticApplicants(response, applicants);
        return response;
    }

    @Override
    public Pair<List<SearchPostResponse>, PaginationResponse> getInterestedPosts(PostInterestFilter filters) {
        User user = userService.getCurrentLoginUser();
        var posts = postRepository.findPostsInterestedByUserId(user.getId());
        List<SearchPostResponse> responses = posts.stream().map(this::toSearchPostResponse).toList();
        PaginationResponse paging = new PaginationResponse(filters.getPage(), filters.getLimit(), posts.size());
        return Pair.of(responses, paging);
    }

    @Override
    public Pair<List<SearchPostResponse>, PaginationResponse> getAppliedPosts(PostApplicationFilter filters) {
        User user = userService.getCurrentLoginUser();
        var posts = postRepository.findPostsApplicationByUserId(user.getId());
        List<SearchPostResponse> responses = posts.stream().map(this::toSearchPostResponse).toList();
        PaginationResponse paging = new PaginationResponse(filters.getPage(), filters.getLimit(), posts.size());
        return Pair.of(responses, paging);
    }

    private PostApplicationResponse toPostApplicationResponse(PostApplication postApplication){
        return new PostApplicationResponse(postApplication);
    }

    @Override
    public Pair<List<PostApplicationResponse>, PaginationResponse> getPostApplications(Long postId, PostApplicationFilter filters) {
        var postApplications = postApplicationRepository.findPostApplicationByPostId(postId);
        List<PostApplicationResponse> responses = postApplications.stream().map(this::toPostApplicationResponse).toList();
        PaginationResponse paging = new PaginationResponse(filters.getPage(), filters.getLimit(), postApplications.size());

        return Pair.of(responses, paging);
    }

    private void setDataStatisticApplicants(StatisticPostResponse response, List<PostApplication> applicants) {
        int shortlisted = 0;
        int rejected = 0;
        int hired = 0;
        Map<LocalDate, Integer> applicantsByDayMap = new HashMap<>();

        for (PostApplication applicant : applicants) {
            switch (applicant.getStatus()) {
                case "short_list":
                    shortlisted++;
                    break;
                case "reject":
                    rejected++;
                    break;
                case "hire":
                    hired++;
                    break;
            }
            countByDate(applicantsByDayMap, applicant.getCreatedAt());
        }
        List<StatisticPostResponse.CountResponse> applicationsPerDays = new ArrayList<>();
        applicantsByDayMap.forEach((date, count) -> applicationsPerDays.add(new StatisticPostResponse.CountResponse(date.toString(), count)));
        response.setHired(hired);
        response.setRejected(rejected);
        response.setShortlisted(shortlisted);
        response.setApplications(applicants.size());
        response.setApplicationsPerDay(applicationsPerDays);
        response.setConversionRate((double) response.getApplications() /(response.getViews() == 0 ? 1 : response.getViews()));
    }

    private void countByDate(Map<LocalDate, Integer> applicantsByDayMap, LocalDateTime createdAt) {
        LocalDate viewDate = createdAt.toLocalDate();
        if(CollectionUtils.isEmpty(applicantsByDayMap.keySet()) || !applicantsByDayMap.containsKey(viewDate)) {
            applicantsByDayMap.put(viewDate, 1);
        } else {
            Integer viewCount = applicantsByDayMap.get(viewDate);
            applicantsByDayMap.replace(viewDate, viewCount, viewCount+1);
        }
    }

    private void setDataStatisticViews(StatisticPostResponse response, List<PostView> views) {
        Map<LocalDate, Integer> viewsByDayMap = new HashMap<>();
        for (PostView view : views) {
            countByDate(viewsByDayMap, view.getCreatedAt());
        }
        List<StatisticPostResponse.CountResponse> viewPerDays = new ArrayList<>();
        viewsByDayMap.forEach((date, count) -> viewPerDays.add(new StatisticPostResponse.CountResponse(date.toString(), count)));
        response.setViews(views.size());
        response.setViewsPerDay(viewPerDays);
    }

    private Post getByIdForStatistic(Long id) {
        return postRepository.findByIdForStatistic(id).orElseThrow(() -> new ResourceNotFoundException("Post not found."));
    }

    private void setSalaryResponse(DetailPostResponse response, Post post) {
        SalaryDto dto = ModelMapperUtils.toObject(post.getSalary(), SalaryDto.class);
        response.setSalary(dto);
    }

}
