package vn.flexin.backend.mono.auth.service.admin;

import vn.flexin.backend.mono.auth.dto.*;
import vn.flexin.backend.mono.auth.dto.ChangePasswordRequest;

public interface AdminAuthService {

    void forgotPassword(AdminForgotPasswordRequest request);

    void resetPassword(String token, String email, String newPassword);

    TokenDto refreshToken(RefreshTokenRequest refreshTokenRequest);

    void logout();

    LoginResponse login(AdminLoginRequest loginRequest);

    void changePassword(ChangePasswordRequest changePasswordRequest);

    void updateProfile(AdminUpdateProfileRequest updateProfileRequest);
} 