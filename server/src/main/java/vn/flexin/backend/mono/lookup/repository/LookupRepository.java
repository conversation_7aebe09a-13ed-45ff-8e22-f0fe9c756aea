package vn.flexin.backend.mono.lookup.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import vn.flexin.backend.mono.common.repository.JpaSpecificationRepository;
import vn.flexin.backend.mono.company.entity.Company;
import vn.flexin.backend.mono.lookup.entity.Lookup;

import java.util.List;

public interface LookupRepository extends JpaSpecificationRepository<Lookup, Long> {
    List<Lookup> findByType(String type);
}
