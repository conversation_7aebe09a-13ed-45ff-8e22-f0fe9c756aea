package vn.flexin.backend.mono.interview.dto.response;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class InterviewDetailResponse {
    private Long id;

    private Long employerId;

    private String employerName;

    private Long jobSeekerId;

    private String jobSeekerName;

    private LocalDateTime scheduledTime;

    private Integer durationMinutes;

    private String status;

    private String meetingCode;

    private String notes;

    private LocalDateTime startedAt;

    private LocalDateTime endedAt;

    private LocalDateTime createdAt;

    private String createdBy;

    private String lastModifiedBy;

    private LocalDateTime lastModifiedAt = LocalDateTime.now();
} 