package vn.flexin.backend.mono.interview.repository;

import org.springframework.stereotype.Repository;
import vn.flexin.backend.mono.common.repository.JpaSpecificationRepository;
import vn.flexin.backend.mono.interview.entity.InterviewRequest;
import vn.flexin.backend.mono.interview.enums.InterviewRequestStatus;
import vn.flexin.backend.mono.interview.enums.PostType;
import vn.flexin.backend.mono.user.entity.User;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface InterviewRequestRepository extends JpaSpecificationRepository<InterviewRequest, Long> {
    List<InterviewRequest> findByStatusAndExpiredDateBefore(InterviewRequestStatus interviewRequestStatus, LocalDateTime now);

    Optional<InterviewRequest> findByRequestUserIdAndReceiveUserIdAndPostId(Long requestUserId, Long receiveUserId, Long postId);

    void deleteAllByPostIdAndPostType(Long id, PostType postType);
}
