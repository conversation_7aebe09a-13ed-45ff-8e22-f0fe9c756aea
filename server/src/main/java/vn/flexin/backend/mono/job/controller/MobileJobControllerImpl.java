package vn.flexin.backend.mono.job.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.flexin.backend.mono.common.dto.ApiResponse;
import vn.flexin.backend.mono.job.dto.JobDto;
import vn.flexin.backend.mono.job.dto.SearchJobRequest;
import vn.flexin.backend.mono.job.service.JobService;

import jakarta.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/v1/mobile/jobs")
@RequiredArgsConstructor
public class MobileJobControllerImpl implements MobileJobController {

    private final JobService jobService;

    @Override
    public ResponseEntity<ApiResponse<JobDto>> createJob(@Valid JobDto jobDto) {
        JobDto createdJob = jobService.createJob(jobDto);
        return new ResponseEntity<>(ApiResponse.success("Job created successfully", createdJob), HttpStatus.CREATED);
    }

    @Override
    public ResponseEntity<ApiResponse<JobDto>> getJobById(Long id) {
        JobDto job = jobService.getJobById(id);
        return new ResponseEntity<>(ApiResponse.success("Job retrieved successfully", job), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponse<List<JobDto>>> getAllJobs() {
        List<JobDto> jobs = jobService.getAllJobs();
        return new ResponseEntity<>(ApiResponse.success("Jobs retrieved successfully", jobs), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponse<List<JobDto>>> getJobsByEmployerId(Long employerId) {
        List<JobDto> jobs = jobService.getJobsByEmployerId(employerId);
        return new ResponseEntity<>(ApiResponse.success("Jobs retrieved successfully", jobs), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponse<List<JobDto>>> getJobsByStatus(String status) {
        List<JobDto> jobs = jobService.getJobsByStatus(status);
        return new ResponseEntity<>(ApiResponse.success("Jobs retrieved successfully", jobs), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponse<List<JobDto>>> getJobsByEmployerIdAndStatus(Long employerId, String status) {
        List<JobDto> jobs = jobService.getJobsByEmployerIdAndStatus(employerId, status);
        return new ResponseEntity<>(ApiResponse.success("Jobs retrieved successfully", jobs), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponse<List<JobDto>>> findJobsByLocation(String location) {
        List<JobDto> jobs = jobService.findJobsByLocation(location);
        return new ResponseEntity<>(ApiResponse.success("Jobs retrieved successfully", jobs), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponse<List<JobDto>>> findJobsByJobType(String jobType) {
        List<JobDto> jobs = jobService.findJobsByJobType(jobType);
        return new ResponseEntity<>(ApiResponse.success("Jobs retrieved successfully", jobs), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponse<List<JobDto>>> findJobsByHourlyRateRange(Double minRate, Double maxRate) {
        List<JobDto> jobs = jobService.findJobsByHourlyRateRange(minRate, maxRate);
        return new ResponseEntity<>(ApiResponse.success("Jobs retrieved successfully", jobs), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponse<JobDto>> updateJob(Long id, @Valid JobDto jobDto) {
        JobDto updatedJob = jobService.updateJob(id, jobDto);
        return new ResponseEntity<>(ApiResponse.success("Job updated successfully", updatedJob), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponse<Void>> deleteJob(Long id) {
        jobService.deleteJob(id);
        return new ResponseEntity<>(ApiResponse.success("Job deleted successfully"), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponse<Page<JobDto>>> searchJobs(@Valid SearchJobRequest searchJobRequest) {
        Page<JobDto> jobs = jobService.searchJobs(searchJobRequest);
        return new ResponseEntity<>(ApiResponse.success("Jobs retrieved successfully", jobs), HttpStatus.OK);
    }
} 