package vn.flexin.backend.mono.company.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import vn.flexin.backend.mono.common.entity.AbstractAuditingEntity;
import vn.flexin.backend.mono.common.util.CommonUtil;
import vn.flexin.backend.mono.company.dto.company.CreateVRDocumentRequest;
import vn.flexin.backend.mono.file.entity.File;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

@Entity
@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "t_company_verification_request_documents")
public class VerificationRequestDocument extends AbstractAuditingEntity<Long> implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull
    private String name;

    private String description;

    @ManyToOne
    @JoinColumn(name = "file_id")  // 🔥 Thêm khóa ngoại cho file phụ
    private File file;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "verification_request_id")
    private VerificationRequest request;

    public VerificationRequestDocument(CreateVRDocumentRequest item, VerificationRequest request, Map<Long, File> fileMap) {
        this.name = item.getName();
        this.description = item.getDescription();
        this.request = request;
        if (item.getFile() != null) {
            this.file = fileMap.get(item.getFile().getId());
        }
    }
}
