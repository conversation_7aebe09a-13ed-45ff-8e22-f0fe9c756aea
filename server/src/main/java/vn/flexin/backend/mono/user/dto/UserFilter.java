package vn.flexin.backend.mono.user.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.CollectionUtils;
import vn.flexin.backend.mono.common.dto.BaseFilter;
import vn.flexin.backend.mono.common.repository.param.*;
import vn.flexin.backend.mono.common.util.SpecificationUtil;
import vn.flexin.backend.mono.user.entity.User;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserFilter extends BaseFilter<User> {
    @JsonProperty("isPhoneVerified")
    private Boolean isPhoneVerified;
    @JsonProperty("isMailVerified")
    private Boolean isMailVerified;
    @JsonProperty("status")
    private Boolean status;
    private String name;
    private String role;
    private String keyword;
    private List<String> roleIds;

    @Override
    public Specification<User> toSpecification() {
        var condition = new Condition();
        if (isPhoneVerified != null) {
            condition.append(new Where(User.Fields.isPhoneVerified, Operator.EQUAL, isPhoneVerified));
        }
        if (isMailVerified != null) {
            condition.append(new Where(User.Fields.isMailVerified, Operator.EQUAL, isMailVerified));
        }
        if (status != null) {
            condition.append(new Where(User.Fields.isActive, Operator.EQUAL, status));
        }
        if (name != null && !name.isEmpty()) {
            condition.append(new Where(User.Fields.name, Operator.LIKE_IGNORE_CASE, name));
        }
        if (role != null && !role.isEmpty()) {
            condition.append(new Where(User.Fields.role, Operator.LIKE_IGNORE_CASE, role));
        }
        if (!CollectionUtils.isEmpty(roleIds)) {
//            condition.append(new Where(User.Fields.roles))
        }
        if (keyword != null && !keyword.isEmpty()) {
            var nameKeywordCondition = new Condition()
                    .append(new Where(User.Fields.name, Operator.LIKE_IGNORE_CASE, keyword));
            var phoneKeywordCondition = new Condition()
                    .append(new Where(User.Fields.phoneNumber, Operator.LIKE_IGNORE_CASE, keyword));
            var emailKeywordCondition = new Condition()
                    .append(new Where(User.Fields.email, Operator.LIKE_IGNORE_CASE, keyword));

            condition.appendComplex(new Where(Complex.OR, List.of(nameKeywordCondition, phoneKeywordCondition, emailKeywordCondition)));
        }

        return SpecificationUtil.bySearchQuery(condition);
    }
}