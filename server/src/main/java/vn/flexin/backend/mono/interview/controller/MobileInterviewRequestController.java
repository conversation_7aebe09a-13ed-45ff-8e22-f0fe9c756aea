package vn.flexin.backend.mono.interview.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.common.dto.CreateObjectResponse;
import vn.flexin.backend.mono.common.dto.PaginationApiResponseDto;
import vn.flexin.backend.mono.interview.dto.InterviewRequestFilter;
import vn.flexin.backend.mono.interview.dto.request.CreateInterviewRequestRequest;
import vn.flexin.backend.mono.interview.dto.request.UpdateInterviewRequestRequest;
import vn.flexin.backend.mono.interview.dto.response.InterviewRequestResponse;

import java.time.LocalDateTime;
import java.util.List;

@Tag(name = "Mobile Interview Request APIs", description = "Interview Request management APIs")
@RequestMapping("/v1/mobile/interview-requests")
public interface MobileInterviewRequestController {

    @Operation(summary = "Create a new interview request")
    @ApiResponse(responseCode = "201", description = "Interview request created successfully")
    @PostMapping
    ResponseEntity<ApiResponseDto<CreateObjectResponse>> createRequest(@RequestBody CreateInterviewRequestRequest request);

    @Operation(summary = "Update an existing interview request")
    @ApiResponse(responseCode = "200", description = "Interview request updated successfully")
    @PutMapping("/{requestId}")
    ResponseEntity<ApiResponseDto<Boolean>> updateRequest(@PathVariable Long requestId,
                                                          @RequestBody UpdateInterviewRequestRequest request);

    @Operation(summary = "Accept an interview request")
    @ApiResponse(responseCode = "200", description = "Interview request accepted successfully")
    @PostMapping("/{requestId}/accept")
    ResponseEntity<ApiResponseDto<Boolean>> acceptRequest(@PathVariable Long requestId);

    @Operation(summary = "Reject an interview request")
    @ApiResponse(responseCode = "200", description = "Interview request rejected successfully")
    @PostMapping("/{requestId}/reject")
    ResponseEntity<ApiResponseDto<Boolean>> rejectRequest(@PathVariable Long requestId, @RequestParam String rejectNote);

    @Operation(summary = "Cancel an interview request")
    @ApiResponse(responseCode = "200", description = "Interview request cancelled successfully")
    @PostMapping("/{requestId}/cancel")
    ResponseEntity<ApiResponseDto<Boolean>> cancelRequest(@PathVariable Long requestId);

    @Operation(summary = "Extend an interview request")
    @ApiResponse(responseCode = "200", description = "Interview request extended successfully")
    @PostMapping("/{requestId}/extend")
    ResponseEntity<ApiResponseDto<Boolean>> extendRequest(@PathVariable Long requestId,
                                                          @RequestParam LocalDateTime newExpiredDate);

    @Operation(summary = "Get interview requests by users")
    @ApiResponse(responseCode = "200", description = "Retrieved interview requests successfully")
    @PostMapping("/search")
    ResponseEntity<PaginationApiResponseDto<List<InterviewRequestResponse>>> getRequestsByRequestUser(@RequestBody InterviewRequestFilter filter);

    @Operation(summary = "Get an interview request by ID")
    @ApiResponse(responseCode = "200", description = "Retrieved interview request successfully")
    @GetMapping("/{requestId}")
    ResponseEntity<InterviewRequestResponse> getRequestById(@PathVariable Long requestId);
}
