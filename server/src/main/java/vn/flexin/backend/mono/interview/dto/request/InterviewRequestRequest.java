package vn.flexin.backend.mono.interview.dto.request;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.flexin.backend.mono.interview.enums.InterviewRequestStatus;
import vn.flexin.backend.mono.interview.enums.PostType;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class InterviewRequestRequest {
    private Long receiveUserId;
    private Long postId;
    private String postName;
    private PostType postType;
    private LocalDateTime expiredDate;
    private InterviewRequestStatus status;
    private String requestNote;
}
