package vn.flexin.backend.mono.post.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.Getter;
import vn.flexin.backend.mono.common.enums.AppStatus;
import vn.flexin.backend.mono.common.exception.ResponseAppStatusException;

import java.util.Arrays;

@Getter
public enum PostStatus {
    DRAFT("draft"),
    ACTIVE("active"),
    EXPIRED("expired"),
    PAUSED("paused"),
    CLOSED("closed"),
    REOPEN("reopen"),
    REJECTED("rejected"),
    PENDING("pending");

    private final String value;

    PostStatus(String value) {
        this.value = value;
    }

    public static String getValues() {
        return String.join(", ", Arrays.stream(values()).map(x -> x.name().toLowerCase()).toList());
    }

    @JsonCreator
    public static PostStatus fromString(String value) {
        try {
            return valueOf(value.toUpperCase());
        } catch (Exception ex) {
            throw new ResponseAppStatusException(AppStatus.BAD_REQUEST, "Post status type must be any of [" + getValues() + "]");
        }
    }
}
