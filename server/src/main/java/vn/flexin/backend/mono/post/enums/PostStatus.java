package vn.flexin.backend.mono.post.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.Getter;
import vn.flexin.backend.mono.common.enums.AppStatus;
import vn.flexin.backend.mono.common.exception.ResponseAppStatusException;

import java.util.Arrays;

public enum PostStatus {
    DRAFT,
    ACTIVE,
    EXPIRED,
    PAUSED,
    CLOSED,
    REOPEN,
    REJECTED,
    PENDING;

    public static String getValues() {
        return String.join(", ", Arrays.stream(values()).map(x -> x.name().toLowerCase()).toList());
    }

    @JsonCreator
    public static PostStatus fromString(String value) {
        try {
            return valueOf(value.toUpperCase());
        } catch (Exception ex) {
            throw new ResponseAppStatusException(AppStatus.BAD_REQUEST, "Post status type must be any of [" + getValues() + "]");
        }
    }
}
