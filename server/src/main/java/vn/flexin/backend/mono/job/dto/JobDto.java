package vn.flexin.backend.mono.job.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class JobDto {
    private Long id;
    
    @NotBlank(message = "Title is required")
    private String title;
    
    @NotBlank(message = "Description is required")
    private String description;
    
    @NotBlank(message = "Location is required")
    private String location;
    
    @NotBlank(message = "Job type is required")
    private String jobType;
    
    @NotNull(message = "Hourly rate is required")
    @Positive(message = "Hourly rate must be positive")
    private Double hourlyRate;
    
    private String requirements;
    private String responsibilities;
    private String status;
    private Long employerId;
    private String employerName;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    private boolean remote;
    private Integer hoursPerWeek;
    private Set<String> workDays = new HashSet<>();
    private Set<String> requiredSkills = new HashSet<>();
    private LocalDate startDate;
    private LocalDate endDate;
    private boolean featured;
} 