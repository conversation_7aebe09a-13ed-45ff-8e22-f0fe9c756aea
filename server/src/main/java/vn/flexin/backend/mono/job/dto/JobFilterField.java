package vn.flexin.backend.mono.job.dto;

public enum JobFilterField {
    ID("id"),
    TITLE("title"),
    LOCATION("location"),
    JOB_TYPE("jobType"),
    HOURLY_RATE("hourlyRate"),
    STATUS("status"),
    EMPLOYER_ID("employer.id"),
    REMOTE("remote"),
    FEATURED("featured");
    
    private final String fieldName;
    
    JobFilterField(String fieldName) {
        this.fieldName = fieldName;
    }
    
    public String fieldName() {
        return fieldName;
    }
} 