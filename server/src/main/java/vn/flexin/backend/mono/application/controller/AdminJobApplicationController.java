package vn.flexin.backend.mono.application.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import vn.flexin.backend.mono.application.dto.JobApplicationDto;
import vn.flexin.backend.mono.application.dto.SearchJobApplicationRequest;

import jakarta.validation.Valid;
import java.util.List;

@Tag(name = "Admin Job Application Management", description = "Admin job application management endpoints")
@SecurityRequirement(name = "bearerAuth")
public interface AdminJobApplicationController {

    @Operation(summary = "Get all job applications", description = "Retrieve a list of all job applications (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Job applications retrieved successfully",
                    content = @Content(schema = @Schema(implementation = JobApplicationDto.class))),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user")
    })
    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<List<JobApplicationDto>>> getAllJobApplications();

    @Operation(summary = "Search and filter job applications", description = "Search and filter job applications with pagination and sorting (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Job applications retrieved successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user")
    })
    @PostMapping("/search")
    @PreAuthorize("hasRole('ADMIN')")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<Page<JobApplicationDto>>> searchJobApplications(
            @Valid @RequestBody SearchJobApplicationRequest searchJobApplicationRequest);

    @Operation(summary = "Get job application by ID", description = "Retrieve a job application by its ID (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Job application retrieved successfully",
                    content = @Content(schema = @Schema(implementation = JobApplicationDto.class))),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user"),
            @ApiResponse(responseCode = "404", description = "Job application not found")
    })
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<JobApplicationDto>> getJobApplicationById(@PathVariable Long id);

    @Operation(summary = "Get job applications by job ID", description = "Retrieve job applications for a specific job (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Job applications retrieved successfully",
                    content = @Content(schema = @Schema(implementation = JobApplicationDto.class))),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user")
    })
    @GetMapping("/job/{jobId}")
    @PreAuthorize("hasRole('ADMIN')")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<List<JobApplicationDto>>> getJobApplicationsByJobId(@PathVariable Long jobId);

    @Operation(summary = "Get job applications by user ID", description = "Retrieve job applications for a specific user (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Job applications retrieved successfully",
                    content = @Content(schema = @Schema(implementation = JobApplicationDto.class))),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user")
    })
    @GetMapping("/user/{userId}")
    @PreAuthorize("hasRole('ADMIN')")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<List<JobApplicationDto>>> getJobApplicationsByUserId(@PathVariable Long userId);

    @Operation(summary = "Update job application status", description = "Update the status of a job application (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Job application status updated successfully",
                    content = @Content(schema = @Schema(implementation = JobApplicationDto.class))),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user"),
            @ApiResponse(responseCode = "404", description = "Job application not found")
    })
    @PatchMapping("/{id}/status")
    @PreAuthorize("hasRole('ADMIN')")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<JobApplicationDto>> updateJobApplicationStatus(
            @PathVariable Long id, @RequestParam String status, @RequestParam(required = false) String adminNotes);

    @Operation(summary = "Delete job application", description = "Delete a job application (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Job application deleted successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user"),
            @ApiResponse(responseCode = "404", description = "Job application not found")
    })
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    ResponseEntity<vn.flexin.backend.mono.common.dto.ApiResponse<Void>> deleteJobApplication(@PathVariable Long id);
} 