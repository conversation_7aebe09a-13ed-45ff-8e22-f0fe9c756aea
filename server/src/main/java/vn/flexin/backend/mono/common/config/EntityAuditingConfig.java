package vn.flexin.backend.mono.common.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;

import java.util.Optional;

@Configuration
@EnableJpaAuditing(auditorAwareRef = "auditorProvider")
public class EntityAuditingConfig {

    public static final Optional<String> ANONYMOUS_AUDITOR = Optional.of("System");

    @Bean
    public AuditorAware<String> auditorProvider() {
        return () -> {
            try {
                Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
                if (authentication == null) {
                    return ANONYMOUS_AUDITOR;
                }
                Jwt principal = (Jwt) authentication.getPrincipal();
                String ulid = principal.getClaimAsString("preferred_username");
                return Optional.of(ulid);
            } catch (Exception e) {
                return ANONYMOUS_AUDITOR;
            }
        };
    }
}
