package vn.flexin.backend.mono.interview.dto.response;

import lombok.Data;
import vn.flexin.backend.mono.interview.enums.InterviewRequestStatus;
import vn.flexin.backend.mono.interview.enums.PostType;
import vn.flexin.backend.mono.user.dto.BasicUserInfoResponse;

import java.time.LocalDateTime;

@Data
public class InterviewRequestResponse {
    private Long id;
    private BasicUserInfoResponse requester;
    private BasicUserInfoResponse receiver;
    private String postName;
    private Long postId;
    private PostType postType;
    private LocalDateTime expiredDate;
    private InterviewRequestStatus status;
    private String rejectNote;
    private String requestNote;
}
