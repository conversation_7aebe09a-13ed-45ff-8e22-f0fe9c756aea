package vn.flexin.backend.mono.user.service.impl;

import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.flexin.backend.mono.common.util.CommonUtil;
import vn.flexin.backend.mono.user.entity.User;
import vn.flexin.backend.mono.user.entity.UserDevice;
import vn.flexin.backend.mono.user.repository.UserDeviceRepository;
import vn.flexin.backend.mono.user.service.UserDeviceService;

import java.util.List;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class UserDeviceServiceImpl implements UserDeviceService {

    private final UserDeviceRepository userDeviceRepository;
    @Override
    public UserDevice save(UserDevice userDevice) {
        return userDeviceRepository.save(userDevice);
    }

    @Override
    public UserDevice getByDeviceIdAndPhoneNumber(String deviceId, String phoneNumber) {
        return userDeviceRepository.findFirstByDeviceIdAndPhoneNumber(deviceId, phoneNumber);
    }

    @Override
    @Transactional
    public void updateUserDeviceAfterUserFullySignUp(User newUser, UserDevice userDevice) {
        userDevice.setUser(newUser);
        userDevice.setLogout(false);
        userDevice.setTempPassword(null);
        userDevice.setDeviceVerified(true);
        userDevice.setDeviceVerifiedAt(CommonUtil.getCurrentUTCTime());
        userDevice.setSignUpComplete(true);
        userDevice.setSignUpCompletedAt(CommonUtil.getCurrentUTCTime());
        userDeviceRepository.save(userDevice);
    }

    @Override
    public void logoutAllDeviceExclude(Long userId, String deviceId) {
        userDeviceRepository.logoutAllDevices(userId, deviceId);
    }

    @Override
    public void logoutDevice(Long userId, String deviceId) {
        userDeviceRepository.logoutDevice(userId, deviceId);
    }

    @Override
    public List<UserDevice> getByUserId(Long userId) {
        return userDeviceRepository.finAllByUserId(userId);
    }

    @Override
    public List<UserDevice> getByPhoneNumber(String phoneNumber) {
        return userDeviceRepository.findAllByPhoneNumber(phoneNumber);
    }

}
