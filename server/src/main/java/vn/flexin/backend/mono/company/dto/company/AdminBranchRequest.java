package vn.flexin.backend.mono.company.dto.company;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.flexin.backend.mono.address.dto.request.AddressRequest;
import vn.flexin.backend.mono.file.dto.FileDto;

import java.util.List;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = false)
public class AdminBranchRequest {
    private Long companyId;

    @NotBlank
    private String name;

    private AddressRequest address;

    @JsonProperty("isDefault")
    private Boolean isDefault;

    private String managerId;

    @NotBlank
    private String phoneNumber;

    private List<FileDto> galleryImages;

    private Map<String, Object> additionalData;





}
