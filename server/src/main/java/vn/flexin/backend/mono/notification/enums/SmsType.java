package vn.flexin.backend.mono.notification.enums;

import org.springframework.web.util.HtmlUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

public enum SmsType {

    SEND_CONFIRM_OTP("%OTP%");

    private final List<String> placeHolders;

    SmsType(String... placeHolders) {
        this.placeHolders = List.of(placeHolders);
    }

    public String replacePlaceHolders(String template, Map<String, Object> data) {
        String result = template;
        for (String placeHolder : placeHolders) {
            String replacement = "";
            if (!Objects.equals(data.getOrDefault(placeHolder, "").toString(), "")) {
                replacement = HtmlUtils.htmlEscape(data.getOrDefault(placeHolder, "").toString());
            }
            result = result.replace(placeHolder, replacement);
        }
        return result;
    }
}
