package vn.flexin.backend.mono.common.util;

import jakarta.persistence.criteria.*;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.CollectionUtils;
import vn.flexin.backend.mono.common.exception.BadRequestException;
import vn.flexin.backend.mono.common.repository.param.Condition;
import vn.flexin.backend.mono.common.repository.param.Join;
import vn.flexin.backend.mono.common.repository.param.Where;

import java.util.ArrayList;
import java.util.List;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class SpecificationUtil {
    public static <T> Specification<T> bySearchQuery(Condition condition) {
        return (Root<T> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            if (condition.getJoin() != null) {
                addJoinColumn(predicates, condition.getJoin(), criteriaBuilder, root);
            }
            if (!CollectionUtils.isEmpty(condition.getWhereList())) {
                condition.getWhereList().stream().filter(e -> ObjectUtils.isNotEmpty(e.getValue())).forEach(filter -> addPredicates(predicates, filter, criteriaBuilder, root.get(filter.getProperty())));
            }
            if (!CollectionUtils.isEmpty(condition.getWhereComplex())) {
                condition.getWhereComplex().stream().filter(e -> ObjectUtils.isNotEmpty(e.getValue())).forEach(filter -> addComplexPredicates(predicates, filter, criteriaBuilder, root));
            }
            if (predicates.isEmpty()) {
                return criteriaBuilder.conjunction();
            }
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    private static <T> void addJoinColumn(List<Predicate> predicates, Join join, CriteriaBuilder criteriaBuilder, Root<T> root) {
        jakarta.persistence.criteria.Join<Object, Object> joinParent = root.join(join.getColumn());
        join.getWhereList().forEach(filter -> addPredicates(predicates, filter, criteriaBuilder, joinParent.get(filter.getProperty())));
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    private static void addPredicates(List<Predicate> predicates, Where where, CriteriaBuilder criteriaBuilder, Path expression) {
        switch (where.getOperator()) {
            case EQUAL:
                predicates.add(criteriaBuilder.equal(expression, where.getValue()));
                break;
            case NOT_EQUAL:
                predicates.add(criteriaBuilder.notEqual(expression, where.getValue()));
                break;
            case IN:
                if (!CollectionUtils.isEmpty((List) where.getValue())) {
                    List<List<Object>> lcti = ListUtils.partition((List<Object>) where.getValue(), 100);
                    Predicate[] ctiPredicates = new Predicate[lcti.size()];
                    for (int i = 0; i < lcti.size(); i++) {
                        ctiPredicates[i] = expression.in(lcti.get(i));
                    }
                    predicates.add(criteriaBuilder.or(ctiPredicates));
                }
                break;
            case NOT_IN:
                if (!CollectionUtils.isEmpty((List) where.getValue())) {
                    List<List<Object>> lcti = ListUtils.partition((List<Object>) where.getValue(), 100);
                    Predicate[] ctiPredicates = new Predicate[lcti.size()];
                    for (int i = 0; i < lcti.size(); i++) {
                        ctiPredicates[i] = expression.in(lcti.get(i)).not();
                    }
                    predicates.add(criteriaBuilder.and(ctiPredicates));
                }
                break;
            case LESS_THAN_OR_EQUAL:
                predicates.add(criteriaBuilder.lessThanOrEqualTo(expression, (Comparable) where.getValue()));
                break;
            case LESS_THAN:
                predicates.add(criteriaBuilder.lessThan(expression, (Comparable) where.getValue()));
                break;
            case GREATER_THAN_OR_EQUAL:
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(expression, (Comparable) where.getValue()));
                break;
            case GREATER_THAN:
                predicates.add(criteriaBuilder.greaterThan(expression, (Comparable) where.getValue()));
                break;
            case LIKE:
                predicates.add(criteriaBuilder.like(expression, "%" + where.getValue() + "%"));
                break;
            case LIKE_IGNORE_CASE:
                predicates.add(criteriaBuilder.like(criteriaBuilder.upper(expression), "%" + ((String) where.getValue()).toUpperCase() + "%"));
                break;
            default:
                throw new BadRequestException("Function not implement");
        }
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    private static <T> void addComplexPredicates(List<Predicate> predicates, Where where, CriteriaBuilder criteriaBuilder, Root<T> root) {
        switch (where.getComplex()) {
            case OR:
                if (!CollectionUtils.isEmpty((List) where.getValue())) {
                    List<Condition> conditions = (List) where.getValue();
                    Predicate[] ctiPredicates = new Predicate[conditions.size()];
                    for (int i = 0; i < ctiPredicates.length; i++) {
                        var condition = conditions.get(i);
                        List<Predicate> subPredicates = new ArrayList<>();
                        if (!CollectionUtils.isEmpty(condition.getWhereList())) {
                            condition.getWhereList().stream().filter(e -> ObjectUtils.isNotEmpty(e.getValue())).forEach(filter -> addPredicates(subPredicates, filter, criteriaBuilder, root.get(filter.getProperty())));
                        }
                        ctiPredicates[i] = criteriaBuilder.and(subPredicates.toArray(new Predicate[0]));
                        if (!CollectionUtils.isEmpty(condition.getWhereComplex())) {
                            condition.getWhereComplex().stream().filter(e -> ObjectUtils.isNotEmpty(e.getValue())).forEach(filter -> addComplexPredicates(predicates, filter, criteriaBuilder, root));
                        }
                    }
                    predicates.add(criteriaBuilder.or(ctiPredicates));
                }
                break;
            default:
                throw new BadRequestException("Function not implement");
        }
    }
}