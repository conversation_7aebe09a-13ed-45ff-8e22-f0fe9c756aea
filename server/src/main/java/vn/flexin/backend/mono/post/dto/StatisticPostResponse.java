package vn.flexin.backend.mono.post.dto;

import lombok.Data;

import java.util.List;

@Data
public class StatisticPostResponse {
    private Long id;
    private int views;
    private int applications;
    private int shortlisted;
    private int rejected;
    private int hired;
    private List<CountResponse> viewsPerDay;
    private List<CountResponse> applicationsPerDay;
    private double conversionRate;

    @Data
    public static class CountResponse {
        private String date;
        private int count;

        public CountResponse(String date, Integer count) {
            this.date = date;
            this.count = count;
        }
    }
}
