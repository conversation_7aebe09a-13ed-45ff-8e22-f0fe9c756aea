package vn.flexin.backend.mono.payment.dto.request;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;
import vn.flexin.backend.mono.payment.enums.TransactionType;

@Data
public class RedeemPointRequest {
    private Long userId;
    @NotNull
    @Positive(message = "Redeemed point must greater than 0")
    private Long point;

    private TransactionType type;
}
