package vn.flexin.backend.mono.resume.repository;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import vn.flexin.backend.mono.common.repository.JpaSpecificationRepository;
import vn.flexin.backend.mono.resume.entity.Resume;
import vn.flexin.backend.mono.user.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;
import java.util.Optional;

@Repository
public interface ResumeRepository extends JpaSpecificationRepository<Resume, Long> {
    List<Resume> findByUser(User user);
    List<Resume> findByUserAndIsActiveTrue(User user);

    @Query("""
        SELECT resume
        FROM Resume resume
        WHERE resume.user.id = :userId
        AND (:isActive IS NULL OR resume.isActive = :isActive)
    """)
    List<Resume> findAllByUserIdAndStatus(@Param("userId") Long userId,
                                          @Param("isActive") Boolean isActive);

    @Query("""
        SELECT r
        FROM Resume r
        JOIN r.skills s
        WHERE s IN :skills
    """)
    List<Resume> findBySkillsIn(@Param("skills") Set<String> skills);

    @Query("""
        SELECT r
        FROM Resume r
        JOIN r.partTimePreference p
        JOIN p.preferredLocations l
        WHERE l IN :preferredLocations
    """)
    List<Resume> findByPreferredLocationsIn(@Param("preferredLocations") Set<String> preferredLocations);

    @Query("""
        SELECT DISTINCT r
        FROM Resume r
        LEFT JOIN FETCH r.user
        LEFT JOIN FETCH r.contactInfo
        LEFT JOIN FETCH r.partTimePreference
        WHERE r.id = :id
    """)
    Optional<Resume> findByIdWithBasicDetails(@Param("id") Long id);

    @Query("""
        SELECT r
        FROM Resume r
        LEFT JOIN FETCH r.workExperiences
        WHERE r.id = :id
    """)
    Optional<Resume> findByIdWithWorkExperiences(@Param("id") Long id);

    @Query("""
        SELECT r
        FROM Resume r
        LEFT JOIN FETCH r.educations
        WHERE r.id = :id
    """)
    Optional<Resume> findByIdWithEducations(@Param("id") Long id);

    @Query("""
        SELECT r
        FROM Resume r
        LEFT JOIN FETCH r.languages
        WHERE r.id = :id
    """)
    Optional<Resume> findByIdWithLanguages(@Param("id") Long id);
}