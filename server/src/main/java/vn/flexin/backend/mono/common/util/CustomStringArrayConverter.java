package vn.flexin.backend.mono.common.util;

import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

@Converter
public class CustomStringArrayConverter implements AttributeConverter<String[], String> {

    // Convert String[] to String (for storing in DB)
    @Override
    public String convertToDatabaseColumn(String[] attribute) {
        if (attribute == null) {
            return null;
        }
        return String.join(",", attribute);  // Joining the array as a comma-separated string
    }

    // Convert String (from DB) to String[] (for entity)
    @Override
    public String[] convertToEntityAttribute(String dbData) {
        if (dbData == null) {
            return null;
        }
        return dbData.split(",");  // Splitting the comma-separated string back into an array
    }
}
