package vn.flexin.backend.mono.company.service.mobile;

import vn.flexin.backend.mono.company.entity.Company;
import vn.flexin.backend.mono.company.entity.VerificationRequest;
import vn.flexin.backend.mono.user.entity.User;

public interface VerificationRequestService {
    VerificationRequest createVerificationRequest(Company company, User requestUser);

    VerificationRequest save(VerificationRequest request);
}
