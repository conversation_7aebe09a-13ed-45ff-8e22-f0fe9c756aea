package vn.flexin.backend.mono.contract.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import jakarta.persistence.*;
import vn.flexin.backend.mono.contract.dto.ContractResponse;
import vn.flexin.backend.mono.user.entity.User;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Entity
@Table(name = "t_contracts")
@EntityListeners(AuditingEntityListener.class)
@Data
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
public class Contract {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private String title;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "employer_id", nullable = false)
    private User employer;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "job_seeker_id", nullable = false)
    private User jobSeeker;

    @Column(nullable = false)
    private LocalDate startDate;

    @Column(nullable = false)
    private LocalDate endDate;

    @Column(nullable = false)
    private Double hourlyRate;

    @Column(nullable = false)
    private String paymentFrequency; // weekly, biweekly, monthly

    @Column(nullable = false)
    private Integer workingHoursPerWeek;

    @ElementCollection
    @CollectionTable(name = "t_contract_work_days", joinColumns = @JoinColumn(name = "contract_id"))
    @Column(name = "work_day")
    private Set<String> workDays = new HashSet<>();

    @Column(nullable = false)
    private String contractType; // part-time, freelance, project

    @Column(nullable = false)
    private String status = "draft"; // draft, offered, active, completed, terminated

    private LocalDateTime activatedAt;

    private LocalDateTime terminatedAt;

    private String terminationReason;

    private boolean isFeePaid = false;

    @OneToMany(mappedBy = "contract", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<TimeEntry> timeEntries = new ArrayList<>();

    @OneToMany(mappedBy = "contract", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<PaymentRecord> payments = new ArrayList<>();

    @OneToMany(mappedBy = "contract", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<ContractMessage> messages = new ArrayList<>();

    @Column(columnDefinition = "JSON")
    private String additionalTerms;

    @CreatedDate
    private LocalDateTime createdAt;

    @LastModifiedDate
    private LocalDateTime updatedAt;

    // Helper methods to maintain bidirectional relationships
    public void addTimeEntry(TimeEntry timeEntry) {
        timeEntries.add(timeEntry);
        timeEntry.setContract(this);
    }

    public void removeTimeEntry(TimeEntry timeEntry) {
        timeEntries.remove(timeEntry);
        timeEntry.setContract(null);
    }

    public void addPayment(PaymentRecord payment) {
        payments.add(payment);
        payment.setContract(this);
    }

    public void removePayment(PaymentRecord payment) {
        payments.remove(payment);
        payment.setContract(null);
    }

    public void addMessage(ContractMessage message) {
        messages.add(message);
        message.setContract(this);
    }

    public void removeMessage(ContractMessage message) {
        messages.remove(message);
        message.setContract(null);
    }

    public ContractResponse toResponse() {
        ContractResponse response = new ContractResponse();
        response.setId(id);
        response.setTitle(title);
        response.setEmployerId(employer.getUserId());
        response.setEmployerName(employer.getName());
        response.setJobSeekerId(jobSeeker.getUserId());
        response.setJobSeekerName(jobSeeker.getName());
        response.setStartDate(startDate);
        response.setEndDate(endDate);
        response.setHourlyRate(hourlyRate);
        response.setPaymentFrequency(paymentFrequency);
        response.setWorkingHoursPerWeek(workingHoursPerWeek);
        response.setContractType(contractType);
        response.setStatus(status);
        response.setActivatedAt(activatedAt);
        response.setTerminatedAt(terminatedAt);
        response.setTerminationReason(terminationReason);
        response.setAdditionalTerms(additionalTerms);
        response.setCreatedAt(createdAt);
        response.setUpdatedAt(updatedAt);
        return response;
    }
} 