package vn.flexin.backend.mono.lookup.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.common.dto.PaginationApiResponseDto;
import vn.flexin.backend.mono.lookup.dto.*;

import java.util.List;
import java.util.Map;

@Tag(name = "Lookup APIs", description = "Lookup management endpoints")
@RequestMapping("/v1/lookups")
public interface LookupController {

    @Operation(summary = "Get all lookups", description = "Get a list of all lookups")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved lookups"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @GetMapping
    ResponseEntity<ApiResponseDto<List<LookupResponse>>> getAllLookups(@RequestParam("type") String type,
                                                                       @RequestParam(value = "dependValue", required = false) String dependValue);

    @Operation(summary = "Get all lookups by user", description = "Get a list of all lookups by user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved lookups"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @GetMapping("/user")
    ResponseEntity<ApiResponseDto<List<LookupResponse>>> getAllLookupByUsers(@RequestParam("type") String type,
                                                                             @RequestParam("userId") Long userId);

    @Operation(summary = "Get all lookup types", description = "Get a list of all lookup types")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved lookup types"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @GetMapping("/types")
    ResponseEntity<ApiResponseDto<List<String>>> getAllLookupsType();

    @Operation(summary = "Create lookup types", description = "Create lookup types")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Create Successfully lookup types"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PostMapping
    ResponseEntity<ApiResponseDto<LookupResponse>> createUserLookup(@Valid @RequestBody CreateUserLookupRequest request);

    @Operation(summary = "Create lookup types", description = "Create lookup types")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Create Successfully lookup types"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PostMapping("/admin")
    @PreAuthorize("hasAuthority('lookup_create')")
    ResponseEntity<ApiResponseDto<LookupResponse>> createAdminLookup(@Valid @RequestBody CreateAdminLookupRequest request);

    @Operation(summary = "Get lookups for review", description = "Retrieves a paginated list of lookups pending admin review")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Lookups retrieved successfully"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PostMapping("/admin/review")
    @PreAuthorize("hasAuthority('company_read')")
    ResponseEntity<PaginationApiResponseDto<List<LookupResponse>>> getLookupsForReview(@Valid @RequestBody LookupFilter filters);

    @Operation(summary = "Update lookup", description = "Update lookup active status and display text")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Lookup updated successfully"),
            @ApiResponse(responseCode = "403", description = "Access denied"),
            @ApiResponse(responseCode = "404", description = "Lookup not found")
    })
    @PutMapping("/admin/{id}")
    @PreAuthorize("hasAuthority('company_update')")
    ResponseEntity<ApiResponseDto<LookupResponse>> updateLookup(
            @PathVariable("id") Long id,
            @Valid @RequestBody UpdateLookupRequest request
    );
}
