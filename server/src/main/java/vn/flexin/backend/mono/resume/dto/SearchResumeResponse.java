package vn.flexin.backend.mono.resume.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.flexin.backend.mono.resume.entity.Resume;

import java.time.LocalDateTime;
import java.util.Set;

@Data
@EqualsAndHashCode(callSuper = false)
public class SearchResumeResponse {
    private Long id;
    private String name;
    private String description;
    private Set<String> skills;
    @JsonProperty("isActive")
    private boolean isActive;
    private LocalDateTime lastUpdateAt;
    private Long userId;

    public SearchResumeResponse(Resume resume) {
        this.id = resume.getId();
        this.name = resume.getName();
        this.description = resume.getDescription();
        this.skills = resume.getSkills();
        this.isActive = resume.isActive();
        this.lastUpdateAt = resume.getLastModifiedAt();
        this.userId = resume.getUser().getId();
    }
}
