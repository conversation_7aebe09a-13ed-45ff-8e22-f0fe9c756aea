package vn.flexin.backend.mono.contract.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ContractMessageResponse {

    private Long id;

    private Long senderId;

    private String senderName;

    private String content;

    private Boolean isRead;

    private LocalDateTime createdAt;
} 