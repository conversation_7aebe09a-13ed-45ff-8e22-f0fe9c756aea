package vn.flexin.backend.mono.post.controller.admin;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.common.dto.CreateObjectResponse;
import vn.flexin.backend.mono.common.dto.PaginationApiResponseDto;
import vn.flexin.backend.mono.post.dto.filters.JobSeekerPostFilter;
import vn.flexin.backend.mono.post.dto.request.jobseeker.CreateJobSeekerPostRequest;
import vn.flexin.backend.mono.post.dto.request.jobseeker.UpdateJobSeekerPostRequest;
import vn.flexin.backend.mono.post.dto.response.jobseeker.JobSeekerDetailResponse;
import vn.flexin.backend.mono.post.dto.response.jobseeker.JobSeekerStatisticPostResponse;
import vn.flexin.backend.mono.post.dto.response.jobseeker.SearchJobSeekerPostResponse;

import java.util.List;

@Tag(name = "Admin JobSeekerPost APIs", description = "Admin JobSeekerPost Management")
@RequestMapping("/v1/admin/job-seeker-posts")
public interface AdminJobSeekerPostController {
    @Operation(summary = "Search job seeker posts", description = "Admin search job seeker posts")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Posts fetched successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "400", description = "Invalid input")
    })
    @PostMapping("/search")
    @PreAuthorize("hasAuthority('jobseeker_post_read')")
    ResponseEntity<PaginationApiResponseDto<List<SearchJobSeekerPostResponse>>> searchPosts(@Valid @RequestBody JobSeekerPostFilter filters);

    @Operation(summary = "Get detail post", description = "Admin get detail job seeker post")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Post fetched successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "404", description = "Post not found"),
    })
    @GetMapping("/{id}")
    @PreAuthorize("hasAuthority('jobseeker_post_read')")
    ResponseEntity<ApiResponseDto<JobSeekerDetailResponse>> getDetailPost(@PathVariable("id") Long id);

    @Operation(summary = "Create job seeker post", description = "Admin create job seeker post")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Post created successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "400", description = "Invalid post data")
    })
    @PostMapping
    @PreAuthorize("hasAuthority('jobseeker_post_create')")
    ResponseEntity<ApiResponseDto<CreateObjectResponse>> createPost(@RequestBody CreateJobSeekerPostRequest request);

    @Operation(summary = "Update job seeker post", description = "Admin update job seeker post")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Post updated successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "404", description = "Post not found"),
    })
    @PutMapping("/{id}")
    @PreAuthorize("hasAuthority('jobseeker_post_update')")
    ResponseEntity<ApiResponseDto<Boolean>> updatePost(@PathVariable("id") Long id, @RequestBody UpdateJobSeekerPostRequest request);

    @Operation(summary = "Delete job seeker post", description = "Admin delete job seeker post")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Post deleted successfully"),
            @ApiResponse(responseCode = "404", description = "Post not found"),
    })
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('jobseeker_post_delete')")
    ResponseEntity<ApiResponseDto<Boolean>> deletePost(@PathVariable("id") Long id);

    @Operation(summary = "Get job seeker post statistics", description = "Admin get job seeker post statistics")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Statistics fetched successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "404", description = "Post not found"),
    })
    @GetMapping("/statistics")
    @PreAuthorize("hasAuthority('jobseeker_post_statistics')")
    ResponseEntity<ApiResponseDto<JobSeekerStatisticPostResponse>> getStatistics();
} 