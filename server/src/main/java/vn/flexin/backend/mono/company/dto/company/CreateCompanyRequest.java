package vn.flexin.backend.mono.company.dto.company;

import io.swagger.v3.oas.annotations.Hidden;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import vn.flexin.backend.mono.address.dto.request.AddressRequest;
import vn.flexin.backend.mono.file.dto.FileDto;

import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class CreateCompanyRequest {
    @Hidden
    private Long id;
    @NotBlank(message = "Company name can not be blank")
    private String name;
    @NotBlank(message = "Industry can not be blank")
    private String industry;
    private String description;
    private AddressRequest address;
    private FileDto logo;
    private FileDto coverImage;
    private List<FileDto> galleryImages;
    private String websiteUrl;
    private String email;
    private String phoneNumber;
    private String foundedYear;
    private boolean isIndividual = false;
    private Map<String, Object> additionalData;
}
