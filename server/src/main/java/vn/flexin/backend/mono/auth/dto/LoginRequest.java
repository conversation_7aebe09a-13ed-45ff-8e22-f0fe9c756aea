package vn.flexin.backend.mono.auth.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.flexin.backend.mono.auth.util.AuthConstant;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LoginRequest {
    @NotBlank(message = "Phone number is required")
    @Pattern(regexp = AuthConstant.PHONE_REGEX, message = "Invalid phone number format")
    private String phone;

    @NotBlank(message = "Password is required")
    private String password;

    @NotBlank(message = "Device ID is required")
    private String deviceId;

    private String deviceName = "Unknown Device";

    private String location;
} 