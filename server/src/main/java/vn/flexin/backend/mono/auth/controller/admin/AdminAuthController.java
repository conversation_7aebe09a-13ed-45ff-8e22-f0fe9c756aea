package vn.flexin.backend.mono.auth.controller.admin;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirements;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestHeader;
import vn.flexin.backend.mono.auth.dto.*;
import vn.flexin.backend.mono.auth.dto.ChangePasswordRequest;

import jakarta.validation.Valid;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;

@Tag(name = "Admin Authentication APIs", description = "Admin authentication endpoints")
@RequestMapping("/v1/admin/auth")
public interface AdminAuthController {

    @Operation(summary = "Login for admin", description = "Login with email and password for admin portal")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Login successful",
                    content = @Content(schema = @Schema(implementation = LoginResponse.class))),
            @ApiResponse(responseCode = "400", description = "Invalid credentials"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user")
    })
    @PostMapping("/login")
    ResponseEntity<ApiResponseDto<LoginResponse>> login(@Valid @RequestBody AdminLoginRequest loginRequest);

    @Operation(summary = "Forgot password for admin", description = "Password reset instructions sent to your email")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Password reset instructions sent to your email"),
    })
    @PostMapping("/forgot-password")
    ResponseEntity<ApiResponseDto<Boolean>> forgotPassword(@RequestBody AdminForgotPasswordRequest request);

    @Operation(summary = "Reset password for admin", description = "Password reset with token")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Password reset successfully"),
    })
    @PostMapping("/reset-password")
    ResponseEntity<ApiResponseDto<Boolean>> resetPassword(@RequestParam("token") String token,
                                                         @RequestParam("email") String email,
                                                         @RequestParam("newPassword") String newPassword);

    @Operation(summary = "Refresh token for admin", description = "Get token by Refresh token")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Token refreshed successfully"),
    })
    @PostMapping("/refresh-token")
    ResponseEntity<ApiResponseDto<TokenDto>> refreshToken(@Valid @RequestBody RefreshTokenRequest refreshTokenRequest);

    @Operation(summary = "Logout", description = "Logout current session")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Logged out successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @PostMapping("/logout")
    ResponseEntity<ApiResponseDto<Boolean>> logout();

    @Operation(summary = "Change password", description = "Change current user's password")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Password changed successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input or passwords don't match"),
            @ApiResponse(responseCode = "401", description = "Current password is incorrect")
    })
    @PostMapping("/change-password")
    ResponseEntity<ApiResponseDto<Boolean>> changePassword(@Valid @RequestBody ChangePasswordRequest changePasswordRequest);

    @Operation(summary = "Update profile", description = "Update current admin's profile information")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Profile updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input data"),
            @ApiResponse(responseCode = "401", description = "Unauthorized")
    })
    @PostMapping("/update-profile")
    ResponseEntity<ApiResponseDto<Boolean>> updateProfile(@Valid @RequestBody AdminUpdateProfileRequest updateProfileRequest);

} 