package vn.flexin.backend.mono.lookup.service;

import vn.flexin.backend.mono.common.dto.PaginationResponse;
import vn.flexin.backend.mono.lookup.dto.*;
import vn.flexin.backend.mono.lookup.entity.Lookup;
import org.springframework.data.util.Pair;

import java.util.List;

public interface LookupService {
    List<String> getAllTypes();
    List<LookupResponse> getLookupsByType(String type, String dependValue);
    List<LookupResponse> getLookupsByType(String type, Long userId);
    List<Lookup> getAllByType(String type);
    LookupResponse createUserLookup(CreateUserLookupRequest request);
    LookupResponse createAdminLookup(CreateAdminLookupRequest request);
    Pair<List<LookupResponse>, PaginationResponse> getLookupsForReview(LookupFilter filters);
    LookupResponse updateLookup(Long id, UpdateLookupRequest request);
}
