package vn.flexin.backend.mono.notification.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;
import vn.flexin.backend.mono.auth.util.AuthConstant;
import vn.flexin.backend.mono.common.exception.BadRequestException;
import vn.flexin.backend.mono.common.exception.ResourceNotFoundException;
import vn.flexin.backend.mono.common.exception.message.ErrorMessage;
import vn.flexin.backend.mono.common.util.CommonUtil;
import vn.flexin.backend.mono.common.util.ObjectMapperUtils;
import vn.flexin.backend.mono.notification.config.SmsConfig;
import vn.flexin.backend.mono.notification.dto.request.SmsWebHookRequest;
import vn.flexin.backend.mono.notification.entity.*;
import vn.flexin.backend.mono.notification.enums.NotificationConstant;
import vn.flexin.backend.mono.notification.enums.SmsType;
import vn.flexin.backend.mono.notification.mapper.SendSmsDataMapper;
import vn.flexin.backend.mono.notification.repository.SmsTemplateRepository;
import vn.flexin.backend.mono.notification.repository.SmsTransactionLogRepository;
import vn.flexin.backend.mono.notification.repository.SmsTransactionRepository;
import vn.flexin.backend.mono.notification.repository.SmsRepository;
import vn.flexin.backend.mono.notification.service.SmsService;

import java.net.URI;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;


@Slf4j
@Service
@AllArgsConstructor
public class SpeedSmsServiceImpl implements SmsService {
    private final SmsTemplateRepository smsTemplateRepository;

    private final SmsRepository smsRepository;
    private final SmsTransactionRepository smsTransactionRepository;
    private final SmsTransactionLogRepository smsTransactionLogRepository;

    private final WebClient webClient;
    private final SmsConfig smsConfig;

    private static final String SUCCESS_RESPONSE_STRING = """
            {"status":"success","sms":1,"cost":720,"invalid_phones":[],"tranId":"OhUlZdqHgG5g499Z2eIrPujaXfM4z94T686411ce98d57"}
            """;


    @Override
    public String getUrl() {
        return smsConfig.getSpeedSmsUrl();
    }

    @Override
    public Map<String, Object> getUserInfo() {
        String url = getUrl() + "/user/info";

        try {
            String response = webClient.get()
                    .uri(url)
                    .headers(getRequestHeaders())
                    .retrieve()
                    .bodyToMono(String.class)
                    .block();
            return ObjectMapperUtils.toMap(response);
        } catch (Exception ex) {
            throw new ResourceNotFoundException("Sms Information user not found");
        }
    }

    @Override
    public void sendOtp(String phone, String otp) {
        SmsTemplate otpTemplate = smsTemplateRepository.findByType(SmsType.SEND_CONFIRM_OTP);
        if (otpTemplate == null) {
            throw new ResourceNotFoundException("Sms Template Not Found");
        }
        Map<String, Object> data = SendSmsDataMapper.getSendOtpMapData(otp);
        String content = SmsType.SEND_CONFIRM_OTP.replacePlaceHolders(otpTemplate.getContent(), data);

        sendSms(List.of(phone), content, CommonUtil.getCurrentUTCTime());
    }

    private Consumer<HttpHeaders> getRequestHeaders() {
        String userCredentials = smsConfig.getApiKey();
        return headers -> {
            headers.set(AuthConstant.API_KEY, userCredentials);
            headers.setContentType(MediaType.APPLICATION_JSON);
        };
    }

    private void sendSms(List<String> phones, String content, LocalDateTime sendTime) {
        List<Sms> smses = toSmses(phones, content, sendTime);
        try {
            log.info("====== START SEND SMS ======");
            content = encodeNonAsciiCharacters(content);
            SpeedSmsData speedSmsData = new SpeedSmsData(phones, content);
            log.info("Sent data: {}", speedSmsData);
            URI uri = new URI(getUrl() + "/sms");

            ResponseEntity<String> response =
                    ResponseEntity.ok(SUCCESS_RESPONSE_STRING);
//                    webClient
//                            .post()
//                            .uri(uri)
//                            .header(AuthConstant.API_KEY, smsConfig.getApiKey())
//                            .header(AuthConstant.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
////                            .headers(getRequestHeaders())
//                            .bodyValue(speedSmsData)
//                            .retrieve()
//                            .toEntity(String.class)
//                            .onErrorResume( e -> {
//                                WebClientResponseException ex = (WebClientResponseException) e;
//                                HttpStatusCode status = ex.getStatusCode();
//                                String body = ex.getResponseBodyAsString();
//                                return Mono.just(ResponseEntity.status(status).body(body));
//                            })
//                            .block();
            if (response == null || response.getStatusCode().is5xxServerError()) {
                throw new BadRequestException(ErrorMessage.CAN_NOT_REGISTER);
            }
            handleSendResult(response.getBody(), smses);
        } catch (Exception ex) {
            log.error(ex.getMessage());
            log.error("SEND SMS ERROR");
            throw new BadRequestException(ErrorMessage.CAN_NOT_REGISTER);
        }
        log.info("====== FINISHED SEND SMS ======");
    }

    private List<Sms> toSmses(List<String> phones, String content, LocalDateTime sendTime) {
        return phones.stream().map(phone -> new Sms(phone, content, sendTime)).collect(Collectors.toList());
    }

    private void updateFailSms(List<Sms> smses) {
        smses.forEach(sms -> {
            int failCount = sms.getFailCount();
            sms.setSuccess(false);
            sms.setFailCount(++failCount);
        });
    }

    private void handleSendResult(String response, List<Sms> smses) throws JsonProcessingException {
        log.info("Receive Response data string: {}", response);
        SpeedSmsResponseData responseData = CommonUtil.fromJsonString(response, new TypeReference<SpeedSmsResponseData>() {});
        if (responseData == null) {
            updateFailSms(smses);
            smsRepository.saveAll(smses);
            log.error("CAN NOT GET RESPONSE FROM SPEED SMS SERVER");
            return;
        }

        smses.forEach(sms -> sms.setSuccess(true));
        smsRepository.saveAll(smses);
        SmsTransaction transaction = new SmsTransaction(responseData);
        smsTransactionRepository.save(transaction);
        List<SmsTransactionLog> logs = new ArrayList<>();
        smses.forEach(sms -> logs.add(new SmsTransactionLog(transaction, sms)));
        smsTransactionLogRepository.saveAll(logs);
    }

    private String encodeNonAsciiCharacters(String value) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < value.length(); i++) {
            char c = value.charAt(i);
            int unit = (int) c;
            if (unit > 127) {
                String hex = String.format("%04x", (int) unit);
                String encodedValue = "\\u" + hex;
                sb.append(encodedValue);
            } else {
                sb.append(c);
            }
        }
        return sb.toString();
    }


    @Override
    public void webhook(SmsWebHookRequest data) {
        log.info("Receive SMS Webhook : {}",data);
        if (data.getTranId() == null || !Objects.equals(data.getChannel(), NotificationConstant.SMS)) {
            return;
        }
        SmsTransactionLog transactionLog = smsTransactionLogRepository.findByTranIdAndSmsReceivePhone(data.getTranId(), data.getPhone());
        if (transactionLog == null) {
            log.error("Not found transaction log for tranId: {}", data.getTranId());
            return;
        }
        Sms sms = transactionLog.getSms();
        sms.setSuccess(data.getStatus() == 0);
        smsRepository.save(sms);

        transactionLog.setStatus(data.getStatusValue());
        transactionLog.setReceiveTime(data.getDeliveryTime());
        smsTransactionLogRepository.save(transactionLog);
        log.info("End sync data from SMS Webhook");
    }
}
