package vn.flexin.backend.mono.post.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.time.LocalDate;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Getter
@Setter
public class SearchAdminPostRequest {
    private String search;
    private String status;
    private String workType;
    private String jobType;
    private String experienceLevel;

    @JsonProperty("isFeatureJob")
    private Boolean isFeatureJob;

    @JsonProperty("urgentHiring")
    private Boolean urgentHiring;

    private List<LocalDate> dateRange;
    private Integer page = 1;
    private Integer pageSize = 10;

}
