package vn.flexin.backend.mono.common.config;

import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.annotation.web.configurers.CorsConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.client.web.OAuth2LoginAuthenticationFilter;
import org.springframework.security.provisioning.InMemoryUserDetailsManager;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.web.cors.CorsConfiguration;
import vn.flexin.backend.mono.common.security.JwtAuthenticationFilter;
import vn.flexin.backend.mono.common.security.PermitAllApiHeaderFilter;
import vn.flexin.backend.mono.common.security.UserDeviceFilter;

import java.util.List;

@Configuration
@EnableWebSecurity
@EnableMethodSecurity
@AllArgsConstructor
public class SecurityConfig {

    private final RealmRoleConverter authenticationConverter;
    private final PasswordEncoder passwordEncoder;
    private final OpenApiConfig openApiConfig;
    private final JwtAuthenticationFilter jwtAuthenticationFilter;
    private final UserDeviceFilter userDeviceFilter;
    private final PermitAllApiHeaderFilter permitAllApiHeaderFilter;

    @Bean
    @Order(1)
    public SecurityFilterChain configureSwaggerSecurity(HttpSecurity http) throws Exception {
        http
                .cors(configureCors())
                .authorizeHttpRequests(authz -> authz
                .requestMatchers(
                        "/swagger-ui.html",
                        "/swagger-ui/**",
                        "/v3/api-docs/**",
                        "/webjars/**"
                ).authenticated()
                .anyRequest().permitAll()
            )
            .csrf(AbstractHttpConfigurer::disable)
            .formLogin(Customizer.withDefaults());
        return http.build();
    }

    @Bean
    public UserDetailsService userDetailsService() {
        UserDetails userDetails = User.builder()
                .username(openApiConfig.getSwaggerUser())
                .password(passwordEncoder.encode(openApiConfig.getSwaggerPassword()))
                .build();

        return new InMemoryUserDetailsManager(userDetails);
    }

    @Bean
    @Order(2)
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http
            .csrf(AbstractHttpConfigurer::disable)
            .formLogin(AbstractHttpConfigurer::disable)
            .cors(configureCors())
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .authorizeHttpRequests(auth -> auth
                // Swagger UI endpoints
                .requestMatchers("/swagger-ui.html", "/swagger-ui/**", "/v3/api-docs/**", "/webjars/**").permitAll()
                // Auth endpoints
                .requestMatchers("/v1/mobile/auth/login").permitAll()
                .requestMatchers("/v1/mobile/auth/register").permitAll()
                .requestMatchers("/v1/mobile/auth/verify-otp").permitAll()
                .requestMatchers("/v1/mobile/auth/create-password").permitAll()
                .requestMatchers("/v1/mobile/auth/select-role").permitAll()
                .requestMatchers("/v1/mobile/auth/request-otp").permitAll()
                .requestMatchers("/v1/mobile/auth/reset-password").permitAll()
                .requestMatchers("/v1/mobile/auth/refresh-token").permitAll()
                .requestMatchers("/v1/sms/webhook").permitAll()
                    // Admin endpoint
                .requestMatchers("/v1/admin/auth/login").permitAll()
                .requestMatchers("/v1/admin/auth/refresh-token").permitAll()
                .requestMatchers("/v1/admin/auth/forgot-password").permitAll()
                .requestMatchers("/v1/admin/auth/reset-password").permitAll()
                // Account creation endpoint
                .requestMatchers("/v1/mobile/users/register").permitAll()
                // All other requests need authentication
                .anyRequest().authenticated()
//                            .anyRequest().permitAll()
            )
            .addFilterBefore(jwtAuthenticationFilter, OAuth2LoginAuthenticationFilter.class)
            .oauth2ResourceServer(resourceServer ->
                resourceServer.jwt(jwtDecoder ->
                        jwtDecoder.jwtAuthenticationConverter(authenticationConverter)
                )
            )
            .addFilterAfter(userDeviceFilter, OAuth2LoginAuthenticationFilter.class)
            .addFilterBefore(permitAllApiHeaderFilter, OAuth2LoginAuthenticationFilter.class)
        ;


        return http.build();
    }

    private Customizer<CorsConfigurer<HttpSecurity>> configureCors() {
        return cors -> cors.configurationSource(request -> {
            CorsConfiguration configuration = new CorsConfiguration();
            configuration.setAllowedOrigins(List.of("*"));
            configuration.setAllowedMethods(List.of("*"));
            configuration.setAllowedHeaders(List.of("*"));
            return configuration;
        });
    }

} 