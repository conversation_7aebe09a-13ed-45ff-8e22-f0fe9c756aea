package vn.flexin.backend.mono.payment.dto.request;

import lombok.Data;
import org.springframework.data.jpa.domain.Specification;
import vn.flexin.backend.mono.common.dto.BaseFilter;
import vn.flexin.backend.mono.common.repository.param.Condition;
import vn.flexin.backend.mono.common.repository.param.Join;
import vn.flexin.backend.mono.common.repository.param.Operator;
import vn.flexin.backend.mono.common.repository.param.Where;
import vn.flexin.backend.mono.common.util.SpecificationUtil;
import vn.flexin.backend.mono.payment.entity.personal.PersonalTransaction;
import vn.flexin.backend.mono.payment.entity.personal.PersonalWallet;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class PersonalTransactionFilter extends BaseFilter<PersonalTransaction> {

    private Long walletId;
    private LocalDateTime fromDate;
    private LocalDateTime toDate;

    @Override
    public Specification<PersonalTransaction> toSpecification() {
        var condition = new Condition();

        if (walletId != null) {
            condition = condition.append(new Join(PersonalTransaction.Fields.wallet, List.of(new Where(PersonalWallet.Fields.id, walletId))));
        }
        if (fromDate != null) {
            condition = condition.append(new Where(CREATED_AT, Operator.GREATER_THAN_OR_EQUAL, fromDate));
        }
        if (toDate != null) {
            condition = condition.append(new Where(CREATED_AT, Operator.LESS_THAN_OR_EQUAL, toDate));
        }
        return SpecificationUtil.bySearchQuery(condition);
    }
}
