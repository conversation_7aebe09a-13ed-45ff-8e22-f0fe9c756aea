package vn.flexin.backend.mono.common.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.NoRepositoryBean;
import vn.flexin.backend.mono.common.dto.BaseFilter;

@NoRepositoryBean
public interface JpaSpecificationRepository<T, ID> extends JpaRepository<T, ID>, JpaSpecificationExecutor<T> {
    default Page<T> findAll(BaseFilter<T> filter) {
        return this.findAll(filter.toSpecification(), filter.toPageable());
    }
}