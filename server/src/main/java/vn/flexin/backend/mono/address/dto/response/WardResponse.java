package vn.flexin.backend.mono.address.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.flexin.backend.mono.address.entity.Ward;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WardResponse {
    private Long code;
    private String name;
    private String codename;
    private String divisionType;
    private Long districtCode;

    public WardResponse(Ward ward) {
        this.code = ward.getCode();
        this.name = ward.getName();
        this.codename = ward.getCodename();
        this.divisionType = ward.getDivisionType();
        if (ward.getDistrict() != null) {
            this.districtCode = ward.getDistrict().getCode();
        }
    }
}
