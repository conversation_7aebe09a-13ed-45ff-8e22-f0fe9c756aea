package vn.flexin.backend.mono.auth.controller.admin;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import vn.flexin.backend.mono.auth.dto.*;
import vn.flexin.backend.mono.auth.dto.ChangePasswordRequest;
import vn.flexin.backend.mono.auth.service.admin.AdminAuthService;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;


@RestController
@RequiredArgsConstructor
@Slf4j
public class AdminAuthControllerImpl implements AdminAuthController {

    private final AdminAuthService adminAuthService;

    @Override
    public ResponseEntity<ApiResponseDto<LoginResponse>> login(@Valid AdminLoginRequest loginRequest) {
        return ResponseEntity.ok(ApiResponseDto.success(adminAuthService.login(loginRequest), "Login Successfully"));
    }

    public ResponseEntity<ApiResponseDto<TokenDto>> refreshToken(RefreshTokenRequest refreshTokenRequest) {
        return ResponseEntity.ok(ApiResponseDto.success(adminAuthService.refreshToken(refreshTokenRequest), "Token refreshed"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> logout() {
        adminAuthService.logout();
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE, "Logged out"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> forgotPassword(AdminForgotPasswordRequest request) {
        adminAuthService.forgotPassword(request);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE, "Password reset instructions sent to your email"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> resetPassword(String token, String email, String newPassword) {
        adminAuthService.resetPassword(token, email, newPassword);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE, "Password reset successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> changePassword(@Valid ChangePasswordRequest changePasswordRequest) {
        adminAuthService.changePassword(changePasswordRequest);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE, "Password changed successfully"));
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> updateProfile(@Valid AdminUpdateProfileRequest updateProfileRequest) {
        adminAuthService.updateProfile(updateProfileRequest);
        return ResponseEntity.ok(ApiResponseDto.success(Boolean.TRUE, "Profile updated successfully"));
    }

}