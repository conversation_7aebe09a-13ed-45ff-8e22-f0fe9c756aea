package vn.flexin.backend.mono.post.service.impl;

import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.flexin.backend.mono.common.exception.ResourceNotFoundException;
import vn.flexin.backend.mono.post.entity.Post;
import vn.flexin.backend.mono.post.entity.PostInterest;
import vn.flexin.backend.mono.post.repository.PostRepository;
import vn.flexin.backend.mono.post.repository.PostInterestRepository;
import vn.flexin.backend.mono.post.service.PostInterestService;

import vn.flexin.backend.mono.user.entity.User;
import vn.flexin.backend.mono.user.repository.user.UserRepository;
import vn.flexin.backend.mono.user.service.UserService;

import java.util.Optional;

@AllArgsConstructor
@Service
@Transactional
public class PostInterestServiceImpl implements PostInterestService {
    private final PostRepository postRepository;
    private final UserRepository userRepository;
    private final UserService userService;
    private final PostInterestRepository postInterestRepository;


    @Override
    public void createPostInterest(Long postId) {
        Post post = postRepository.findById(postId)
                .orElseThrow(() -> new ResourceNotFoundException("Post not found"));

        User currentLoginUser = userService.getCurrentLoginUser();
        User user = userRepository.findById(currentLoginUser.getId())
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));

        Optional<PostInterest> existingPostInterest = postInterestRepository.findPostInterested(currentLoginUser.getId(), postId);
        if (existingPostInterest.isPresent()) {
            return;
        }

        PostInterest postInterest = new PostInterest();
        postInterest.setPost(post);
        postInterest.setUser(user);

        postInterestRepository.save(postInterest);
    }

    @Override
    public void deletePostInterest(Long postId) {
        Post post = postRepository.findById(postId)
                .orElseThrow(() -> new ResourceNotFoundException("Post not found"));

        User currentLoginUser = userService.getCurrentLoginUser();

        PostInterest postInterest = postInterestRepository.findPostInterested(currentLoginUser.getId(), post.getId())
                .orElseThrow(() -> new ResourceNotFoundException("Post interested not found"));

        postInterestRepository.delete(postInterest);
    }
}
