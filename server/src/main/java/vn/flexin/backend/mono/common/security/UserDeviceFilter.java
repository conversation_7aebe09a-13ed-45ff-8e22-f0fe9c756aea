package vn.flexin.backend.mono.common.security;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.keycloak.representations.idm.UserRepresentation;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import vn.flexin.backend.mono.auth.service.keycloak.UserKeycloakService;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.common.util.CommonUtil;
import vn.flexin.backend.mono.user.entity.UserDevice;
import vn.flexin.backend.mono.user.repository.UserDeviceRepository;

import java.io.IOException;
import java.io.PrintWriter;

@Component
@Slf4j
@AllArgsConstructor
public class UserDeviceFilter extends OncePerRequestFilter  implements ErrorResponseFilter{

    private UserDeviceRepository userDeviceRepository;

    private UserKeycloakService userKeycloakService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        if (isErrorResponse(response)) {
            return;
        }
        String authorizationHeader = request.getHeader("Authorization");
        if (authorizationHeader != null && notBasicUrl(authorizationHeader)) {
            UserRepresentation currentUser = userKeycloakService.getCurrentUser();
            if (currentUser != null) {
                String deviceId = CommonUtil.getDeviceIdAndPhoneNumber(currentUser).getFirst();
                if (!isValidDeviceId(deviceId, currentUser.getUsername())) {
                    userKeycloakService.logoutUser(currentUser.getId());
                    logger.warn("Invalid deviceId: " + deviceId);
                    setErrorResponse(response, "Invalid deviceId");
                    return;
                }
            } else {
                logger.warn("Authorization header is missing or invalid");
                setErrorResponse(response, "Missing or invalid Authorization header");
                return;
            }
        }
        // Proceed with the next filter in the chain
        filterChain.doFilter(request, response);
    }

    private boolean notBasicUrl(String requestURI) {
        return !requestURI.contains("Basic");
    }

    private boolean isValidDeviceId(String deviceId, String ulid) {
        UserDevice userDevice = userDeviceRepository.findFirstByUserUlidAndDeviceId(ulid, deviceId);
        if (userDevice == null) {
            return true;
        }
        return !userDevice.isLogout();
    }
}
