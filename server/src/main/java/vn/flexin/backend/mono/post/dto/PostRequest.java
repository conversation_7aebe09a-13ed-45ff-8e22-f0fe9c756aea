package vn.flexin.backend.mono.post.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.validation.annotation.Validated;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
public class PostRequest {
    @NotNull
    private Long employerId;

    @NotBlank
    private String title;

    private String description;

    private String location;

    @Valid
    @NotNull
    private SalaryDto salary;

    @NotNull
    private String jobType;

    @JsonProperty("isRemote")
    private boolean isRemote;

    private List<String> skills;

    @Valid
    private ExperienceDto experience;

    private String education;

    @JsonProperty("isFeatureJob")
    private boolean isFeatureJob;

    private List<PostWorkingInformation> workingInformation;
    private String workType;
}
