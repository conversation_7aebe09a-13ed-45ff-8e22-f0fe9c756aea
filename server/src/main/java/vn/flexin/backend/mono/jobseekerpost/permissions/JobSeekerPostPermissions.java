package vn.flexin.backend.mono.jobseekerpost.permissions;

import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.Getter;
import vn.flexin.backend.mono.common.enums.AppStatus;
import vn.flexin.backend.mono.common.exception.ResponseAppStatusException;

import java.util.Arrays;

@Getter
public enum JobSeekerPostPermissions {
    JOBSEEKER_POST_READ("jobseeker_post_read"),
    JOBSEEKER_POST_CREATE("jobseeker_post_create"),
    JOBSEEKER_POST_UPDATE("jobseeker_post_update"),
    JOBSEEKER_POST_DELETE("jobseeker_post_delete"),
    JOBSEEKER_POST_STATISTICS("jobseeker_post_statistics");

    private final String value;

    JobSeekerPostPermissions(String value) {
        this.value = value;
    }

    public static String getValues() {
        return String.join(", ", Arrays.stream(values()).map(x -> x.name().toLowerCase()).toList());
    }

    @JsonCreator
    public static JobSeekerPostPermissions fromString(String value) {
        try {
            return valueOf(value.toUpperCase());
        } catch (Exception ex) {
            throw new ResponseAppStatusException(AppStatus.BAD_REQUEST, "JobSeekerPost permissions type must be any of [" + getValues() + "]");
        }
    }
} 