package vn.flexin.backend.mono.auth.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SocialLoginRequest {
    @NotBlank(message = "Provider is required")
    @Pattern(regexp = "^(google|facebook|apple)$", message = "Provider must be either 'google', 'facebook', or 'apple'")
    private String provider;

    @NotBlank(message = "Access token is required")
    private String accessToken;

    @NotBlank(message = "Device ID is required")
    private String deviceId;

    private String deviceName;
} 