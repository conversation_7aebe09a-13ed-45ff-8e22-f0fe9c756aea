package vn.flexin.backend.mono.notification.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import vn.flexin.backend.mono.common.util.CommonUtil;
import vn.flexin.backend.mono.notification.enums.NotificationConstant;

import java.util.List;
import java.util.stream.Collectors;

@Data
@AllArgsConstructor
public class SpeedSmsData{
    private String to;
    private String content;
    private String sender;

    public SpeedSmsData (String phoneNumbers, String content) {
        this.to = phoneNumbers;
        this.content = content;
        this.sender = NotificationConstant.SMS_BRANCH_NAME;
    }

    public SpeedSmsData (List<String> phoneNumbers, String content) {
        this.to = phoneNumbers.stream().map(CommonUtil::formatPhoneNumber).collect(Collectors.joining());
        this.content = content;
        this.sender = NotificationConstant.SMS_BRANCH_NAME;
    }

}
