package vn.flexin.backend.mono.application.dto;

public enum JobApplicationFilterField {
    ID("id"),
    JOB_ID("job.id"),
    JOB_TITLE("job.title"),
    JOB_SEEKER_ID("jobSeeker.id"),
    JO<PERSON>_SEEKER_NAME("jobSeeker.name"),
    RESUME_ID("resume.id"),
    STATUS("status");
    
    private final String fieldName;
    
    JobApplicationFilterField(String fieldName) {
        this.fieldName = fieldName;
    }
    
    public String fieldName() {
        return fieldName;
    }
} 