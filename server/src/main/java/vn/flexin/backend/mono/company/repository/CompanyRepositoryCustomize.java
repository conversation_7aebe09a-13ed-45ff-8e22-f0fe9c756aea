package vn.flexin.backend.mono.company.repository;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.*;
import org.hibernate.Hibernate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import vn.flexin.backend.mono.company.dto.company.SearchAdminBranchByCompanyRequest;
import vn.flexin.backend.mono.company.dto.company.SearchAdminCompanyRequest;
import vn.flexin.backend.mono.company.dto.company.SearchAdminTeamMemberByCompanyRequest;
import vn.flexin.backend.mono.company.dto.company.SearchCompanyRequest;
import vn.flexin.backend.mono.company.entity.Branch;
import vn.flexin.backend.mono.company.entity.Company;
import vn.flexin.backend.mono.company.entity.Staff;

import java.util.ArrayList;
import java.util.List;

@Repository
public class CompanyRepositoryCustomize {
    @PersistenceContext
    private EntityManager entityManager;

    public Page<Company> searchCompanies(SearchCompanyRequest searchRequest) {

        // Pagination
        int page = (searchRequest.getPage() != null) ? searchRequest.getPage()-1 : 0;
        int limit = (searchRequest.getLimit() != null) ? searchRequest.getLimit() : 10;
        Pageable pageable = PageRequest.of(page, limit);

        List<Company> results = getListCompanies(searchRequest, pageable);
        long totalCount = countTotalCompanies(searchRequest);

        return new PageImpl<>(results, pageable, totalCount);
    }

    private long countTotalCompanies(SearchCompanyRequest searchRequest) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();

        CriteriaQuery<Long> countQuery = cb.createQuery(Long.class);
        Root<Company> countRoot = countQuery.from(Company.class);

        List<Predicate> predicates = buildSearchPredicate(cb,searchRequest, countRoot);

        countQuery.select(cb.count(countRoot));
        countQuery.where(cb.and(predicates.toArray(new Predicate[0])));
        return entityManager.createQuery(countQuery).getSingleResult();
    }

    private List<Company> getListCompanies(SearchCompanyRequest searchRequest, Pageable pageable ) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Company> query = cb.createQuery(Company.class);
        Root<Company> company = query.from(Company.class);

        company.fetch("user", JoinType.INNER);
        company.fetch("logo", JoinType.LEFT);
        company.fetch("coverImage", JoinType.LEFT);

        List<Predicate> predicates = buildSearchPredicate(cb,searchRequest, company);

        // Combine predicates
        query.where(cb.and(predicates.toArray(new Predicate[0])));

        // Sorting
        if (searchRequest.getSortBy() != null && !searchRequest.getSortOrder().isEmpty()) {
            if ("asc".equalsIgnoreCase(searchRequest.getSortOrder())) {
                query.orderBy(cb.asc(company.get(searchRequest.getSortBy())));
            } else {
                query.orderBy(cb.desc(company.get(searchRequest.getSortBy())));
            }
        }

        TypedQuery<Company> typedQuery = entityManager.createQuery(query.distinct(true));
        typedQuery.setFirstResult((int) pageable.getOffset());
        typedQuery.setMaxResults(pageable.getPageSize());

        // Execute query
        List<Company> results = typedQuery.getResultList();
        for (Company item : results) {
            Hibernate.initialize(item.getBranches()); // Initialize branches separately if needed
            Hibernate.initialize(item.getGalleryImages()); // Initialize branches separately if needed
        }
        return results;
    }

    private List<Predicate> buildSearchPredicate(CriteriaBuilder cb, SearchCompanyRequest searchRequest, Root<Company> company){
        List<Predicate> predicates = new ArrayList<>();

        // Filter by userId
        if (searchRequest.getUserId() != null) {
            predicates.add(cb.equal(company.get("user").get("id"), searchRequest.getUserId()));
        }

        // Filter by industry
        if (searchRequest.getIndustry() != null && !searchRequest.getIndustry().isEmpty()) {
            predicates.add(cb.equal(company.get("industry"), searchRequest.getIndustry()));
        }

        // Search by query in name or description
        if (searchRequest.getQuery() != null && !searchRequest.getQuery().isEmpty()) {
            String likePattern = "%" + searchRequest.getQuery().toLowerCase() + "%";
            Predicate namePredicate = cb.like(cb.lower(company.get("name")), likePattern);
            Predicate descriptionPredicate = cb.like(cb.lower(company.get("description")), likePattern);
            predicates.add(cb.or(namePredicate, descriptionPredicate));
        }

        return predicates;
    }

    @Transactional(readOnly = true)
    public List<Company> searchCompanyAdmin(SearchAdminCompanyRequest request) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Company> query = cb.createQuery(Company.class);
        Root<Company> companyRoot = query.from(Company.class);

        List<Predicate> predicates = new ArrayList<>();

        buildSearchAdminCompanyPredicates(request, predicates, companyRoot, cb);

        // Build the final query
        query.select(companyRoot).where(predicates.toArray(new Predicate[0]));

        // Pagination
        if (request.getPage() != null && request.getLimit() != null) {
            int offset = (request.getPage()) * request.getLimit();
            return entityManager.createQuery(query)
                    .setFirstResult(offset)
                    .setMaxResults(request.getLimit())
                    .getResultList();
        } else {
            return entityManager.createQuery(query).getResultList();
        }
    }

    private void buildSearchAdminCompanyPredicates(SearchAdminCompanyRequest request, List<Predicate> predicates,
                                                Root<Company> companyRoot, CriteriaBuilder cb) {
        if (request.getKeyword() != null && !request.getKeyword().trim().isEmpty()) {
            Predicate titlePredicate = cb.like(companyRoot.get("title"), "%" + request.getKeyword() + "%");
            Predicate desPredicate = cb.like(companyRoot.get("description"), "%" + request.getKeyword() + "%");
            titlePredicate = cb.or(titlePredicate, desPredicate);
            predicates.add(titlePredicate);
        }

        if (request.getIndustry() != null) {
            predicates.add(companyRoot.get("industry").in(request.getIndustry()));
        }

        if (request.getIsVerified() != null) {
            predicates.add(cb.equal(companyRoot.get("isIndividual"), request.getIsVerified()));
        }

        if (request.getIsVerified() != null) {
            predicates.add(cb.equal(companyRoot.get("isVerified"), request.getIsVerified()));
        }
        
    }

    @Transactional(readOnly = true)
    public long countAdminCompanies(SearchAdminCompanyRequest request) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Long> countQuery = cb.createQuery(Long.class);
        Root<Company> companyRoot = countQuery.from(Company.class);

        // Using the count function
        countQuery.select(cb.count(companyRoot));

        List<Predicate> predicates = new ArrayList<>();

        buildSearchAdminCompanyPredicates(request, predicates, companyRoot, cb);

        // Apply predicates to the count query
        countQuery.where(predicates.toArray(new Predicate[0]));

        return entityManager.createQuery(countQuery).getSingleResult();
    }

    @Transactional(readOnly = true)
    public List<Branch> searchBranchAdmin(Long id, SearchAdminBranchByCompanyRequest request) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Branch> query = cb.createQuery(Branch.class);
        Root<Branch> branchRoot = query.from(Branch.class);

        List<Predicate> predicates = new ArrayList<>();

        buildSearchAdminBranchPredicates(id, request, predicates, branchRoot, cb);

        // Build the final query
        query.select(branchRoot).where(predicates.toArray(new Predicate[0]));

        // Pagination
        if (request.getPage() != null && request.getPageSize() != null) {
            int offset = (request.getPage() - 1) * request.getPageSize();
            return entityManager.createQuery(query)
                    .setFirstResult(offset)
                    .setMaxResults(request.getPageSize())
                    .getResultList();
        } else {
            return entityManager.createQuery(query).getResultList();
        }
    }

    private void buildSearchAdminBranchPredicates(Long id, SearchAdminBranchByCompanyRequest request,
                                                  List<Predicate> predicates,
                                                   Root<Branch> branchRoot, CriteriaBuilder cb) {
        if (request.getSearch() != null && !request.getSearch().trim().isEmpty()) {
            Predicate titlePredicate = cb.like(branchRoot.get("title"), "%" + request.getSearch() + "%");
            Predicate desPredicate = cb.like(branchRoot.get("description"), "%" + request.getSearch() + "%");
            titlePredicate = cb.or(titlePredicate, desPredicate);
            predicates.add(titlePredicate);
        }
    }

    @Transactional(readOnly = true)
    public long countAdminBranch(Long id, SearchAdminBranchByCompanyRequest request) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Long> countQuery = cb.createQuery(Long.class);
        Root<Branch> branchRoot = countQuery.from(Branch.class);

        // Using the count function
        countQuery.select(cb.count(branchRoot));

        List<Predicate> predicates = new ArrayList<>();

        buildSearchAdminBranchPredicates(id, request, predicates, branchRoot, cb);

        // Apply predicates to the count query
        countQuery.where(predicates.toArray(new Predicate[0]));

        return entityManager.createQuery(countQuery).getSingleResult();
    }

    @Transactional(readOnly = true)
    public List<Staff> searchStaffAdmin(Long id, SearchAdminTeamMemberByCompanyRequest request) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Staff> query = cb.createQuery(Staff.class);
        Root<Staff> staffRoot = query.from(Staff.class);

        List<Predicate> predicates = new ArrayList<>();

        buildSearchAdminStaffPredicates(id, request, predicates, staffRoot, cb);

        // Build the final query
        query.select(staffRoot).where(predicates.toArray(new Predicate[0]));

        // Pagination
        if (request.getPage() != null && request.getPageSize() != null) {
            int offset = (request.getPage() - 1) * request.getPageSize();
            return entityManager.createQuery(query)
                    .setFirstResult(offset)
                    .setMaxResults(request.getPageSize())
                    .getResultList();
        } else {
            return entityManager.createQuery(query).getResultList();
        }
    }

    private void buildSearchAdminStaffPredicates(Long id, SearchAdminTeamMemberByCompanyRequest request,
                                                  List<Predicate> predicates,
                                                  Root<Staff> root, CriteriaBuilder cb) {
        if (request.getSearch() != null && !request.getSearch().trim().isEmpty()) {
            Predicate titlePredicate = cb.like(root.get("title"), "%" + request.getSearch() + "%");
            Predicate desPredicate = cb.like(root.get("description"), "%" + request.getSearch() + "%");
            titlePredicate = cb.or(titlePredicate, desPredicate);
            predicates.add(titlePredicate);
        }
    }

    @Transactional(readOnly = true)
    public long countAdminStaff(Long id, SearchAdminTeamMemberByCompanyRequest request) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Long> countQuery = cb.createQuery(Long.class);
        Root<Staff> root = countQuery.from(Staff.class);

        // Using the count function
        countQuery.select(cb.count(root));

        List<Predicate> predicates = new ArrayList<>();

        buildSearchAdminStaffPredicates(id, request, predicates, root, cb);

        // Apply predicates to the count query
        countQuery.where(predicates.toArray(new Predicate[0]));

        return entityManager.createQuery(countQuery).getSingleResult();
    }
}
