package vn.flexin.backend.mono.company.service.mobile.impl;

import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import vn.flexin.backend.mono.address.service.AddressService;
import vn.flexin.backend.mono.common.dto.CreateObjectResponse;
import vn.flexin.backend.mono.common.exception.BadRequestException;
import vn.flexin.backend.mono.common.exception.ResourceNotFoundException;
import vn.flexin.backend.mono.common.util.ModelMapperUtils;
import vn.flexin.backend.mono.company.dto.branch.BranchResponse;
import vn.flexin.backend.mono.company.dto.branch.CreateBranchRequest;
import vn.flexin.backend.mono.company.dto.staff.InviteStaffRequest;
import vn.flexin.backend.mono.company.dto.staff.UpdateStaffRequest;
import vn.flexin.backend.mono.company.entity.Branch;
import vn.flexin.backend.mono.company.entity.Company;
import vn.flexin.backend.mono.company.entity.Staff;
import vn.flexin.backend.mono.company.repository.BranchRepository;
import vn.flexin.backend.mono.company.service.mobile.BranchService;
import vn.flexin.backend.mono.company.service.mobile.StaffService;
import vn.flexin.backend.mono.file.dto.FileDto;
import vn.flexin.backend.mono.file.entity.File;
import vn.flexin.backend.mono.file.repository.FileRepository;
import vn.flexin.backend.mono.file.service.FileService;
import vn.flexin.backend.mono.user.dto.BasicUserInfoResponse;
import vn.flexin.backend.mono.user.entity.User;
import vn.flexin.backend.mono.user.service.UserService;

import java.util.*;

@Service
@Transactional
@AllArgsConstructor
public class BranchServiceImpl implements BranchService {
    private final BranchRepository branchRepository;
    private final StaffService staffService;
    private final FileService fileService;
    private final FileRepository fileRepository;
    private final UserService userService;
    private AddressService addressService;

    @Override
    public CreateObjectResponse createBranch(CreateBranchRequest request, Company company) {
        Branch branch = ModelMapperUtils.toObject(request, Branch.class);
        if (!CollectionUtils.isEmpty(request.getGalleryImages())) {
            List<Long> fileIds = request.getGalleryImages().stream().map(FileDto::getId).toList();
            List<File> files = fileService.getByIds(fileIds, fileRepository);
            branch.setGalleryImages(files);
        }
        if (request.getManagerId() != null) {
            User user = userService.getByIdNullable(request.getManagerId());
            branch.setManager(user);
        }
        branch.setActive(true);
        branch.setCompany(company);
        branch.setAddress(addressService.createAddress(request.getAddress()));
        branch = save(branch);

        if (branch.isDefault()) {
            branchRepository.updateDefaultOtherBranch(request.getCompanyId(), branch.getId());
        }
        return new CreateObjectResponse(branch.getId());
    }

    @Override
    public void updateBranch(CreateBranchRequest request) {
        Branch branch = getById(request.getId());
        verifyBranchBelongToCompany(branch.getCompany().getId(), request.getCompanyId());
        branch.setName(request.getName());
        branch.setAddress(null);
        branch.setDefault(request.isDefault());
        branch.setAdditionalData(request.getAdditionalData());
        if (!CollectionUtils.isEmpty(request.getGalleryImages())) {
            List<Long> fileIds = request.getGalleryImages().stream().map(FileDto::getId).toList();
            List<File> files = fileService.getByIds(fileIds, fileRepository);
            branch.setGalleryImages(files);
        }
        branch.setAddress(addressService.updateAddress(request.getAddress()));
        branch = save(branch);

        if (branch.isDefault()) {
            branchRepository.updateDefaultOtherBranch(request.getCompanyId(), branch.getId());
        }
    }

    private void verifyBranchBelongToCompany(Long originCompanyId, Long companyId) {
        if (!Objects.equals(originCompanyId, companyId)) {
            throw new ResourceNotFoundException("Branch not found");
        }
    }

    @Override
    public BranchResponse getDetailBranch(Long branchId, Long companyId) {
        Branch branch = getById(branchId);
        verifyBranchBelongToCompany(branch.getCompany().getId(), companyId);
        List<Staff> staffs = staffService.getStaffByBranchIds(Collections.singletonList(branchId));
        return toBranchResponse(branch, staffs);
    }

    @Override
    public void deleteBranch(Long branchId, Long companyId) {
        Branch branch = getById(branchId);
        verifyBranchBelongToCompany(branch.getCompany().getId(), companyId);
        staffService.deleteByBranchId(branchId);
        branchRepository.delete(branch);
    }

    private BranchResponse toBranchResponse(Branch branch, List<Staff> staffs) {
        BranchResponse response = ModelMapperUtils.toObject(branch, BranchResponse.class);
        if (CollectionUtils.isEmpty(staffs)) {
            response.setStaffCount(0);
            response.setManager(null);
        } else {
            staffs.stream().filter(Staff::isManager).map(Staff::getUser).findFirst()
                    .ifPresent(manager -> response.setManager(new BasicUserInfoResponse(manager)));
            response.setStaffCount(staffs.size());
        }
        if(CollectionUtils.isEmpty(branch.getGalleryImages())){
            response.setGalleryImages(Collections.emptyList());
            return response;
        }
        List<FileDto> galleryImages = branch.getGalleryImages().stream().map(FileDto::new).toList();
        response.setGalleryImages(galleryImages);
        return response;
    }

    @Override
    public List<BranchResponse> getListCompanyBranches(Long companyId) {
        List<Branch> branches = branchRepository.findByCompanyId(companyId);
        return toBranchResponseList(branches);
    }

    @Override
    public List<BranchResponse> toBranchResponseList(List<Branch> branches) {
        List<Long> branchIds = branches.stream().map(Branch::getId).toList();
        List<Staff> allBranchStaffs = staffService.getStaffByBranchIds(branchIds);
        Map<Long, List<Staff>> branchListStaffMap = new HashMap<>();
        for (Long branchId : branchIds) {
            List<Staff> staffs = allBranchStaffs.stream()
                    .filter(staff -> Objects.equals(staff.getBranch().getId(), branchId)).toList();
            branchListStaffMap.put(branchId, staffs);
        }
        return branches.stream().map(branch -> {
            List<Staff> staffs = branchListStaffMap.get(branch.getId());
            return toBranchResponse(branch, staffs);
        }).toList();
    }

    @Override
    public Branch save(Branch branch) {
        return branchRepository.save(branch);
    }

    @Override
    public List<Branch> getByCompanyId(Long companyId) {
        return branchRepository.findByCompanyId(companyId);
    }

    @Override
    public Branch getById(Long branchId) {
        return branchRepository.findById(branchId).orElseThrow(() -> new ResourceNotFoundException("Branch not found"));
    }

    @Override
    public void updateBranchStaffStatus(Long branchId, Long companyId, String staffId, UpdateStaffRequest request) {
        Branch branch = getById(branchId);
        verifyBranchBelongToCompany(branch.getCompany().getId(), companyId);
        Staff staff = getStaffByBranch(branchId, staffId);
        staff.setActive(request.isActive);
        staffService.save(staff);
    }

    private Staff getStaffByBranch(Long branchId, String staffId) {
        Staff staff = staffService.getByBranchIdAndUserUlid(branchId, staffId);
        if (!Objects.equals(staff.getBranch().getId(), branchId)) {
            throw new ResourceNotFoundException("Staff not found");
        }
        return staff;
    }

    @Override
    public void removeBranchStaff(Long branchId, String staffId, Long companyId) {
        Branch branch = getById(branchId);
        verifyBranchBelongToCompany(branch.getCompany().getId(), companyId);
        Staff staff = getStaffByBranch(branchId, staffId);
        staffService.deleteStaff(staff);
    }

    @Override
    public Long inviteUser(Branch branch, Long companyId, User invitedUser, InviteStaffRequest request) {
        verifyBranchBelongToCompany(branch.getCompany().getId(), companyId);
        Staff staff = createStaff(request, branch, invitedUser);
        return staff.getId();
    }

    private Staff createStaff(InviteStaffRequest request, Branch branch, User inviteUser) {
        Staff existedStaff = staffService.getByBranchIdAndUserUlid(branch.getId(), request.getUserId());
        if (existedStaff != null) {
            if (existedStaff.isPending()) {
                throw new BadRequestException("User is already invited");
            }
            throw new BadRequestException("User is already in company");
        }

        Staff newStaff = new Staff(branch, inviteUser, request.getPosition());
        return staffService.save(newStaff);
    }

}
