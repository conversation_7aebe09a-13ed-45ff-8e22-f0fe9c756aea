package vn.flexin.backend.mono.company.entity;

import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;
import org.hibernate.annotations.Type;
import vn.flexin.backend.mono.address.entity.Address;
import vn.flexin.backend.mono.common.entity.AbstractAuditingEntity;
import vn.flexin.backend.mono.file.entity.File;
import vn.flexin.backend.mono.user.entity.User;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Setter
@Getter
@Entity
@AllArgsConstructor
@NoArgsConstructor
@FieldNameConstants
@Table(name = "t_companies")
public class Company extends AbstractAuditingEntity<Long> implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "user_id")
    private User user;

    private String name;

    private String industry;

    private String description;

    @OneToOne
    @JoinColumn(name = "address_id")
    private Address address;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "logo_id")
    private File logo;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "cover_image_id")
    private File coverImage;

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
            name = "t_company_gallery_images",
            joinColumns = @JoinColumn(name = "company_id"),
            inverseJoinColumns = @JoinColumn(name = "file_id")
    )
    @ToString.Exclude
    private List<File> galleryImages;

    private String websiteUrl;

    private String email;

    private String phoneNumber;

    private String foundedYear;

    private boolean isVerified;

    private boolean isIndividual;

    @ToString.Exclude
    @OneToMany(fetch = FetchType.LAZY, mappedBy = "company", cascade = {CascadeType.DETACH, CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH})
    private List<Branch> branches;

    @Type(JsonType.class)
    @Column(columnDefinition = "jsonb")
    private Map<String, Object> additionalData;

}
