package vn.flexin.backend.mono.resume.controller.admin;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.flexin.backend.mono.common.dto.*;
import vn.flexin.backend.mono.resume.dto.*;
import vn.flexin.backend.mono.resume.service.AdminResumeService;
import vn.flexin.backend.mono.user.dto.ContactInfoDto;
import vn.flexin.backend.mono.user.dto.PartTimePreferenceDto;

import java.util.List;

@RestController
@RequestMapping("/v1/admin/resumes")
@RequiredArgsConstructor
@Slf4j
public class AdminResumeControllerImpl implements AdminResumeController {

    private final AdminResumeService adminResumeService;

    @Override
    public ResponseEntity<ApiResponseDto<List<SearchResumeResponse>>> getAllResumes() {
        log.info("Admin retrieving all resumes");
        List<SearchResumeResponse> resumes = adminResumeService.getAllResumes();
        return new ResponseEntity<>(ApiResponseDto.success("Resumes retrieved successfully", resumes), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<PaginationApiResponseDto<List<SearchResumeResponse>>> searchResumes(@Valid ResumeFilter resumeFilter) {
        Pair<PaginationResponse, List<SearchResumeResponse>> response = adminResumeService.searchResumes(resumeFilter);
        return new ResponseEntity<>(PaginationApiResponseDto.success("Resumes retrieved successfully", response.getRight(), response.getLeft()), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponseDto<ResumeDto>> getResumeById(Long id) {
        log.info("Admin retrieving resume with ID: {}", id);
        ResumeDto resume = adminResumeService.getResumeByIdFull(id);
        return new ResponseEntity<>(ApiResponseDto.success("Resume retrieved successfully", resume), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponseDto<List<ResumeDto>>> getResumesByUserId(Long userId) {
        log.info("Admin retrieving resumes for user ID: {}", userId);
        List<ResumeDto> resumes = adminResumeService.getResumesByUserIdFull(userId);
        return new ResponseEntity<>(ApiResponseDto.success("Resumes retrieved successfully", resumes), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponseDto<CreateObjectResponse>> createResume(@Valid ResumeDto resumeDto) {
        log.info("Admin creating new resume: {}", resumeDto);
        Long createdResume = adminResumeService.adminCreateResume(resumeDto);
        return new ResponseEntity<>(ApiResponseDto.success("Resume created successfully", new CreateObjectResponse(createdResume)), HttpStatus.CREATED);
    }

    @Override
    public ResponseEntity<ApiResponseDto<ResumeDto>> updateResume(Long id, @Valid ResumeDto resumeDto) {
        log.info("Admin updating resume with ID: {}", id);
        ResumeDto updatedResume = adminResumeService.updateResume(id, resumeDto);
        return new ResponseEntity<>(ApiResponseDto.success("Resume updated successfully", updatedResume), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> deleteResume(Long id) {
        log.info("Admin deleting resume with ID: {}", id);
        adminResumeService.deleteResume(id);
        return new ResponseEntity<>(ApiResponseDto.success("Resume deleted successfully", Boolean.TRUE), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponseDto<WorkExperienceDto>> addWorkExperience(Long resumeId, @Valid WorkExperienceDto workExperienceDto) {
        log.info("Admin adding work experience to resume ID: {}", resumeId);
        WorkExperienceDto addedWorkExperience = adminResumeService.addWorkExperience(resumeId, workExperienceDto);
        return new ResponseEntity<>(ApiResponseDto.success("Work experience added successfully", addedWorkExperience), HttpStatus.CREATED);
    }

    @Override
    public ResponseEntity<ApiResponseDto<WorkExperienceDto>> updateWorkExperience(Long id, @Valid WorkExperienceDto workExperienceDto) {
        log.info("Admin updating work experience with ID: {}", id);
        WorkExperienceDto updatedWorkExperience = adminResumeService.updateWorkExperience(id, workExperienceDto);
        return new ResponseEntity<>(ApiResponseDto.success("Work experience updated successfully", updatedWorkExperience), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> deleteWorkExperience(Long id) {
        log.info("Admin deleting work experience with ID: {}", id);
        adminResumeService.deleteWorkExperience(id);
        return new ResponseEntity<>(ApiResponseDto.success("Work experience deleted successfully", Boolean.TRUE), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponseDto<EducationDto>> addEducation(Long resumeId, @Valid EducationDto educationDto) {
        log.info("Admin adding education to resume ID: {}", resumeId);
        EducationDto addedEducation = adminResumeService.addEducation(resumeId, educationDto);
        return new ResponseEntity<>(ApiResponseDto.success("Education added successfully", addedEducation), HttpStatus.CREATED);
    }

    @Override
    public ResponseEntity<ApiResponseDto<EducationDto>> updateEducation(Long id, @Valid EducationDto educationDto) {
        log.info("Admin updating education with ID: {}", id);
        EducationDto updatedEducation = adminResumeService.updateEducation(id, educationDto);
        return new ResponseEntity<>(ApiResponseDto.success("Education updated successfully", updatedEducation), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> deleteEducation(Long id) {
        log.info("Admin deleting education with ID: {}", id);
        adminResumeService.deleteEducation(id);
        return new ResponseEntity<>(ApiResponseDto.success("Education deleted successfully", Boolean.TRUE), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponseDto<ContactInfoDto>> updateContactInfo(Long resumeId, @Valid ContactInfoDto contactInfoDto) {
        log.info("Admin updating contact info for resume ID: {}", resumeId);
        ContactInfoDto updatedContactInfo = adminResumeService.updateContactInfo(resumeId, contactInfoDto);
        return new ResponseEntity<>(ApiResponseDto.success("Contact info updated successfully", updatedContactInfo), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponseDto<PartTimePreferenceDto>> updatePartTimePreference(Long resumeId, @Valid PartTimePreferenceDto partTimePreferenceDto) {
        log.info("Admin updating part time preference for resume ID: {}", resumeId);
        PartTimePreferenceDto updatedPartTimePreference = adminResumeService.updatePartTimePreference(resumeId, partTimePreferenceDto);
        return new ResponseEntity<>(ApiResponseDto.success("Part time preference updated successfully", updatedPartTimePreference), HttpStatus.OK);
    }

    @Override
    public ResponseEntity<ApiResponseDto<Boolean>> updateIsActive(Long id, boolean isActive) {
        adminResumeService.updateIsActive(id, isActive);
        return new ResponseEntity<>(ApiResponseDto.success("isActive updated successfully", true), HttpStatus.OK);
    }
} 