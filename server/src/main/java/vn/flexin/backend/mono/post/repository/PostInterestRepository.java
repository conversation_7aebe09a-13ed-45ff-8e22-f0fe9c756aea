package vn.flexin.backend.mono.post.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import vn.flexin.backend.mono.post.entity.PostInterest;

import java.util.Optional;

public interface PostInterestRepository extends JpaRepository<PostInterest, Long> {
    @Query(value = """
        SELECT pi
        FROM PostInterest pi
        WHERE pi.user.id = :userId
        AND pi.post.id = :postId
    """)
    Optional<PostInterest> findPostInterested(@Param("userId") Long userId, @Param("postId") Long postId);

    @Modifying
    @Query("DELETE FROM PostInterest pi WHERE pi.post.id = :id")
    void deleteByPostId(@Param("id")Long id);
}
