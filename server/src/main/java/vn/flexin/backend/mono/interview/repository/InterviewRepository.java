package vn.flexin.backend.mono.interview.repository;

import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import vn.flexin.backend.mono.application.dto.InterviewStatusCount;
import vn.flexin.backend.mono.interview.entity.Interview;
import vn.flexin.backend.mono.common.repository.JpaSpecificationRepository;

import java.util.List;

@Repository
public interface InterviewRepository extends JpaSpecificationRepository<Interview, Long> {
    @Query("SELECT i.status as status, COUNT(i) as count FROM Interview i GROUP BY i.status")
    List<InterviewStatusCount> getInterviewCountsByStatus();

    @Query("SELECT i.status as status, COUNT(i) as count FROM Interview i WHERE i.employer.id = :employerId GROUP BY i.status")
    List<InterviewStatusCount> getInterviewCountsByStatusAndEmployerId(Long employerId);

    @Query("SELECT i.status as status, COUNT(i) as count FROM Interview i WHERE i.jobSeeker.id = :jobSeekerId GROUP BY i.status")
    List<InterviewStatusCount> getInterviewCountsByStatusAndJobSeekerId(Long jobSeekerId);
} 