package vn.flexin.backend.mono.company.dto.branch;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.jpa.domain.Specification;
import vn.flexin.backend.mono.common.dto.BaseFilter;
import vn.flexin.backend.mono.common.repository.param.Condition;
import vn.flexin.backend.mono.common.repository.param.Join;
import vn.flexin.backend.mono.common.repository.param.Operator;
import vn.flexin.backend.mono.common.repository.param.Where;
import vn.flexin.backend.mono.common.util.SpecificationUtil;
import vn.flexin.backend.mono.company.entity.Branch;
import vn.flexin.backend.mono.company.entity.Company;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
public class BranchFilter extends BaseFilter<Branch> {
    private Long companyId;
    private String keyword;
    private Boolean isDefault;
    private String name;
    @Override
    public Specification<Branch> toSpecification() {
        Condition condition = new Condition();
        
        if(isDefault != null) {
            condition.append(new Where(Branch.Fields.isDefault, isDefault));
        }

        if(name != null && !name.isEmpty()) {
            condition.append(new Where(Branch.Fields.name, Operator.LIKE_IGNORE_CASE, name));
        }

        if(companyId != null) {
            condition.append(new Join(Branch.Fields.company, List.of(new Where(Company.Fields.id, companyId))));
        }
        if (keyword != null && !keyword.isEmpty()) {
            condition.append(new Where(Branch.Fields.name, Operator.LIKE, keyword));
        }

        return SpecificationUtil.bySearchQuery(condition);
    }
}
