package vn.flexin.backend.mono.resume.permissions;

import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.Getter;
import vn.flexin.backend.mono.common.enums.AppStatus;
import vn.flexin.backend.mono.common.exception.ResponseAppStatusException;

import java.util.Arrays;

@Getter
public enum ResumePermissions {
    RESUME_READ("resume_read"),
    RESUME_CREATE("resume_create"),
    RESUME_UPDATE("resume_update"),
    RESUME_DELETE("resume_delete");

    private final String value;

    ResumePermissions(String value) {
        this.value = value;
    }

    public static String getValues() {
        return String.join(", ", Arrays.stream(values()).map(x -> x.name().toLowerCase()).toList());
    }

    @JsonCreator
    public static ResumePermissions fromString(String value) {
        try {
            return valueOf(value.toUpperCase());
        } catch (Exception ex) {
            throw new ResponseAppStatusException(AppStatus.BAD_REQUEST, "Resume permissions type must be any of [" + getValues() + "]");
        }
    }
} 