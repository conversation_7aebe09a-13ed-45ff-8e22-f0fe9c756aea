package vn.flexin.backend.mono.company.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import vn.flexin.backend.mono.common.repository.JpaSpecificationRepository;
import vn.flexin.backend.mono.company.entity.Branch;

import java.util.List;

public interface BranchRepository extends JpaSpecificationRepository<Branch, Long> {
    List<Branch> findByCompanyId(Long companyId);

    @Modifying
    @Query("UPDATE Branch branch SET branch.isDefault = FALSE WHERE branch.id <> :id AND branch.company.id = :companyId")
    void updateDefaultOtherBranch(@Param("companyId") Long companyId,
                                  @Param("id")Long id);

    @Modifying
    @Query("DELETE FROM Branch b WHERE b.company.id = :id")
    void deleteByCompanyId(@Param("id")Long id);

    boolean existsByName(String name);
    boolean existsByAddress_Id(Long addressId);
}
