package vn.flexin.backend.mono.home.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.flexin.backend.mono.role.dto.response.RoleResponse;

import java.time.LocalDateTime;
import java.util.Set;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OutstandingCandidateDto {
    private Long id;

    @Size(max = 26)
    private String ulid;
    
    @NotBlank(message = "Name is required")
    @Size(max = 50, message = "Name must be less than 50 characters")
    private String name;

    @NotBlank(message = "Role is required")
    private String role;

    private Set<RoleResponse> roles;

    private Set<Long> roleIds;

    private String phoneNumber;
    
    private String profilePicture;
    
    private boolean isActive = true;

    private LocalDateTime createdAt;

    private Double minHourlyRate;
} 