package vn.flexin.backend.mono.contract.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.common.dto.PaginationApiResponseDto;
import vn.flexin.backend.mono.contract.dto.*;

import java.util.List;

@Tag(name = "Admin Contract APIs", description = "Admin contract management endpoints")
@SecurityRequirement(name = "bearerAuth")
public interface AdminContractController {

    @Operation(summary = "Get all contracts", description = "Retrieve a list of all contracts (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Contracts retrieved successfully",
                    content = @Content(schema = @Schema(implementation = ContractDetailResponse.class))),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user")
    })
    @GetMapping
    @PreAuthorize("hasRole('ADMIN')")
    ResponseEntity<ApiResponseDto<List<ContractDetailResponse>>> getAllContracts();

    @Operation(summary = "Search and filter contracts", description = "Search and filter contracts with pagination and sorting (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Contracts retrieved successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user")
    })
    @PostMapping("/search")
    @PreAuthorize("hasRole('ADMIN')")
    ResponseEntity<PaginationApiResponseDto<List<ContractResponse>>> searchContracts(@Valid @RequestBody ContractFilter filter);

    @Operation(summary = "Get contract by ID", description = "Retrieve a contract by its ID (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Contract retrieved successfully",
                    content = @Content(schema = @Schema(implementation = ContractDetailResponse.class))),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user"),
            @ApiResponse(responseCode = "404", description = "Contract not found")
    })
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    ResponseEntity<ApiResponseDto<ContractDetailResponse>> getContractById(@PathVariable Long id);

    @Operation(summary = "Get contracts by employer ID", description = "Retrieve contracts for a specific employer (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Contracts retrieved successfully",
                    content = @Content(schema = @Schema(implementation = ContractDetailResponse.class))),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user")
    })
    @GetMapping("/employer/{employerId}")
    @PreAuthorize("hasRole('ADMIN')")
    ResponseEntity<ApiResponseDto<List<ContractDetailResponse>>> getContractsByEmployerId(@PathVariable Long employerId);

    @Operation(summary = "Get contracts by freelancer ID", description = "Retrieve contracts for a specific freelancer (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Contracts retrieved successfully",
                    content = @Content(schema = @Schema(implementation = ContractDetailResponse.class))),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user")
    })
    @GetMapping("/freelancer/{freelancerId}")
    @PreAuthorize("hasRole('ADMIN')")
    ResponseEntity<ApiResponseDto<List<ContractDetailResponse>>> getContractsByFreelancerId(@PathVariable Long freelancerId);

    @Operation(summary = "Create contract", description = "Create a new contract (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Contract created successfully",
                    content = @Content(schema = @Schema(implementation = ContractDetailResponse.class))),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user")
    })
    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    ResponseEntity<ApiResponseDto<ContractDetailResponse>> createContract(@Valid @RequestBody ContractRequet requet);

    @Operation(summary = "Update contract", description = "Update an existing contract (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Contract updated successfully",
                    content = @Content(schema = @Schema(implementation = ContractDetailResponse.class))),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user"),
            @ApiResponse(responseCode = "404", description = "Contract not found")
    })
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    ResponseEntity<ApiResponseDto<ContractDetailResponse>> updateContract(
            @PathVariable Long id, @Valid @RequestBody ContractRequet requet);

    @Operation(summary = "Update contract status", description = "Update the status of a contract (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Contract status updated successfully",
                    content = @Content(schema = @Schema(implementation = ContractDetailResponse.class))),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user"),
            @ApiResponse(responseCode = "404", description = "Contract not found")
    })
    @PatchMapping("/{id}/status")
    @PreAuthorize("hasRole('ADMIN')")
    ResponseEntity<ApiResponseDto<ContractDetailResponse>> updateContractStatus(
            @PathVariable Long id, @RequestParam String status, @RequestParam(required = false) String adminNotes);

    @Operation(summary = "Delete contract", description = "Delete a contract (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Contract deleted successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user"),
            @ApiResponse(responseCode = "404", description = "Contract not found")
    })
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    ResponseEntity<ApiResponseDto<Boolean>> deleteContract(@PathVariable Long id);

    @Operation(summary = "Search and filter TimeEntries", description = "Search and filter timeEntries with pagination and sorting (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "TimeEntries retrieved successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user")
    })
    @GetMapping("/{contractId}/time-entries")
    @PreAuthorize("hasRole('ADMIN')")
    ResponseEntity<PaginationApiResponseDto<List<TimeEntryResponse>>> searchTimeEntries(@PathVariable("contractId") Long contractId, TimeEntryFilter filter);

    @Operation(summary = "Search and filter payments", description = "Search and filter payments with pagination and sorting (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "payments retrieved successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user")
    })
    @GetMapping("/{contractId}/payments")
    @PreAuthorize("hasRole('ADMIN')")
    ResponseEntity<PaginationApiResponseDto<List<PaymentRecordResponse>>> searchPaymentRecord(@PathVariable("contractId") Long contractId, PaymentRecordFilter filter);

    @Operation(summary = "Create TimeEntry", description = "Create a new timeEntry (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "TimeEntry created successfully",
                    content = @Content(schema = @Schema(implementation = TimeEntryResponse.class))),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user")
    })
    @PostMapping("/{contractId}/time-entries")
    @PreAuthorize("hasRole('ADMIN')")
    ResponseEntity<ApiResponseDto<TimeEntryResponse>> createTimeEntry(@Valid @RequestBody TimeEntryRequest request);

    @Operation(summary = "Create payments", description = "Create a new payments (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "payments created successfully",
                    content = @Content(schema = @Schema(implementation = PaymentRecordResponse.class))),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user")
    })
    @PostMapping("/{contractId}/payments")
    @PreAuthorize("hasRole('ADMIN')")
    ResponseEntity<ApiResponseDto<PaymentRecordResponse>> createPaymentRecord(@Validated(PaymentRecordRequest.create.class) PaymentRecordRequest request);

    @Operation(summary = "Update timeEntry status", description = "Update the status of a timeEntry (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "TimeEntry status updated successfully",
                    content = @Content(schema = @Schema(implementation = TimeEntryResponse.class))),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user"),
            @ApiResponse(responseCode = "404", description = "Contract not found")
    })
    @PatchMapping("/{contractId}/time-entries/{entryId}/status")
    @PreAuthorize("hasRole('ADMIN')")
    ResponseEntity<ApiResponseDto<TimeEntryResponse>> updateTimeEntryStatus(@PathVariable Long contractId, @PathVariable Long entryId, @RequestParam String status);

    @Operation(summary = "Update paymentRecord status", description = "Update the status of a paymentRecord (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "paymentRecord status updated successfully",
                    content = @Content(schema = @Schema(implementation = PaymentRecordResponse.class))),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user"),
            @ApiResponse(responseCode = "404", description = "Contract not found")
    })
    @PatchMapping("/{contractId}/payments/{paymentId}/status")
    @PreAuthorize("hasRole('ADMIN')")
    ResponseEntity<ApiResponseDto<PaymentRecordResponse>> updatePaymentRecordStatus(@PathVariable Long paymentId,
                                                                                    @PathVariable Long contractId,
                                                                                    @RequestBody @Validated(PaymentRecordRequest.update.class) PaymentRecordRequest request);

    @Operation(summary = "Search and filter messages", description = "Search and filter messages with pagination and sorting (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "messages retrieved successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user")
    })
    @GetMapping("/{contractId}/messages")
    @PreAuthorize("hasRole('ADMIN')")
    ResponseEntity<PaginationApiResponseDto<List<ContractMessageResponse>>> searchMessage(@PathVariable Long contractId, ContractMessageFilter filter);

    @Operation(summary = "Update messages status", description = "Update the status of a messages (Admin only)")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "messages status updated successfully",
                    content = @Content(schema = @Schema(implementation = ContractMessageResponse.class))),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden - Not an admin user"),
            @ApiResponse(responseCode = "404", description = "Contract not found")
    })
    @PatchMapping("/{contractId}/messages/{messageId}/read")
    @PreAuthorize("hasRole('ADMIN')")
    ResponseEntity<ApiResponseDto<ContractMessageResponse>> updateMessageIsRead(@PathVariable Long contractId,
                                                                                @PathVariable Long messageId);
}