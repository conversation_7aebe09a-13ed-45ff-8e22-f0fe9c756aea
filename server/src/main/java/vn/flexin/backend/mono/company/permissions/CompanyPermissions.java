package vn.flexin.backend.mono.company.permissions;

import com.fasterxml.jackson.annotation.JsonCreator;
import vn.flexin.backend.mono.common.enums.AppStatus;
import vn.flexin.backend.mono.common.exception.ResponseAppStatusException;

import java.util.Arrays;

public enum CompanyPermissions {
    COMPANY_READ("company_read"),
    COMPANY_UPDATE("company_update"),
    COMPANY_DELETE("company_delete"),
    COMPANY_CREATE("company_create"),
    COMPANY_INVITE_STAFF("company_invite_staff"),
    COMPANY_UPDATE_STAFF("company_update_staff"),
    COMPANY_REMOVE_STAFF("company_remove_staff"),
    COMPANY_VERIFY("company_verify"),
    COMPANY_CREATE_BRANCH("company_create_branch"),
    COMPANY_UPDATE_BRANCH("company_update_branch"),
    COMPANY_DELETE_BRANCH("company_delete_branch");

    private final String value;

    CompanyPermissions(String value) {
        this.value = value;
    }

    public static String getValues() {
        return String.join(", ", Arrays.stream(values()).map(x -> x.name().toLowerCase()).toList());
    }

    @JsonCreator
    public static CompanyPermissions fromString(String value) {
        try {
            return valueOf(value.toUpperCase());
        } catch (Exception ex) {
            throw new ResponseAppStatusException(AppStatus.BAD_REQUEST, "User permissions type must be any of [" + getValues() + "]");
        }
    }
}
