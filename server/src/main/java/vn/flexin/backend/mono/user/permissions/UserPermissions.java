package vn.flexin.backend.mono.user.permissions;

import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.Getter;
import vn.flexin.backend.mono.common.enums.AppStatus;
import vn.flexin.backend.mono.common.exception.ResponseAppStatusException;

import java.util.Arrays;

@Getter
public enum UserPermissions {
    USER_READ("user_read"),
    USER_UPDATE("user_update"),
    USER_DELETE("user_delete"),
    USER_CREATE("user_create"),
    USER_ASSIGN_ROLE("user_assign_role"),
    USER_REMOVE_ROLE("user_remove_role"),
    VIEW_ACCOUNT_MANAGEMENT("view_account_management");

    private final String value;

    UserPermissions(String value) {
        this.value = value;
    }

    public static String getValues() {
        return String.join(", ", Arrays.stream(values()).map(x -> x.name().toLowerCase()).toList());
    }

    @JsonCreator
    public static UserPermissions fromString(String value) {
        try {
            return valueOf(value.toUpperCase());
        } catch (Exception ex) {
            throw new ResponseAppStatusException(AppStatus.BAD_REQUEST, "User permissions type must be any of [" + getValues() + "]");
        }
    }
}

