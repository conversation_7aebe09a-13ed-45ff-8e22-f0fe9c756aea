package vn.flexin.backend.mono.common.repository.param;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
public class Join {
    private String column;
    private List<Where> whereList;
    private Join nestedJoin;

    public Join(String column, List<Where> whereList) {
        this.column = column;
        this.whereList = whereList;
    }

    public Join(String column, List<Where> whereList, Join nestedJoin) {
        this.column = column;
        this.whereList = whereList != null ? whereList : new ArrayList<>();
        this.nestedJoin = nestedJoin;
    }

    public Join(String column, Join nestedJoin) {
        this.column = column;
        this.whereList = new ArrayList<>();
        this.nestedJoin = nestedJoin;
    }

}