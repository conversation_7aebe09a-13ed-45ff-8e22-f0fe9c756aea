package vn.flexin.backend.mono.address.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.flexin.backend.mono.common.entity.AbstractAuditingEntity;

@Data
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "t_addresses")
public class Address extends AbstractAuditingEntity<Long> {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    @JoinColumn(name = "province_code")
    private Province province;

    @ManyToOne
    @JoinColumn(name = "district_code")
    private District district;


    @ManyToOne
    @JoinColumn(name = "ward_code")
    private Ward ward;

    private String detailAddress;

}
