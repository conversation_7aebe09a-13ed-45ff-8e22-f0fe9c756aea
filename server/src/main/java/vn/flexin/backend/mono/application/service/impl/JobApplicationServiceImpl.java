package vn.flexin.backend.mono.application.service.impl;

import vn.flexin.backend.mono.application.dto.JobApplicationDto;
import vn.flexin.backend.mono.application.dto.SearchJobApplicationRequest;
import vn.flexin.backend.mono.common.exception.BadRequestException;
import vn.flexin.backend.mono.common.exception.ResourceNotFoundException;
import vn.flexin.backend.mono.job.entity.Job;
import vn.flexin.backend.mono.application.entity.JobApplication;
import vn.flexin.backend.mono.resume.entity.Resume;
import vn.flexin.backend.mono.user.entity.User;
import vn.flexin.backend.mono.application.repository.JobApplicationRepository;
import vn.flexin.backend.mono.application.service.JobApplicationService;
import vn.flexin.backend.mono.job.service.JobService;
import vn.flexin.backend.mono.resume.service.ResumeService;
import vn.flexin.backend.mono.user.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class JobApplicationServiceImpl implements JobApplicationService {

    @Autowired
    private JobApplicationRepository jobApplicationRepository;

    @Autowired
    private JobService jobService;

    @Autowired
    private UserService userService;

    @Autowired
    private ResumeService resumeService;

    @Override
    public JobApplicationDto createJobApplication(JobApplicationDto jobApplicationDto) {
        // Check if the job seeker has already applied for this job
        if (existsByJobIdAndJobSeekerId(jobApplicationDto.getJobId(), jobApplicationDto.getJobSeekerId())) {
            throw new BadRequestException("You have already applied for this job");
        }

        Job job = jobService.getJobEntityById(jobApplicationDto.getJobId());
        User jobSeeker = userService.getUserEntityById(jobApplicationDto.getJobSeekerId());
        Resume resume = resumeService.getResumeEntityById(jobApplicationDto.getResumeId());

        JobApplication jobApplication = new JobApplication();
        jobApplication.setJob(job);
        jobApplication.setJobSeeker(jobSeeker);
        jobApplication.setResume(resume);
        jobApplication.setCoverLetter(jobApplicationDto.getCoverLetter());
        jobApplication.setStatus(jobApplicationDto.getStatus());

        JobApplication savedJobApplication = jobApplicationRepository.save(jobApplication);
        return mapToDto(savedJobApplication);
    }

    @Override
    public JobApplicationDto getJobApplicationById(Long id) {
        JobApplication jobApplication = getJobApplicationEntityById(id);
        return mapToDto(jobApplication);
    }

    @Override
    public List<JobApplicationDto> getJobApplicationsByJobId(Long jobId) {
        Job job = jobService.getJobEntityById(jobId);
        List<JobApplication> jobApplications = jobApplicationRepository.findByJob(job);
        return jobApplications.stream().map(this::mapToDto).collect(Collectors.toList());
    }

    @Override
    public List<JobApplicationDto> getJobApplicationsByJobSeekerId(Long jobSeekerId) {
        User jobSeeker = userService.getUserEntityById(jobSeekerId);
        List<JobApplication> jobApplications = jobApplicationRepository.findByJobSeeker(jobSeeker);
        return jobApplications.stream().map(this::mapToDto).collect(Collectors.toList());
    }

    @Override
    public List<JobApplicationDto> getJobApplicationsByResumeId(Long resumeId) {
        Resume resume = resumeService.getResumeEntityById(resumeId);
        List<JobApplication> jobApplications = jobApplicationRepository.findByResume(resume);
        return jobApplications.stream().map(this::mapToDto).collect(Collectors.toList());
    }

    @Override
    public List<JobApplicationDto> getJobApplicationsByJobIdAndStatus(Long jobId, String status) {
        Job job = jobService.getJobEntityById(jobId);
        List<JobApplication> jobApplications = jobApplicationRepository.findByJobAndStatus(job, status);
        return jobApplications.stream().map(this::mapToDto).collect(Collectors.toList());
    }

    @Override
    public List<JobApplicationDto> getJobApplicationsByJobSeekerIdAndStatus(Long jobSeekerId, String status) {
        User jobSeeker = userService.getUserEntityById(jobSeekerId);
        List<JobApplication> jobApplications = jobApplicationRepository.findByJobSeekerAndStatus(jobSeeker, status);
        return jobApplications.stream().map(this::mapToDto).collect(Collectors.toList());
    }

    @Override
    public JobApplicationDto updateJobApplication(Long id, JobApplicationDto jobApplicationDto) {
        JobApplication jobApplication = getJobApplicationEntityById(id);
        
        // Only allow updating cover letter
        jobApplication.setCoverLetter(jobApplicationDto.getCoverLetter());
        
        JobApplication updatedJobApplication = jobApplicationRepository.save(jobApplication);
        return mapToDto(updatedJobApplication);
    }

    @Override
    public JobApplicationDto updateJobApplicationStatus(Long id, String status, String employerNotes) {
        JobApplication jobApplication = getJobApplicationEntityById(id);
        
        jobApplication.setStatus(status);
        jobApplication.setEmployerNotes(employerNotes);
        
        if (status.equals("reviewed")) {
            jobApplication.setReviewedAt(LocalDateTime.now());
        }
        
        JobApplication updatedJobApplication = jobApplicationRepository.save(jobApplication);
        return mapToDto(updatedJobApplication);
    }

    @Override
    public void deleteJobApplication(Long id) {
        JobApplication jobApplication = getJobApplicationEntityById(id);
        jobApplicationRepository.delete(jobApplication);
    }

    @Override
    public boolean existsByJobIdAndJobSeekerId(Long jobId, Long jobSeekerId) {
        Job job = jobService.getJobEntityById(jobId);
        User jobSeeker = userService.getUserEntityById(jobSeekerId);
        Optional<JobApplication> jobApplication = jobApplicationRepository.findByJobAndJobSeeker(job, jobSeeker);
        return jobApplication.isPresent();
    }

    @Override
    public JobApplication getJobApplicationEntityById(Long id) {
        return jobApplicationRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("JobApplication", "id", id));
    }

    @Override
    public Page<JobApplicationDto> searchJobApplications(SearchJobApplicationRequest searchJobApplicationRequest) {
        Integer pageNumber = searchJobApplicationRequest.getPageNumber();
        Integer numberElementInPage = searchJobApplicationRequest.getPageSize();
        
        Pageable pageable = PageRequest.of(pageNumber - 1, numberElementInPage);
        
        // For now, we'll implement a simple search based on available methods
        // In a real implementation, you would create a JobApplicationRepositoryCustom similar to JobRepositoryCustom
        
        Page<JobApplication> applicationPage;
        
        // If we have filters, use them
        if (searchJobApplicationRequest.getFilters() != null && !searchJobApplicationRequest.getFilters().isEmpty()) {
            // This is a simplified implementation
            // In a real scenario, you would process all filters properly
            applicationPage = jobApplicationRepository.findAll(pageable);
        } else if (searchJobApplicationRequest.getKeyword() != null && !searchJobApplicationRequest.getKeyword().isEmpty()) {
            // If we have a keyword, search by keyword
            // This is a placeholder - you would implement a proper keyword search
            applicationPage = jobApplicationRepository.findAll(pageable);
        } else {
            // Otherwise, get all applications with pagination
            applicationPage = jobApplicationRepository.findAll(pageable);
        }
        
        return applicationPage.map(this::mapToDto);
    }

    private JobApplicationDto mapToDto(JobApplication jobApplication) {
        JobApplicationDto jobApplicationDto = new JobApplicationDto();
        jobApplicationDto.setId(jobApplication.getId());
        jobApplicationDto.setJobId(jobApplication.getJob().getId());
        jobApplicationDto.setJobTitle(jobApplication.getJob().getTitle());
        jobApplicationDto.setJobSeekerId(jobApplication.getJobSeeker().getId());
        jobApplicationDto.setJobSeekerName(jobApplication.getJobSeeker().getName());
        jobApplicationDto.setResumeId(jobApplication.getResume().getId());
        jobApplicationDto.setResumeName(jobApplication.getResume().getName());
        jobApplicationDto.setCoverLetter(jobApplication.getCoverLetter());
        jobApplicationDto.setStatus(jobApplication.getStatus());
        jobApplicationDto.setEmployerNotes(jobApplication.getEmployerNotes());
        jobApplicationDto.setRejectionReason(jobApplication.getRejectionReason());
        return jobApplicationDto;
    }
} 