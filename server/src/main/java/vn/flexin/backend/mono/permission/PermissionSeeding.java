package vn.flexin.backend.mono.permission;

import jakarta.ws.rs.NotFoundException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.keycloak.admin.client.resource.RolesResource;
import org.keycloak.representations.idm.RoleRepresentation;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;
import vn.flexin.backend.mono.auth.service.keycloak.AdminKeycloakService;
import vn.flexin.backend.mono.company.permissions.CompanyPermissions;
import vn.flexin.backend.mono.jobseekerpost.permissions.JobSeekerPostPermissions;
import vn.flexin.backend.mono.permission.entity.Permission;
import vn.flexin.backend.mono.permission.permissions.PermissionPermissions;
import vn.flexin.backend.mono.permission.repository.PermissionRepository;
import vn.flexin.backend.mono.role.permissions.RolePermissions;
import vn.flexin.backend.mono.user.permissions.UserPermissions;
import vn.flexin.backend.mono.post.permissions.PostPermissions;
import vn.flexin.backend.mono.resume.permissions.ResumePermissions;

import java.util.Arrays;
import java.util.stream.Stream;

@Component
@AllArgsConstructor
@Slf4j
class PermissionSeeding implements CommandLineRunner {

    private final PermissionRepository permissionRepository;
    private final AdminKeycloakService adminKeycloakService;

    @Override
    public void run(String... args) throws Exception {
        log.info("Starting permission seeding process...");

        Enum<?>[] allPermissions = getAllPermissions();

        log.info("Found {} permissions to seed", allPermissions.length);

        for (Enum<?> permission : allPermissions) {
            log.info("Processing permission: {}", permission.name());
            createPermission(permission.name().toLowerCase());
        }

        log.info("Permission seeding process completed successfully!");
    }

    public void createPermission(String name) {
        try {
            if (permissionRepository.existsByName(name)) {
                log.debug("Permission '{}' already exists, skipping...", name);
                return;
            }

            var existedPermission = permissionRepository.findByName(name);
            if (existedPermission.isPresent()) {
                log.debug("Permission '{}' found in database, skipping...", name);
                return;
            }

            Permission permission = new Permission();
            permission.setName(name);
            permission.setDescription(name);

            String keycloakId = createPermissionInKeycloak(name);
            if (keycloakId != null) {
                permission.setKeycloakId(keycloakId);
                log.info("Created permission '{}' in Keycloak with ID: {}", name, keycloakId);
            } else {
                log.warn("Failed to create permission '{}' in Keycloak, but will save to database", name);
            }

            permissionRepository.save(permission);
            log.info("Successfully saved permission '{}' to database", name);

        } catch (Exception e) {
            log.error("Error creating permission '{}': {}", name, e.getMessage(), e);
        }
    }

    private String createPermissionInKeycloak(String name) {
        try {
            RolesResource rolesResource = adminKeycloakService.getRealmResource().roles();

            // Check if role already exists in Keycloak
            try {
                var existingRole = rolesResource.get(name).toRepresentation();
                if (existingRole != null) {
                    log.debug("Role '{}' already exists in Keycloak with ID: {}", name, existingRole.getId());
                    return existingRole.getId();
                }
            } catch (NotFoundException e) {
                // Role doesn't exist, continue with creation
                log.debug("Role '{}' not found in Keycloak, creating new one...", name);
            }

            RoleRepresentation newRole = new RoleRepresentation();
            newRole.setName(name);
            newRole.setDescription(name);

            rolesResource.create(newRole);

            // Get the created role to retrieve its ID
            return rolesResource.get(name).toRepresentation().getId();

        } catch (NotFoundException ex) {
            log.error("Keycloak role '{}' not found after creation", name);
            return null;
        } catch (Exception ex) {
            log.error("Error creating role '{}' in Keycloak: {}", name, ex.getMessage(), ex);
            return null;
        }
    }

    public static Enum<?>[] getAllPermissions() {
        return Stream.of(
                        UserPermissions.values(),
                        PermissionPermissions.values(),
                        RolePermissions.values(),
                        CompanyPermissions.values(),
                        PostPermissions.values(),
                        ResumePermissions.values(),
                        JobSeekerPostPermissions.values()
                ).flatMap(Arrays::stream)
                .toArray(size -> new Enum<?>[size]);
    }
}