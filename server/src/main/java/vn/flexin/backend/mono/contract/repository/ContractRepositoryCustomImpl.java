package vn.flexin.backend.mono.contract.repository;

import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import jakarta.persistence.criteria.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;
import vn.flexin.backend.mono.contract.dto.ContractFilter;
import vn.flexin.backend.mono.contract.entity.Contract;
import vn.flexin.backend.mono.user.entity.User;

import java.util.ArrayList;
import java.util.List;

@Repository
public class ContractRepositoryCustomImpl implements ContractRepositoryCustom {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public Page<Contract> findAllWithEmployerAndJobSeeker(ContractFilter filter) {
        CriteriaBuilder cb = entityManager.getCriteriaBuilder();
        CriteriaQuery<Contract> query = cb.createQuery(Contract.class);
        Root<Contract> root = query.from(Contract.class);

        // Fetch employer and jobSeeker to avoid lazy loading issues
        root.fetch("employer", JoinType.LEFT);
        root.fetch("jobSeeker", JoinType.LEFT);

        List<Predicate> predicates = buildPredicates(cb, root, filter);

        if (!predicates.isEmpty()) {
            query.where(cb.and(predicates.toArray(new Predicate[0])));
        }

        // Apply pagination
        Pageable pageable = filter.toPageable();
        TypedQuery<Contract> typedQuery = entityManager.createQuery(query.distinct(true));
        typedQuery.setFirstResult((int) pageable.getOffset());
        typedQuery.setMaxResults(pageable.getPageSize());

        List<Contract> contracts = typedQuery.getResultList();

        // Count total results
        long total = countTotal(cb, filter);

        return new PageImpl<>(contracts, pageable, total);
    }

    private List<Predicate> buildPredicates(CriteriaBuilder cb, Root<Contract> root, ContractFilter filter) {
        List<Predicate> predicates = new ArrayList<>();

        if (filter.getStatus() != null) {
            predicates.add(cb.equal(root.get("status"), filter.getStatus()));
        }

        if (filter.getContractType() != null) {
            predicates.add(cb.equal(root.get("contractType"), filter.getContractType()));
        }

        if (filter.getEmployerId() != null) {
            Join<Contract, User> employerJoin = root.join("employer");
            predicates.add(cb.equal(employerJoin.get("id"), filter.getEmployerId()));
        }

        if (filter.getJobSeekerId() != null) {
            Join<Contract, User> jobSeekerJoin = root.join("jobSeeker");
            predicates.add(cb.equal(jobSeekerJoin.get("id"), filter.getJobSeekerId()));
        }

        if (filter.getSearch() != null && !filter.getSearch().trim().isEmpty()) {
            String searchPattern = "%" + filter.getSearch().toLowerCase() + "%";
            Predicate titlePredicate = cb.like(cb.lower(root.get("title")), searchPattern);
            Predicate idPredicate = cb.like(cb.lower(root.get("id").as(String.class)), searchPattern);
            predicates.add(cb.or(titlePredicate, idPredicate));
        }

        return predicates;
    }

    private long countTotal(CriteriaBuilder cb, ContractFilter filter) {
        CriteriaQuery<Long> countQuery = cb.createQuery(Long.class);
        Root<Contract> countRoot = countQuery.from(Contract.class);
        countQuery.select(cb.count(countRoot));

        List<Predicate> predicates = buildPredicates(cb, countRoot, filter);
        if (!predicates.isEmpty()) {
            countQuery.where(cb.and(predicates.toArray(new Predicate[0])));
        }

        return entityManager.createQuery(countQuery).getSingleResult();
    }
}
