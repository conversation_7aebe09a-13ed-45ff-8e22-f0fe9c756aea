package vn.flexin.backend.mono.common.exception;

import lombok.Getter;
import org.springframework.core.NestedRuntimeException;
import org.springframework.http.HttpStatus;
import vn.flexin.backend.mono.common.dto.ErrorDto;

import java.util.List;

@Getter
public class ApiException extends NestedRuntimeException {
    private final HttpStatus httpStatus;
    private final List<ErrorDto> errors;

    public ApiException(HttpStatus httpStatus, List<ErrorDto> errors) {
        super("");
        this.httpStatus = httpStatus;
        this.errors = errors;
    }

}