package vn.flexin.backend.mono.job.repository;

import vn.flexin.backend.mono.job.entity.Job;
import vn.flexin.backend.mono.user.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface JobRepository extends JpaRepository<Job, Long>, JobRepositoryCustom {
    List<Job> findByEmployerId(Long employerId);
    
    List<Job> findByStatus(String status);
    
    List<Job> findByEmployerIdAndStatus(Long employerId, String status);
    
    @Query("SELECT j FROM Job j WHERE " +
           "LOWER(j.title) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(j.description) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(j.location) LIKE LOWER(CONCAT('%', :keyword, '%'))")
    List<Job> searchJobs(String keyword);
    
    List<Job> findByLocationContainingIgnoreCase(String location);
    
    List<Job> findByJobType(String jobType);
    
    List<Job> findByHourlyRateBetween(Double minRate, Double maxRate);
    
    List<Job> findByEmployer(User employer);
    
    List<Job> findByEmployerAndStatus(User employer, String status);
    
    List<Job> findByLocation(String location);
    
    @Query("SELECT j FROM Job j WHERE j.hourlyRate >= :minRate AND j.hourlyRate <= :maxRate")
    List<Job> findByHourlyRateRange(Double minRate, Double maxRate);
} 