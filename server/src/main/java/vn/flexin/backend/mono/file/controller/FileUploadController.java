package vn.flexin.backend.mono.file.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import vn.flexin.backend.mono.common.dto.ApiResponse;
import vn.flexin.backend.mono.file.dto.FilePathResponse;
import vn.flexin.backend.mono.file.dto.ListFilePathResponse;

import java.util.Map;

@RequestMapping("/v1/files")
@Tag(name = "File APIs", description = "File management endpoints")
public interface FileUploadController {

    @Operation(summary = "Upload file", description = "Upload file to storage server")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Successfully upload file"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Bad request"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PostMapping("/upload")
    ResponseEntity<ApiResponse<FilePathResponse>> uploadFile(@RequestParam("file") MultipartFile file, String type);


    @Operation(summary = "Upload files", description = "Upload files to storage server")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Successfully upload files"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Bad request"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PostMapping("/upload/multiple")
    ResponseEntity<ApiResponse<ListFilePathResponse>> uploadMultipleFiles(@RequestParam("files") MultipartFile[] files, String type);


    @Operation(summary = "Retrieve image by name", description = "Retrieve image by name")
    @ApiResponses(value = {
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "Successfully retrieve image"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "Bad request"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "403", description = "Access denied"),
            @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "Not found")
    })
    @GetMapping("/images/{imageName}")
    ResponseEntity<ApiResponse<Map<String, String>>> retrieveImageByName(@PathVariable("imageName") String imageName);



}
