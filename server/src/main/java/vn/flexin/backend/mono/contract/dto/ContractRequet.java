package vn.flexin.backend.mono.contract.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Data
@NoArgsConstructor
public class ContractRequet {
    private Long id;
    
    @NotBlank(message = "Title is required")
    private String title;
    
    @NotNull(message = "Employer ID is required")
    private Long employerId;
    
    private String employerName;
    
    @NotNull(message = "Job seeker ID is required")
    private Long jobSeekerId;
    
    private String jobSeekerName;
    
    @NotNull(message = "Start date is required")
    private LocalDate startDate;
    
    @NotNull(message = "End date is required")
    private LocalDate endDate;
    
    @NotNull(message = "Hourly rate is required")
    @Positive(message = "Hourly rate must be positive")
    private Double hourlyRate;
    
    @NotBlank(message = "Payment frequency is required")
    private String paymentFrequency; // weekly, biweekly, monthly
    
    @NotNull(message = "Working hours per week is required")
    @Positive(message = "Working hours per week must be positive")
    private Integer workingHoursPerWeek;
    
    private Set<String> workDays = new HashSet<>();
    
    @NotBlank(message = "Contract type is required")
    private String contractType; // part-time, freelance, project
    
    private String status; // draft, offered, active, completed, terminated

    private LocalDateTime activatedAt;
    
    private LocalDateTime terminatedAt;
    
    private String terminationReason;
    
    private boolean isFeePaid;
    
    private String additionalTerms;

    private List<TimeEntryResponse> timeEntries;

    private Set<PaymentRecordResponse> payments;

    private List<ContractMessageResponse> messages;
} 