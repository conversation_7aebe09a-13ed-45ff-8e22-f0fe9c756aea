package vn.flexin.backend.mono.common.repository.param;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;


@Data
public class Condition {
    private List<Where> whereList;
    private List<Where> whereComplex;
    private Join join;

    public Condition append(Where where) {
        if (whereList == null) {
            whereList = new ArrayList<>();
        }
        whereList.add(where);
        return this;
    }

    public Condition appendComplex(Where where) {
        if (whereComplex == null) {
            whereComplex = new ArrayList<>();
        }
        whereComplex.add(where);
        return this;
    }

    public Condition append(Join join) {
        this.join = join;
        return this;
    }

    public Condition appendEqualCondition(String property, Object value) {
        return this.append(new Where(property, value));
    }
}