package vn.flexin.backend.mono.company.dto.company;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@Getter
@Setter
public class SearchAdminBranchByCompanyRequest {
    private String search;
    private String sortBy;
    private String sortOrder;
    private Integer page = 1;
    private Integer pageSize = 10;

}
