package vn.flexin.backend.mono.contract.repository;

import vn.flexin.backend.mono.common.repository.JpaSpecificationRepository;
import vn.flexin.backend.mono.contract.entity.Contract;
import vn.flexin.backend.mono.user.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ContractRepository extends JpaSpecificationRepository<Contract, Long> {
    List<Contract> findByEmployer(User employer);
    List<Contract> findByJobSeeker(User jobSeeker);
    List<Contract> findByEmployerAndStatus(User employer, String status);
    List<Contract> findByJobSeekerAndStatus(User jobSeeker, String status);
} 