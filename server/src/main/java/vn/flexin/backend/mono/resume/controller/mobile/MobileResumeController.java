package vn.flexin.backend.mono.resume.controller.mobile;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.flexin.backend.mono.common.dto.ApiResponseDto;
import vn.flexin.backend.mono.common.dto.CreateObjectResponse;
import vn.flexin.backend.mono.resume.dto.EducationDto;
import vn.flexin.backend.mono.resume.dto.ResumeDto;
import vn.flexin.backend.mono.resume.dto.SearchResumeResponse;
import vn.flexin.backend.mono.resume.dto.WorkExperienceDto;
import vn.flexin.backend.mono.user.dto.ContactInfoDto;
import vn.flexin.backend.mono.user.dto.PartTimePreferenceDto;

import java.util.List;

@RestController
@RequestMapping("/v1/mobile/resumes")
@Tag(name = "Mobile Resumes APIs", description = "Mobile resume management endpoints")
public interface MobileResumeController {
    
    @Operation(summary = "Create a new resume", description = "Create a new resume (Job Seeker only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Successfully created resume"),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PostMapping
//    @PreAuthorize("hasRole('JOB_SEEKER')")
    ResponseEntity<ApiResponseDto<CreateObjectResponse>> createResume(@Valid @RequestBody ResumeDto resumeDto);
    
    @Operation(summary = "Get resume by ID", description = "Get a resume by its ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved resume"),
        @ApiResponse(responseCode = "404", description = "Resume not found"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @GetMapping("/{id}")
//    @PreAuthorize("hasRole('JOB_SEEKER') or hasRole('EMPLOYER') or hasRole('ADMIN')")
    ResponseEntity<ApiResponseDto<ResumeDto>> getResumeById(@PathVariable Long id);
    
    @Operation(summary = "Get resumes by user ID", description = "Get all resumes for a specific user")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved resumes"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @GetMapping("/user/{userId}")
//    @PreAuthorize("hasRole('JOB_SEEKER') or hasRole('EMPLOYER') or hasRole('ADMIN')")
    ResponseEntity<ApiResponseDto<List<SearchResumeResponse>>> getResumesByUserId(@PathVariable Long userId);

    @Operation(summary = "Get resumes by current login user", description = "Get all resumes for current login user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved resumes"),
            @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @GetMapping("/me")
//    @PreAuthorize("hasRole('JOB_SEEKER') or hasRole('EMPLOYER') or hasRole('ADMIN')")
    ResponseEntity<ApiResponseDto<List<SearchResumeResponse>>> getMyResumes(@RequestParam(value = "isActive", required = false) Boolean isActive);
    
    @Operation(summary = "Get active resumes by user ID", description = "Get all active resumes for a specific user")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved active resumes"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @GetMapping("/user/{userId}/active")
//    @PreAuthorize("hasRole('JOB_SEEKER') or hasRole('EMPLOYER') or hasRole('ADMIN')")
    ResponseEntity<ApiResponseDto<List<SearchResumeResponse>>> getActiveResumesByUserId(@PathVariable Long userId);
    
    @Operation(summary = "Update resume", description = "Update a resume (Job Seeker only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully updated resume"),
        @ApiResponse(responseCode = "404", description = "Resume not found"),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PutMapping("/{id}")
//    @PreAuthorize("hasRole('JOB_SEEKER')")
    ResponseEntity<ApiResponseDto<ResumeDto>> updateResume(@PathVariable Long id, @Valid @RequestBody ResumeDto resumeDto);
    
    @Operation(summary = "Delete resume", description = "Delete a resume (Job Seeker or Admin only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully deleted resume"),
        @ApiResponse(responseCode = "404", description = "Resume not found"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @DeleteMapping("/{id}")
//    @PreAuthorize("hasRole('JOB_SEEKER') or hasRole('ADMIN')")
    ResponseEntity<ApiResponseDto<Boolean>> deleteResume(@PathVariable Long id);
    
    // Work Experience endpoints
    @Operation(summary = "Add work experience", description = "Add work experience to a resume (Job Seeker only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Successfully added work experience"),
        @ApiResponse(responseCode = "404", description = "Resume not found"),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PostMapping("/{resumeId}/work-experiences")
//    @PreAuthorize("hasRole('JOB_SEEKER')")
    ResponseEntity<ApiResponseDto<WorkExperienceDto>> addWorkExperience(
            @PathVariable Long resumeId,
            @Valid @RequestBody WorkExperienceDto workExperienceDto);
    
    @Operation(summary = "Update work experience", description = "Update work experience (Job Seeker only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully updated work experience"),
        @ApiResponse(responseCode = "404", description = "Work experience not found"),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PutMapping("/work-experiences/{id}")
//    @PreAuthorize("hasRole('JOB_SEEKER')")
    ResponseEntity<ApiResponseDto<WorkExperienceDto>> updateWorkExperience(
            @PathVariable Long id,
            @Valid @RequestBody WorkExperienceDto workExperienceDto);
    
    @Operation(summary = "Delete work experience", description = "Delete work experience (Job Seeker only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully deleted work experience"),
        @ApiResponse(responseCode = "404", description = "Work experience not found"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @DeleteMapping("/work-experiences/{id}")
//    @PreAuthorize("hasRole('JOB_SEEKER')")
    ResponseEntity<ApiResponseDto<Boolean>> deleteWorkExperience(@PathVariable Long id);
    
    // Education endpoints
    @Operation(summary = "Add education", description = "Add education to a resume (Job Seeker only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Successfully added education"),
        @ApiResponse(responseCode = "404", description = "Resume not found"),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PostMapping("/{resumeId}/educations")
//    @PreAuthorize("hasRole('JOB_SEEKER')")
    ResponseEntity<ApiResponseDto<EducationDto>> addEducation(
            @PathVariable Long resumeId,
            @Valid @RequestBody EducationDto educationDto);
    
    @Operation(summary = "Update education", description = "Update education (Job Seeker only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully updated education"),
        @ApiResponse(responseCode = "404", description = "Education not found"),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PutMapping("/educations/{id}")
//    @PreAuthorize("hasRole('JOB_SEEKER')")
    ResponseEntity<ApiResponseDto<EducationDto>> updateEducation(
            @PathVariable Long id,
            @Valid @RequestBody EducationDto educationDto);
    
    @Operation(summary = "Delete education", description = "Delete education (Job Seeker only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully deleted education"),
        @ApiResponse(responseCode = "404", description = "Education not found"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @DeleteMapping("/educations/{id}")
//    @PreAuthorize("hasRole('JOB_SEEKER')")
    ResponseEntity<ApiResponseDto<Boolean>> deleteEducation(@PathVariable Long id);
    
    // Contact Info endpoint
    @Operation(summary = "Update contact info", description = "Update contact info for a resume (Job Seeker only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully updated contact info"),
        @ApiResponse(responseCode = "404", description = "Resume not found"),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PutMapping("/{resumeId}/contact-info")
//    @PreAuthorize("hasRole('JOB_SEEKER')")
    ResponseEntity<ApiResponseDto<ContactInfoDto>> updateContactInfo(
            @PathVariable Long resumeId,
            @Valid @RequestBody ContactInfoDto contactInfoDto);
    
    // Part Time Preference endpoint
    @Operation(summary = "Update part time preference", description = "Update part time preference for a resume (Job Seeker only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully updated part time preference"),
        @ApiResponse(responseCode = "404", description = "Resume not found"),
        @ApiResponse(responseCode = "400", description = "Invalid input"),
        @ApiResponse(responseCode = "403", description = "Access denied")
    })
    @PutMapping("/{resumeId}/part-time-preference")
//    @PreAuthorize("hasRole('JOB_SEEKER')")
    ResponseEntity<ApiResponseDto<PartTimePreferenceDto>> updatePartTimePreference(
            @PathVariable Long resumeId,
            @Valid @RequestBody PartTimePreferenceDto partTimePreferenceDto);
    
//    @Operation(summary = "Search and filter resumes", description = "Search and filter resumes with pagination and sorting")
//    @ApiResponses(value = {
//        @ApiResponse(responseCode = "200", description = "Successfully retrieved resumes"),
//        @ApiResponse(responseCode = "400", description = "Invalid input"),
//        @ApiResponse(responseCode = "403", description = "Access denied")
//    })
//    @PostMapping("/search")
////    @PreAuthorize("hasRole('JOB_SEEKER') or hasRole('EMPLOYER') or hasRole('ADMIN')")
//    ResponseEntity<PaginationApiResponseDto<List<SearchResumeResponse>>> searchResumes(@Valid @RequestBody ResumeFilter resumeFilter);

} 