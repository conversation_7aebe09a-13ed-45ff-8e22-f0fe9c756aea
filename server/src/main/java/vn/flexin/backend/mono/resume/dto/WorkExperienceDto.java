package vn.flexin.backend.mono.resume.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;

@Data
@NoArgsConstructor
public class WorkExperienceDto {
    private Long id;
    
    @NotBlank(message = "Company name is required")
    private String company;
    
    @NotBlank(message = "Position is required")
    private String position;
    
    @NotNull(message = "Start date is required")
    private LocalDate startDate;
    
    private LocalDate endDate;
    
    private String description;
    
    private Long resumeId;
} 