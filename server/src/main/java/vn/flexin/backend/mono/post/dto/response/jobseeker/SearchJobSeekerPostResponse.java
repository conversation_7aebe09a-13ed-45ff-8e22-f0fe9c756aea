package vn.flexin.backend.mono.post.dto.response.jobseeker;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.flexin.backend.mono.address.dto.response.SimpleAddressResponse;
import vn.flexin.backend.mono.post.enums.PostWorkDay;
import vn.flexin.backend.mono.post.enums.PostWorkShift;

import java.util.Set;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SearchJobSeekerPostResponse {
    private Long id;
    private Long jobSeekerId;
    private String status;
    private Set<String> skills;
    private String title;
    private String description;
    private Set<PostWorkDay> workingDays;
    private SimpleAddressResponse location;
    private Integer opportunityCount;
    private Integer newOpportunityCount;
    private Integer viewCount;
    private boolean isFeatureJob;
    private String featureDuration;
    private Set<PostWorkShift> workingShifts;
    private Integer interviewRequestCount;
}
