spring:
  datasource:
    url: ***************************************************************************************************
    username: postgres
    password: HlEGpqVWDHCC4LIYpfiq
    driver-class-name: org.postgresql.Driver
  jpa:
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
    hibernate:
      ddl-auto: update
    show-sql: true
  security:
    oauth2:
      client:
        registration:
          user:
            provider: keycloak
            client-id: flexin-user
            authorization-grant-type: authorization_code
            redirect-uri: https://api.flexin.dev/oauth2/flexin
            scope:
              - openid
          admin:
            provider: keycloak
            client-id: flexin-admin
            authorization-grant-type: authorization_code
            redirect-uri: https://api.flexin.dev/oauth2/flexin
            scope:
              - openid
              - profile
        provider:
          keycloak:
            issuer-uri: https://auth.flexin.dev/realms/flexin
      resource-server:
        jwt:
          issuer-uri: https://auth.flexin.dev/realms/flexin
          jwk-set-uri: ${spring.security.oauth2.client.provider.keycloak.issuer-uri}/protocol/openid-connect/certs

# Keycloak Configuration
keycloak:
  auth-server-url: https://auth.flexin.dev
  realm: flexin
  user:
    client-id: flexin-user
    client-secret: D855N9kfSU0TuVEG9BUTW9XhjVZS0NIu
  admin:
    client-id: flexin-admin
    client-secret: bP5PkQ39OBWaSE90i5fcoYemBV76OK5R
  token-uri: ${keycloak.auth-server-url}/realms/${keycloak.realm}/protocol/openid-connect/token
  default-role-name: default-roles-flexin

# JWT Configuration
jwt:
  temp-token:
    secret: "your-256-bit-secret-key-for-temporary-token-generation"

storage:
  aws:
    access-key: ********************
    secret-access-key: LjVXCwbuGfj1fHyBWrVQorVBRcB9tlOy90J1svVJ
    region: ap-southeast-1
    avatar-bucket-name: dev-flexin-user-avatars
    document-bucket-name: dev-flexin-documents
    backup-bucket-name: dev-flexin-backups

swagger:
  server-url: https://api.flexin.dev/
  username: flexin_dev
  password: FlexinD3v@2025

notification:
  sms:
    speedSmsUrl: abc
    apiKey: abc

