<!doctype html>
<html
  xmlns:th="http://www.thymeleaf.org"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.thymeleaf.org"
  th:lang="${#locale.language}"
  lang="en"
>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <link rel="icon" href="${baseUrl}/favicon.ico" />
    <title>Your request cannot be processed</title>
    <style>
      ::-moz-selection {
        background: #b3d4fc;
        text-shadow: none;
      }

      ::selection {
        background: #b3d4fc;
        text-shadow: none;
      }

      html {
        padding: 30px 10px;
        font-size: 20px;
        line-height: 1.4;
        color: #737373;
        background: #3e8acc;
        -webkit-text-size-adjust: 100%;
        -ms-text-size-adjust: 100%;
      }

      html,
      input {
        font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
      }

      body {
        max-width: 1000px;
        _width: 500px;
        padding: 30px 20px 50px;
        border: 1px solid #b3b3b3;
        border-radius: 4px;
        margin: 0 auto;
        box-shadow:
          0 1px 10px #a7a7a7,
          inset 0 1px 0 #fff;
        background: #fcfcfc;
      }

      h1 {
        margin: 0 10px;
        font-size: 50px;
        text-align: center;
      }

      h1 span {
        color: #bbb;
      }

      h3 {
        margin: 1.5em 0 0.5em;
      }

      p {
        margin: 1em 0;
      }

      ul {
        padding: 0 0 0 40px;
        margin: 1em 0;
      }

      .container {
        max-width: 800px;
        _width: 380px;
        margin: 0 auto;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1 th:text="#{error.title}">Your request cannot be processed <span>:(</span></h1>

      <p th:text="#{error.subtitle}">Sorry, an error has occurred.</p>

      <span th:text="#{error.status}">Status:</span>&nbsp;<span th:text="${error}"></span>&nbsp;(<span th:text="${error}"></span>)<br />
      <span th:if="${!#strings.isEmpty(message)}">
        <span th:text="#{error.message}">Message:</span>&nbsp;<span th:text="${message}"></span><br />
      </span>
    </div>
  </body>
</html>
