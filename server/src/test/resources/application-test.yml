spring:
  datasource:
    url: jdbc:h2:mem:testdb
    username: sa
    password: 
    driver-class-name: org.h2.Driver
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: http://localhost:9090/realms/flexin

# Disable Swagger for tests
springdoc:
  api-docs:
    enabled: false
  swagger-ui:
    enabled: false

# Keycloak Configuration
keycloak:
  auth-server-url: http://localhost:9090
  realm: flexin
  client-id: backend-services
  client-secret: test-secret
  token-uri: ${keycloak.auth-server-url}/realms/${keycloak.realm}/protocol/openid-connect/token

# JWT Configuration
jwt:
  temp-token:
    secret: "test-256-bit-secret-key-for-temporary-token-generation" 