package vn.flexin.backend.mono.config;

import org.mockito.Mockito;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import vn.flexin.backend.mono.auth.service.keycloak.UserKeycloakService;
import vn.flexin.backend.mono.user.service.UserService;

@TestConfiguration
public class TestConfig {

    @Bean
    @Primary
    public UserKeycloakService keycloakService() {
        return Mockito.mock(UserKeycloakService.class);
    }

    @Bean
    @Primary
    public UserService userService() {
        return Mockito.mock(UserService.class);
    }

    @Bean
    @Primary
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
} 