version: '3'
services:
  keycloak-database:
    container_name: keycloak-postgres
    image: postgres:16
    volumes:
      - ./local_storage/db/keycloak:/var/lib/postgresql/data/
    environment:
      POSTGRES_DB: keycloak
      POSTGRES_USER: keycloak
      POSTGRES_PASSWORD: password
    ports:
      - "5455:5432"
    restart: unless-stopped
    networks:
      - flexi-network

  keycloak:
    image: quay.io/keycloak/keycloak:26.1.2
    container_name: keycloak
    environment:
      DB_VENDOR: postgres
      DB_ADDR: keycloak-database
      DB_PORT: 5432
      DB_DATABASE: keycloak
      DB_USER: keycloak
      DB_PASSWORD: password
      KEYCLOAK_USER: admin                # Admin user
      KEYCLOAK_PASSWORD: password     # Admin password
      KC_BOOTSTRAP_ADMIN_USERNAME: admin
      KC_BOOTSTRAP_ADMIN_PASSWORD: password
      HTTP_ENABLED: "true"
      KC_HOSTNAME: localhost                # Change this if needed in production
      KC_HOSTNAME_STRICT_HTTPS: "false"
      KC_HOSTNAME_STRICT: "false"
      KC_PROXY: edge
      KC_PROXY_ADDRESS_FORWARDING: "true"
      KC_PROXY_HEADERS: xforwarded
    ports:
      - "8888:8080"
    depends_on:
      - keycloak-database
    command: [ "start-dev" ]
    networks:
      - flexi-network

networks:
  flexi-network:
    driver: bridge