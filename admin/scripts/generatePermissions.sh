#!/bin/bash

PERM_TOKEN=""

API_URL="https://api.flexin.dev/v1/admin/permissions/search"
OUT_FILE="src/constants/_permissionEnum.ts"

if [ -z "$PERM_TOKEN" ]; then
  echo "PERM_TOKEN env missing."
  exit 1
fi

response=$(curl -s -X POST "$API_URL" \
  -H "Authorization: Bearer $PERM_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"keyword":"","page":0,"limit":1000}')

if [ -z "$response" ]; then
  echo "No response from API"
  exit 1
fi

names=$(echo "$response" | jq -r '.data[].name')

if [ -z "$names" ]; then
  echo "No permissions found or wrong response format."
  exit 1
fi

echo "// AUTO-GENERATED FILE. DO NOT EDIT." > "$OUT_FILE"
echo "export enum PermissionEnum {" >> "$OUT_FILE"
for name in $names; do
  upper=$(echo "$name" | tr '[:lower:]' '[:upper:]')
  echo "  $upper = \"$name\"," >> "$OUT_FILE"
done
echo "}" >> "$OUT_FILE"

echo "Generated: $OUT_FILE"
