// contexts/LanguageContext.tsx
"use client";

import { getUserLocale, setUserLocale } from "@/i18n/locale";
import {
  createContext,
  useContext,
  useEffect,
  useState,
  ReactNode,
} from "react";

type Locale = "en" | "vi";

interface LanguageContextType {
  locale: Locale;
  setLocale: (locale: Locale) => void;
}

const LanguageContext = createContext<LanguageContextType | undefined>(
  undefined
);

export function LanguageProvider({ children }: { children: ReactNode }) {
  const [locale, setLocaleState] = useState<Locale>("en");

  useEffect(() => {
    const fetchLocale = async () => {
      // Load from localStorage on component mount
      const savedLocale = await getUserLocale();
      if (savedLocale && (savedLocale === "en" || savedLocale === "vi")) {
        setLocaleState(savedLocale);
      }
    };
    fetchLocale();
  }, []);

  const setLocale = async (newLocale: Locale) => {
    await setUserLocale(newLocale);
    setLocaleState(newLocale);
  };

  return (
    <LanguageContext.Provider value={{ locale, setLocale }}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error("useLanguage must be used within a LanguageProvider");
  }
  return context;
}
