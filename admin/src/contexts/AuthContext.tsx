// contexts/AuthContext.tsx
"use client";

import { utils } from "@/utils";
import { useRouter } from "next/navigation";
import { createContext, useContext, useEffect, useState } from "react";
import access from "@/access";
import AuthService from "@/services/authService";
import { SignInFormValues, SignInResponse } from "@/types/auth";

export interface AuthContextType {
  isAuthenticated: boolean;
  user: {
    id: string | null;
    name: string | null;
    email: string | null;
    role: string | null;
    permissions: string[] | [];
  } | null;
  loading: boolean;
  logout: () => void;
  setUser: (
    user: {
      id: string | null;
      name: string | null;
      email: string | null;
      role: string | null;
      permissions: string[] | [];
    } | null
  ) => void;
  login: (credentials: SignInFormValues) => Promise<SignInResponse>;
  access: ReturnType<typeof access> | null;
}

export const AuthContext = createContext<AuthContextType | undefined>(
  undefined
);

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [user, setUser] = useState<AuthContextType["user"]>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [accessValue, setAccessValue] = useState<ReturnType<
    typeof access
  > | null>(null);
  const router = useRouter();

  useEffect(() => {
    const checkAuth = () => {
      const isAuth = utils.auth.isAuthenticated();
      setIsAuthenticated(isAuth);

      if (isAuth) {
        const user = utils.auth.getUserInfo();
        setUser(user);
      } else {
        setUser(null);
      }

      setLoading(false);
    };

    checkAuth();

    // Listen to storage events for multi-tab synchronization
    const handleStorageChange = () => {
      checkAuth();
    };

    window.addEventListener("storage", handleStorageChange);
    return () => window.removeEventListener("storage", handleStorageChange);
  }, []);

  useEffect(() => {
    if (user) {
      setAccessValue(access({ currentUser: user }));
    } else {
      setAccessValue(null);
    }
  }, [user]);

  const login = async (
    credentials: SignInFormValues
  ): Promise<SignInResponse> => {
    const res = await AuthService.signin(credentials);
    utils.auth.storeAuthData(res as SignInResponse);
    const mappedUser = res.user
      ? {
          id: res.user.id?.toString() ?? null,
          name: res.user.name ?? null,
          email: res.user.email ?? null,
          role: res.user.role ?? null,
          permissions: res.user.permissions ?? [],
        }
      : null;
    setUser(mappedUser);
    setIsAuthenticated(true);
    return res;
  };

  const logout = () => {
    utils.auth.clearAuthData();
    setIsAuthenticated(false);
    setUser(null);
    localStorage.removeItem("user");
    router.push("/signin");
  };

  return (
    <AuthContext.Provider
      value={{
        isAuthenticated,
        user,
        loading,
        logout,
        setUser,
        login,
        access: accessValue,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
