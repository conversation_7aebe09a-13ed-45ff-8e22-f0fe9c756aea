import { TABLE_DATE, TABLE_DATE_SHORT } from "@/constants/dates";
import dayjs from "dayjs";
import { SignInResponse } from "@/types/auth";
import { LOCAL_STORAGE_KEYS } from "@/constants/auth";
import { refreshToken as doRefreshToken } from "@/services/ApiService";

// --- Table ---
function formatTableDate(date: string) {
  return dayjs(date).format(TABLE_DATE);
}

function formatBirthDate(date: string) {
  return dayjs(date).format(TABLE_DATE_SHORT);
}

// --- Auth ---
function storeAuthData(authData: SignInResponse) {
  localStorage.setItem(LOCAL_STORAGE_KEYS.ACCESS_TOKEN, authData.accessToken);
  localStorage.setItem(LOCAL_STORAGE_KEYS.REFRESH_TOKEN, authData.refreshToken);
  localStorage.setItem(LOCAL_STORAGE_KEYS.TOKEN_TYPE, authData.tokenType);

  if (authData.user) {
    localStorage.setItem(
      LOCAL_STORAGE_KEYS.USER,
      JSON.stringify(authData.user)
    );
  }
}

function getAccessToken() {
  return localStorage.getItem(LOCAL_STORAGE_KEYS.ACCESS_TOKEN);
}

function getRefreshToken() {
  return localStorage.getItem(LOCAL_STORAGE_KEYS.REFRESH_TOKEN);
}

function getAuthHeader() {
  const token = getAccessToken();
  const tokenType = localStorage.getItem(LOCAL_STORAGE_KEYS.TOKEN_TYPE);
  return token ? `${tokenType} ${token}` : "";
}

function getUserId() {
  const userStr = localStorage.getItem(LOCAL_STORAGE_KEYS.USER);
  if (!userStr) return null;

  try {
    const user = JSON.parse(userStr);
    return user.id;
  } catch (error) {
    console.error("Error parsing user data:", error);
    return null;
  }
}

function getUserInfo() {
  const userStr = localStorage.getItem(LOCAL_STORAGE_KEYS.USER);
  if (!userStr) return null;

  try {
    return JSON.parse(userStr);
  } catch (error) {
    console.error("Error parsing user data:", error);
    return null;
  }
}

function validateToken(token: string | null) {
  if (!token) return false;
  try {
    const payload = JSON.parse(atob(token.split(".")[1]));
    if (payload.exp && Date.now() / 1000 > payload.exp) {
      return false;
    }
    return true;
  } catch {
    return false;
  }
}

function isAuthenticated() {
  const token = getAccessToken();
  const refresh = getRefreshToken();
  const tokenValid = validateToken(token);

  if (tokenValid) return true;

  // If token expried, then check the refresh token.
  const refreshValid = validateToken(refresh);

  if (refreshValid) {
    doRefreshToken();
    return true;
  }

  // Both token and refresh token are invalid
  clearAuthData();
  return false;
}

function clearAuthData() {
  localStorage.removeItem(LOCAL_STORAGE_KEYS.ACCESS_TOKEN);
  localStorage.removeItem(LOCAL_STORAGE_KEYS.REFRESH_TOKEN);
  localStorage.removeItem(LOCAL_STORAGE_KEYS.TOKEN_TYPE);
  localStorage.removeItem(LOCAL_STORAGE_KEYS.USER);
}

// --- Form ---
function filterOption(input: string, option: any) {
  return (
    option?.label?.toString().toLowerCase().includes(input.toLowerCase()) ??
    false
  );
}

// Format address from UserAddress object to string
const formatAddress = (address: any): string => {
  if (!address) return "N/A";

  const parts = [];
  if (address.detailAddress) parts.push(address.detailAddress);
  if (address.wardName) parts.push(address.wardName);
  if (address.districtName) parts.push(address.districtName);
  if (address.provinceName) parts.push(address.provinceName);

  return parts.length > 0 ? parts.join(", ") : "N/A";
};

// --- Export ---
export const utils = {
  formatTableDate,
  formatBirthDate,
  formatAddress,

  auth: {
    storeAuthData,
    getAccessToken,
    getRefreshToken,
    getAuthHeader,
    getUserId,
    getUserInfo,
    isAuthenticated,
    clearAuthData,
  },

  form: {
    filterOption,
  },
};
