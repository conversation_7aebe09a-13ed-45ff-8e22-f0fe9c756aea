import type { JobSeekerPost } from "@/types/post";
import type { Resume } from "@/types/resume";

/**
 * Adapter to convert JobSeekerPost to ProfileView props
 * Note: JobSeekerPost has simple string[] languages, not {language, level}[]
 */
export const jobSeekerPostToProfileView = (post: JobSeekerPost) => {
  return {
    basicInfo: {
      name: post.title,
      isActive: post.status === "active",
      description: post.description,
    },
    contactInfo: undefined, // JobSeekerPost doesn't have contact info
    skills: post.skills,
    // Convert simple string[] to {language, level}[] format for ProfileView
    languages: post.languages?.map((lang) => ({
      language: lang,
      level: "Not specified", // JobSeekerPost doesn't have levels
    })),
    workExperiences: post.experiences?.map((exp) => ({
      position: exp.industry,
      company: `${exp.yearOfExperience} years experience`,
      startDate: "", // JobSeekerPost doesn't have start/end dates
      description: `${exp.yearOfExperience} years of experience in ${exp.industry}`,
    })),
    educations:
      post.educationLevel || post.educationDetail
        ? [
            {
              degree: post.educationLevel || "Not specified",
              institution: post.educationDetail || "Not specified",
              startDate: "", // JobSeekerPost doesn't have education dates
              description: post.educationDetail,
            },
          ]
        : undefined,
    partTimePreference: {
      minHourlyRate: post.salary?.min,
      maxHoursPerWeek: post.workingHourPerDay
        ? post.workingHourPerDay * post.workingDays?.length
        : undefined,
      availableDays: post.workingDays,
      availableTimeSlots: post.workingShifts,
      preferredJobTypes: [
        post.jobType,
        post.contractType,
        post.workType,
      ].filter(Boolean),
      remoteOnly: post.workType === "remote",
      maxTravelDistance: undefined, // JobSeekerPost doesn't have this
      isStudent: undefined, // JobSeekerPost doesn't have this
      studyMajor: undefined, // JobSeekerPost doesn't have this
      additionalNotes: undefined, // JobSeekerPost doesn't have this
    },
  };
};

/**
 * Adapter to convert Resume to ProfileView props
 */
export const resumeToProfileView = (resume: Resume) => {
  return {
    basicInfo: {
      name: resume.name,
      isActive: resume.isActive,
      description: resume.description,
    },
    contactInfo: resume.contactInfo,
    skills: resume.skills,
    languages: resume.languages,
    workExperiences: resume.workExperiences,
    educations: resume.educations,
    partTimePreference: resume.partTimePreference,
  };
};

/**
 * Generic adapter for any object that has basic profile information
 */
export const genericToProfileView = (data: {
  name?: string;
  title?: string;
  isActive?: boolean;
  status?: string;
  description?: string;
  contactInfo?: any;
  skills?: string[];
  languages?: Array<{ language: string; level: string }>;
  workExperiences?: any[];
  experiences?: any[];
  educations?: any[];
  educationLevel?: string;
  educationDetail?: string;
  partTimePreference?: any;
  [key: string]: any;
}) => {
  return {
    basicInfo: {
      name: data.name || data.title || "Unknown",
      isActive: data.isActive ?? data.status === "active",
      description: data.description,
    },
    contactInfo: data.contactInfo,
    skills: data.skills,
    languages: data.languages,
    workExperiences:
      data.workExperiences ||
      data.experiences?.map((exp: any) => ({
        position: exp.position || exp.industry || exp.title,
        company: exp.company || `${exp.yearOfExperience || 0} years experience`,
        startDate: exp.startDate || "",
        endDate: exp.endDate,
        description:
          exp.description || `Experience in ${exp.industry || exp.position}`,
      })),
    educations:
      data.educations ||
      (data.educationLevel || data.educationDetail
        ? [
            {
              degree: data.educationLevel || "Not specified",
              institution: data.educationDetail || "Not specified",
              startDate: "",
              description: data.educationDetail,
            },
          ]
        : undefined),
    partTimePreference: data.partTimePreference,
  };
};
