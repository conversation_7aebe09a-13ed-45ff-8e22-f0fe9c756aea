"use client";

import { routes } from "@/routes";
import { Breadcrumb } from "antd";
import { useTranslations } from "next-intl";
import Link from "next/link";
import { usePathname } from "next/navigation";
import React from "react";

const MainBreadcrumb = () => {
  const pathname = usePathname();
  const t = useTranslations("route");

  // Helper function to check if a segment looks like an ID or parameter
  const isIdOrParam = (segment: string) => {
    // Check if it's a number, UUID pattern, or other common ID patterns
    return (
      /^(\d+|[a-f0-9-]{36}|route\.\d+)$/i.test(segment) ||
      segment.includes(".") ||
      segment.length > 20
    ); // Assume very long segments are IDs
  };

  // Helper function to find route info from path, with fallback for dynamic routes
  const findRouteInfo = (
    path: string,
    originalSegment?: string
  ): {
    label: string;
    path: string;
    parents: { key: string; label: string }[];
    isDynamic?: boolean;
  } | null => {
    // Recursive function to search through nested routes
    const searchRoutes = (
      routesObj: any,
      currentParents: { key: string; label: string }[] = []
    ): {
      label: string;
      path: string;
      parents: { key: string; label: string }[];
      isDynamic?: boolean;
    } | null => {
      for (const key in routesObj) {
        const route = routesObj[key];

        // Check if this route has the exact path
        if ("path" in route && route.path === path) {
          return {
            label: t(route.label),
            path: route.path,
            parents: currentParents,
          };
        }

        // Check nested routes recursively
        if ("children" in route && route.children) {
          const newParents = route.key
            ? [...currentParents, { key: route.key, label: t(route.label) }]
            : currentParents;

          const result = searchRoutes(route.children, newParents);
          if (result) {
            return result;
          }
        }
      }
      return null;
    };

    // First try recursive search
    const result = searchRoutes(routes);
    if (result) {
      return result;
    }

    // If no exact match found and this looks like a dynamic route,
    // try to find the parent route by removing the last segment
    if (originalSegment && isIdOrParam(originalSegment)) {
      const parentPath = path.substring(0, path.lastIndexOf("/"));
      if (parentPath) {
        const parentInfo = findRouteInfo(parentPath);
        if (parentInfo) {
          return {
            label: originalSegment, // Use the ID/param as label
            path: path,
            parents: [
              ...parentInfo.parents,
              { key: parentInfo.path, label: parentInfo.label },
            ],
            isDynamic: true,
          };
        }
      }
    }

    return null;
  };

  // Generate breadcrumb items based on current path
  const generateBreadcrumbItems = () => {
    const items = [{ title: <Link href="/">{t("home")}</Link> }];

    if (pathname === "/") return items;

    // Set to track labels we've already added
    const addedLabels = new Set([t("home")]);

    // Build path segments
    const segments = pathname.split("/").filter(Boolean);
    let currentPath = "";

    // Process segments
    segments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      const routeInfo = findRouteInfo(currentPath, segment);
      const isLast = index === segments.length - 1;

      if (routeInfo) {
        // Add parent routes if they exist and haven't been added
        routeInfo.parents.forEach((parent) => {
          if (!addedLabels.has(parent.label)) {
            // Find the parent route to get its path
            const findParentPath = (
              searchRoutes: any,
              targetKey: string
            ): string => {
              for (const key in searchRoutes) {
                const route = searchRoutes[key];
                if (route.key === targetKey) {
                  return "path" in route ? route.path : "#";
                }
                if ("children" in route && route.children) {
                  const found = findParentPath(route.children, targetKey);
                  if (found !== "#") return found;
                }
              }
              return "#";
            };

            const parentHref = findParentPath(routes, parent.key);
            items.push({
              title: <Link href={parentHref}>{parent.label}</Link>,
            });
            addedLabels.add(parent.label);
          }
        });

        // Only add current route if we haven't already added this label
        if (!addedLabels.has(routeInfo.label)) {
          items.push({
            title: isLast ? (
              <>{routeInfo.label}</>
            ) : (
              <Link href={routeInfo.path}>{routeInfo.label}</Link>
            ),
          });
          addedLabels.add(routeInfo.label);
        }
      } else {
        // If it looks like an ID/parameter, don't try to translate it
        if (isIdOrParam(segment)) {
          if (!addedLabels.has(segment)) {
            items.push({ title: <>{segment}</> });
            addedLabels.add(segment);
          }
        } else {
          // Format segment - replace hyphens with underscores for translation
          const formattedSegment = segment?.replace(/-/g, "_");
          const translatedLabel = t(formattedSegment);

          if (!addedLabels.has(translatedLabel)) {
            items.push({ title: <>{translatedLabel}</> });
            addedLabels.add(translatedLabel);
          }
        }
      }
    });

    return items;
  };

  return (
    <Breadcrumb
      items={generateBreadcrumbItems()}
      style={{ margin: "16px 5px" }}
    />
  );
};

export default MainBreadcrumb;
