/* eslint-disable react-hooks/exhaustive-deps */
// components/features/address/AddressDisplay.tsx
"use client";
import { ApiResponseAddress } from "@/types/address";
import {
  CheckOutlined,
  CopyOutlined,
  EnvironmentOutlined,
} from "@ant-design/icons";
import { Skeleton, Space, Typography } from "antd";
import { useTranslations } from "next-intl";
import React, { useEffect, useState } from "react";

const { Text, Paragraph } = Typography;

interface AddressDisplayProps {
  addressData: ApiResponseAddress;
  showLabels?: boolean;
  showIcon?: boolean;
  showCopyButton?: boolean;
  layout?: "horizontal" | "vertical" | "inline";
  className?: string;
  loading?: boolean;
  emptyText?: string;
  maxWidth?: number;
  truncate?: boolean;
}

const AddressDisplay: React.FC<AddressDisplayProps> = ({
  addressData,
  showLabels = false,
  showIcon = true,
  showCopyButton = false,
  layout = "inline",
  className = "",
  loading = false,
  emptyText,
  maxWidth,
  truncate = false,
}) => {
  const t = useTranslations("address");

  const [addressNames, setAddressNames] = useState<{
    provinceName?: string;
    districtName?: string;
    wardName?: string;
  }>({});
  const [isLoadingNames, setIsLoadingNames] = useState(false);
  const [copied, setCopied] = useState(false);

  // Load address names from codes
  useEffect(() => {
    const loadAddressNames = async () => {
      if (
        !addressData.province.code &&
        !addressData.district.code &&
        !addressData.ward.code
      ) {
        setAddressNames({});
        return;
      }

      setIsLoadingNames(true);
      try {
        setAddressNames({
          provinceName: addressData.province.name,
          districtName: addressData.district.name,
          wardName: addressData.ward.name,
        });
      } catch (error) {
        console.error("Error loading address names:", error);
      } finally {
        setIsLoadingNames(false);
      }
    };

    loadAddressNames();
  }, [
    addressData.province.code,
    addressData.district.code,
    addressData.ward.code,
  ]);

  // Format full address
  const formatFullAddress = (): string => {
    const parts = [];

    if (addressData.detailAddress?.trim()) {
      parts.push(addressData.detailAddress.trim());
    }

    if (addressNames.wardName) {
      parts.push(addressNames.wardName);
    }

    if (addressNames.districtName) {
      parts.push(addressNames.districtName);
    }

    if (addressNames.provinceName) {
      parts.push(addressNames.provinceName);
    }

    return parts.join(", ");
  };

  // Handle copy to clipboard
  const handleCopy = async () => {
    const fullAddress = formatFullAddress();
    if (!fullAddress) return;

    try {
      await navigator.clipboard.writeText(fullAddress);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error("Failed to copy address:", error);
    }
  };

  // Check if address is empty
  const isEmpty =
    !addressData.province.code &&
    !addressData.district.code &&
    !addressData.ward.code &&
    !addressData.detailAddress?.trim();

  if (loading || isLoadingNames) {
    return (
      <div className={className}>
        <Skeleton.Input
          active
          size="small"
          style={{ width: maxWidth || 300 }}
        />
      </div>
    );
  }

  if (isEmpty) {
    return (
      <Text type="secondary" className={className}>
        {emptyText || t("address_not_provided")}
      </Text>
    );
  }

  const fullAddress = formatFullAddress();

  // Render based on layout
  if (layout === "vertical" && showLabels) {
    return (
      <div className={className} style={{ maxWidth }}>
        <Space direction="vertical" size="small" style={{ width: "100%" }}>
          {addressData.detailAddress && (
            <div>
              <Text strong>Địa chỉ chi tiết: </Text>
              <Text>{addressData.detailAddress}</Text>
            </div>
          )}
          {addressNames.wardName && (
            <div>
              <Text strong>Phường/Xã: </Text>
              <Text>{addressNames.wardName}</Text>
            </div>
          )}
          {addressNames.districtName && (
            <div>
              <Text strong>Quận/Huyện: </Text>
              <Text>{addressNames.districtName}</Text>
            </div>
          )}
          {addressNames.provinceName && (
            <div>
              <Text strong>Tỉnh/Thành phố: </Text>
              <Text>{addressNames.provinceName}</Text>
            </div>
          )}
        </Space>
      </div>
    );
  }

  if (layout === "horizontal") {
    return (
      <div className={className} style={{ maxWidth }}>
        <Space wrap>
          {showIcon && <EnvironmentOutlined />}
          <Paragraph
            ellipsis={truncate ? { rows: 1, tooltip: fullAddress } : false}
            copyable={showCopyButton ? { text: fullAddress } : false}
            style={{ margin: 0 }}
          >
            {fullAddress}
          </Paragraph>
        </Space>
      </div>
    );
  }

  // Default inline layout
  return (
    <Space className={className} style={{ maxWidth }}>
      {showIcon && <EnvironmentOutlined />}
      <Text ellipsis={truncate ? { tooltip: fullAddress } : false}>
        {fullAddress}
      </Text>
      {showCopyButton && (
        <Text
          style={{ cursor: "pointer", color: copied ? "#52c41a" : "#1890ff" }}
          onClick={handleCopy}
        >
          {copied ? <CheckOutlined /> : <CopyOutlined />}
        </Text>
      )}
    </Space>
  );
};

export default AddressDisplay;
