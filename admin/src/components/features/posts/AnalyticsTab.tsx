"use client";
import type { Post } from "@/types/post";
import { Al<PERSON>, Card, Descriptions, Divider } from "antd";
import React from "react";

interface AnalyticsTabProps {
  post: Post;
}

const AnalyticsTab: React.FC<AnalyticsTabProps> = ({ post }) => {
  return (
    <Card>
      <Descriptions bordered>
        <Descriptions.Item label="Total Views">
          {post.viewCount}
        </Descriptions.Item>
        <Descriptions.Item label="Total Applications">
          {post.applicationCount}
        </Descriptions.Item>
        <Descriptions.Item label="New Applications">
          {post.newApplications}
        </Descriptions.Item>
        <Descriptions.Item label="Application Rate">
          {post.viewCount > 0
            ? `${((post.applicationCount / post.viewCount) * 100).toFixed(1)}%`
            : "0%"}
        </Descriptions.Item>
      </Descriptions>

      {/* In a real application, you might include graphs or charts here */}
      <Divider />
      <Alert
        message="Analytics visualization"
        description="In a complete implementation, this section would include detailed charts showing view trends, application rates, and other analytics."
        type="info"
      />
    </Card>
  );
};

export default AnalyticsTab;
