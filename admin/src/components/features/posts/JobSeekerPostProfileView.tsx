import React from "react";
import { Row, Col } from "antd";
import {
  BasicInfo,
  Skills,
  SimpleLanguages,
  WorkExperience,
  Education,
  PartTimePreferences,
} from "@/components/features/users/job-seeker-resumes/components";
import type { JobSeekerPost } from "@/types/post";

interface JobSeekerPostProfileViewProps {
  post: JobSeekerPost;
  showHeader?: boolean;
}

const JobSeekerPostProfileView: React.FC<JobSeekerPostProfileViewProps> = ({
  post,
  showHeader = true,
}) => {
  // Convert JobSeekerPost to format expected by components
  const basicInfo = {
    name: post.title,
    isActive: post.status === "active",
    description: post.description,
  };

  const workExperiences = post.experiences?.map(exp => ({
    position: exp.industry,
    company: `${exp.yearOfExperience} years experience`,
    startDate: "",
    description: `${exp.yearOfExperience} years of experience in ${exp.industry}`,
  }));

  const educations = (post.educationLevel || post.educationDetail) ? [{
    degree: post.educationLevel || "Not specified",
    institution: post.educationDetail || "Not specified", 
    startDate: "",
    description: post.educationDetail,
  }] : undefined;

  const partTimePreference = {
    minHourlyRate: post.salary?.min,
    maxHoursPerWeek: post.workingHourPerDay ? post.workingHourPerDay * post.workingDays?.length : undefined,
    availableDays: post.workingDays,
    availableTimeSlots: post.workingShifts,
    preferredJobTypes: [post.jobType, post.contractType, post.workType].filter(Boolean),
    remoteOnly: post.workType === "remote",
  };

  return (
    <div className="space-y-6">
      {showHeader && <BasicInfo basicInfo={basicInfo} />}
      
      <Row gutter={[24, 24]}>
        <Col xs={24} lg={12}>
          <Skills skills={post.skills} />
        </Col>
        <Col xs={24} lg={12}>
          <SimpleLanguages languages={post.languages} />
        </Col>
      </Row>

      <Row gutter={[24, 24]}>
        <Col xs={24} lg={12}>
          <WorkExperience workExperiences={workExperiences} />
        </Col>
        <Col xs={24} lg={12}>
          <Education educations={educations} />
        </Col>
      </Row>

      <PartTimePreferences partTimePreference={partTimePreference} />
    </div>
  );
};

export default JobSeekerPostProfileView;
