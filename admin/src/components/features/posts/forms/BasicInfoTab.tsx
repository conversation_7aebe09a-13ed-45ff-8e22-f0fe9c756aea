"use client";
import { Card, Flex, Form, FormInstance } from "antd";
import React, { useEffect, useState } from "react";
import { useTranslations } from "next-intl";
import BaseInput from "@/components/ui/inputs/BaseInput";
import BaseTextArea from "@/components/ui/inputs/BaseTextArea";
import DebounceSelect from "@/components/ui/selects/DebounceSelect";
import CompanyService from "@/services/companyService";
import BranchService from "@/services/branchService";
import AddressFormField from "@/components/features/address/AddressFormField";

interface BasicInfoTabProps {
  isEdit?: boolean;
  form?: FormInstance;
  addressDisabled?: boolean;
}

const BasicInfoTab: React.FC<BasicInfoTabProps> = ({
  isEdit = false,
  form,
  addressDisabled = false,
}) => {
  const formInstance = form || Form.useFormInstance();
  const tPosts = useTranslations("posts");
  const tCommon = useTranslations("common");

  const [companyOptions, setCompanyOptions] = useState<any[]>([]);
  const [branchOptions, setBranchOptions] = useState<any[]>([]);

  useEffect(() => {
    const companyId = formInstance.getFieldValue("companyId");
    if (companyId && typeof companyId === "number") {
      CompanyService.getList({ page: 0, limit: 20000 }).then((res: any) => {
        const found = res.data.find((c: any) => c.id === companyId);
        if (found) {
          const option = { value: found.id, label: found.name };
          setCompanyOptions([option]);
          formInstance.setFieldValue("companyId", option);
        }
      });
    }
  }, [formInstance]);

  useEffect(() => {
    const branchId = formInstance.getFieldValue("branchId");
    const companyId =
      formInstance.getFieldValue("companyId")?.value ||
      formInstance.getFieldValue("companyId");
    if (branchId && typeof branchId === "number" && companyId) {
      BranchService.getList({ companyId, page: 0, limit: 20000 }).then(
        (res: any) => {
          const found = res.data.find((b: any) => b.id === branchId);
          if (found) {
            const option = { value: found.id, label: found.name };
            setBranchOptions([option]);
            formInstance.setFieldValue("branchId", option);
          }
        }
      );
    }
  }, [formInstance, formInstance.getFieldValue("companyId")]);

  return (
    <Card title={tPosts("basic_information")} className="mb-6">
      <BaseInput
        name="title"
        label={tPosts("job_title")}
        required
        placeholder={tPosts("placeholders.job_title")}
        rules={[
          {
            required: true,
            message: tPosts("validation.please_enter_job_title"),
          },
        ]}
      />

      <Flex gap="middle">
        <DebounceSelect
          label={tCommon("fields.company")}
          name="companyId"
          fetchOptions={(search) =>
            CompanyService.getList({
              keyword: search,
              page: 0,
              limit: 20000,
            })
          }
          required
          options={companyOptions}
        />

        <DebounceSelect
          label={tCommon("fields.branch")}
          name="branchId"
          fetchOptions={(search) =>
            BranchService.getList({
              keyword: search,
              page: 0,
              limit: 20000,
              companyId:
                formInstance.getFieldValue("companyId")?.value ||
                formInstance.getFieldValue("companyId"),
            })
          }
          required
          options={branchOptions}
        />
      </Flex>

      <AddressFormField
        name="address"
        required
        className="mt-4"
        disabled={addressDisabled}
      />

      <BaseTextArea
        name="description"
        label={tPosts("job_description")}
        required
        placeholder={tPosts("placeholders.job_description")}
        rows={6}
        rules={[
          {
            required: true,
            message: tPosts("validation.please_enter_job_description"),
          },
        ]}
      />
    </Card>
  );
};

export default BasicInfoTab;
