"use client";

import { <PERSON><PERSON>, <PERSON>, Button } from "antd";
import DebounceSelect from "@/components/ui/selects/DebounceSelect";
import { useState, useCallback, useEffect } from "react";
import UserService from "@/services/userService";
import * as ResumeService from "@/services/resumeService";
import { UserRole } from "@/constants/userRole";
import PostService from "@/services/postService";
import ResumeView from "../users/job-seeker-resumes/ResumeView";

export enum ApplicationModalMode {
  CREATE = "create",
  EDIT = "edit",
  VIEW = "view",
}

const CreateApplicationModal = ({
  open,
  onCancel,
  onSuccess,
  postId,
  mode = ApplicationModalMode.CREATE,
  userId,
  resumeId,
}: any) => {
  const [form] = Form.useForm();
  const [selectedUserId, setSelectedUserId] = useState<any>(null);
  const [selectedResume, setSelectedResume] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  // Load user/resume if in view or edit mode
  useEffect(() => {
    if (
      (mode === ApplicationModalMode.VIEW ||
        mode === ApplicationModalMode.EDIT) &&
      userId
    ) {
      setSelectedUserId(userId);
      form.setFieldsValue({ user: userId });
    }
    if (
      (mode === ApplicationModalMode.VIEW ||
        mode === ApplicationModalMode.EDIT) &&
      resumeId
    ) {
      form.setFieldsValue({ resume: resumeId });
      ResumeService.getResumeById(resumeId).then((resume) =>
        setSelectedResume(resume)
      );
    }
  }, [mode, userId, resumeId, form]);

  const fetchUserOptions = async (search: string) => {
    const res = await UserService.getList({
      role: UserRole.JOB_SEEKER,
      search,
    });
    return { data: res.data };
  };

  const fetchResumeOptions = useCallback(async () => {
    if (!selectedUserId) return { data: [] };
    const res = await ResumeService.getUserResumes(selectedUserId);
    return { data: res.data };
  }, [selectedUserId]);

  const handleCreateApplication = async () => {
    if (mode === ApplicationModalMode.VIEW) return;
    setLoading(true);
    const values = await form.validateFields();
    const res = await PostService.createApplication({
      postId: postId,
      resumeId: values.resume,
    });
    onSuccess(res);
    form.resetFields();
    setSelectedUserId(null);
    setSelectedResume(null);
    setLoading(false);
  };

  const handleCancel = () => {
    form.resetFields();
    setSelectedUserId(null);
    setSelectedResume(null);
    onCancel();
  };

  return (
    <Modal
      open={open}
      onCancel={handleCancel}
      title={
        mode === ApplicationModalMode.VIEW
          ? "View Application"
          : mode === ApplicationModalMode.EDIT
          ? "Edit Application"
          : "Create Application"
      }
      width={1500}
      onOk={handleCreateApplication}
      okButtonProps={{
        disabled: mode === ApplicationModalMode.VIEW,
        loading,
        style: {
          display: mode === ApplicationModalMode.VIEW ? "none" : undefined,
        },
      }}
      cancelButtonProps={{
        style: {
          display: mode === ApplicationModalMode.VIEW ? "none" : undefined,
        },
      }}
    >
      <Form form={form} layout="vertical">
        <div
          style={{
            display: "flex",
            gap: 16,
            width: "100%",
            alignItems: "flex-end",
          }}
        >
          <Form.Item
            name="user"
            label="User"
            rules={[{ required: true, message: "User is required" }]}
            style={{ flex: 1, marginBottom: 0 }}
          >
            <DebounceSelect
              placeholder="Search user"
              fetchOptions={fetchUserOptions}
              onChange={(val) => {
                setSelectedUserId(val);
                form.setFieldsValue({ resume: undefined });
                setSelectedResume(null);
              }}
              disabled={
                mode === ApplicationModalMode.VIEW ||
                mode === ApplicationModalMode.EDIT
              }
              value={selectedUserId}
            />
          </Form.Item>
          <Form.Item
            name="resume"
            label="Resume"
            rules={[{ required: true, message: "Resume is required" }]}
            style={{ flex: 1, marginBottom: 0 }}
          >
            <DebounceSelect
              placeholder="Select resume"
              fetchOptions={fetchResumeOptions}
              onChange={async (val) => {
                const resume = await ResumeService.getResumeById(val);
                setSelectedResume(resume);
              }}
              disabled={!selectedUserId || mode === ApplicationModalMode.VIEW}
              key={selectedUserId || "resume"}
              value={form.getFieldValue("resume")}
            />
          </Form.Item>
        </div>
      </Form>
      {selectedResume && <ResumeView resume={selectedResume} />}
    </Modal>
  );
};

export default CreateApplicationModal;
