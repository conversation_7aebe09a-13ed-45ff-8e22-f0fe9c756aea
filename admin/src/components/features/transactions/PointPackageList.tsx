"use client";
// app/admin/transactions/components/PointPackageList.tsx
import BaseTable from "@/components/ui/tables/BaseTable";
import TransactionService from "@/services/transactionService";
import { PointPackage } from "@/types/transaction";
import {
  Button,
  Form,
  Input,
  InputNumber,
  Modal,
  notification,
  Switch,
  Tag,
} from "antd";
import dayjs from "dayjs";
import { useState } from "react";

type FormValues = Omit<PointPackage, "id" | "createdAt">;

const PointPackageList = () => {
  const [form] = Form.useForm<FormValues>();
  const [modalVisible, setModalVisible] = useState(false);
  const [editingPackage, setEditingPackage] = useState<PointPackage | null>(
    null
  );
  const [loading, setLoading] = useState(false);

  const columns = [
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      render: (text: string, record: PointPackage) => (
        <div>
          {text} {record.isPopular && <Tag color="gold">Popular</Tag>}
        </div>
      ),
    },
    {
      title: "Points",
      dataIndex: "points",
      key: "points",
      render: (points: number) => points.toLocaleString(),
    },
    {
      title: "Price ($)",
      dataIndex: "price",
      key: "price",
      render: (price: number) => `$${price.toLocaleString()}`,
    },
    {
      title: "Value",
      key: "value",
      render: (_: unknown, record: PointPackage) => {
        const valuePerPoint = record.price / record.points;
        return `$${valuePerPoint.toFixed(4)} per point`;
      },
    },
    {
      title: "Created",
      dataIndex: "createdAt",
      key: "createdAt",
      render: (text: string) => dayjs(text).format("MMM DD, YYYY"),
    },
  ];

  const handleCreateOrUpdate = async (values: FormValues) => {
    setLoading(true);

    try {
      if (editingPackage) {
        await TransactionService.updatePointPackage(editingPackage.id, values);
        notification.success({
          message: "Point package updated",
          description: `The point package "${values.name}" has been updated successfully.`,
        });
      } else {
        await TransactionService.createPointPackage(values);
        notification.success({
          message: "Point package created",
          description: `The point package "${values.name}" has been created successfully.`,
        });
      }

      setModalVisible(false);
      form.resetFields();
      setEditingPackage(null);
    } catch (error) {
      console.log("🚀 ~ handleCreateOrUpdate ~ error:", error);
      notification.error({
        message: "Operation failed",
        description: "Failed to save the point package. Please try again.",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (record: PointPackage) => {
    setEditingPackage(record);
    form.setFieldsValue({
      name: record.name,
      points: record.points,
      price: record.price,
      description: record.description,
      isPopular: record.isPopular,
    });
    setModalVisible(true);
  };

  const handleDelete = async (record: PointPackage) => {
    try {
      await TransactionService.deletePointPackage(record.id);
      notification.success({
        message: "Point package deleted",
        description: `The point package "${record.name}" has been deleted successfully.`,
      });
    } catch (error) {
      console.log("🚀 ~ handleDelete ~ error:", error);
      notification.error({
        message: "Delete failed",
        description: "Failed to delete the point package. Please try again.",
      });
    }
  };

  const handleCreate = () => {
    form.resetFields();
    setEditingPackage(null);
    setModalVisible(true);
  };

  return (
    <>
      <BaseTable<PointPackage>
        api={TransactionService.getPointPackages}
        columns={columns}
        rowKey="id"
        title="Point Packages"
        createBtnText="Create Package"
        onCreate={handleCreate}
        onEdit={handleEdit}
        onDelete={handleDelete}
        showSearch={true}
        searchPlaceholder="Search by package name..."
      />

      {/* Create/Edit Modal */}
      <Modal
        title={editingPackage ? "Edit Point Package" : "Create Point Package"}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setModalVisible(false)}>
            Cancel
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={loading}
            onClick={() => form.submit()}
          >
            {editingPackage ? "Update" : "Create"}
          </Button>,
        ]}
        width={500}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreateOrUpdate}
          initialValues={{
            isPopular: false,
          }}
        >
          <Form.Item
            name="name"
            label="Package Name"
            rules={[
              { required: true, message: "Please enter the package name" },
            ]}
          >
            <Input placeholder="e.g. Basic Package, Premium Package" />
          </Form.Item>

          <Form.Item
            name="points"
            label="Points"
            rules={[
              { required: true, message: "Please enter the number of points" },
            ]}
          >
            <InputNumber
              min={1}
              style={{ width: "100%" }}
              placeholder="e.g. 100, 500, 1000"
            />
          </Form.Item>

          <Form.Item
            name="price"
            label="Price ($)"
            rules={[{ required: true, message: "Please enter the price" }]}
          >
            <InputNumber
              min={0.01}
              step={0.01}
              precision={2}
              style={{ width: "100%" }}
              placeholder="e.g. 9.99, 19.99, 49.99"
            />
          </Form.Item>

          <Form.Item name="description" label="Description">
            <Input.TextArea
              rows={3}
              placeholder="Enter a description for this package (optional)"
            />
          </Form.Item>

          <Form.Item
            name="isPopular"
            label="Mark as Popular"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default PointPackageList;
