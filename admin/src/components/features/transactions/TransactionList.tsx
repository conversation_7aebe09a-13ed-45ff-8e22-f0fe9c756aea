"use client";
// app/admin/transactions/components/TransactionList.tsx
import BaseTable from "@/components/ui/tables/BaseTable";
import { FilterConfig } from "@/components/ui/tables/TableFilter";
import TransactionService from "@/services/transactionService";
import { Transaction } from "@/types/transaction";
import { Modal, Tag, Typography } from "antd";
import dayjs from "dayjs";
import { useState } from "react";

const { Text } = Typography;

const TransactionList = () => {
  const [selectedTransaction, setSelectedTransaction] =
    useState<Transaction | null>(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);

  const getStatusTag = (status: string) => {
    const statusMap: Record<string, { color: string; text: string }> = {
      completed: { color: "success", text: "Completed" },
      pending: { color: "processing", text: "Pending" },
      failed: { color: "error", text: "Failed" },
      refunded: { color: "warning", text: "Refunded" },
    };

    const { color, text } = statusMap[status] || {
      color: "default",
      text: status,
    };
    return <Tag color={color}>{text}</Tag>;
  };

  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      width: 80,
    },
    {
      title: "User",
      dataIndex: "userName",
      key: "userName",
      render: (text: string, record: Transaction) => (
        <span>
          {text} <Text type="secondary">({record.userId})</Text>
        </span>
      ),
    },
    {
      title: "Date",
      dataIndex: "timestamp",
      key: "timestamp",
      render: (text: string) => dayjs(text).format("MMM DD, YYYY HH:mm"),
      sorter: true,
    },
    {
      title: "Points",
      dataIndex: "points",
      key: "points",
      render: (points: number) => <Text strong>{points.toLocaleString()}</Text>,
      sorter: true,
    },
    {
      title: "Amount",
      dataIndex: "amount",
      key: "amount",
      render: (amount: number) => (
        <Text strong>${amount.toLocaleString()}</Text>
      ),
      sorter: true,
    },
    {
      title: "Payment Method",
      dataIndex: "paymentMethod",
      key: "paymentMethod",
      render: (method: string) => {
        const methodMap: Record<string, string> = {
          credit_card: "Credit Card",
          bank_transfer: "Bank Transfer",
          e_wallet: "E-Wallet",
          other: "Other",
        };
        return methodMap[method] || method;
      },
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
      render: (status: string) => getStatusTag(status),
    },
  ];

  const filters: FilterConfig[] = [
    {
      key: "status",
      label: "Status",
      type: "select",
      options: [
        { value: "completed", label: "Completed" },
        { value: "pending", label: "Pending" },
        { value: "failed", label: "Failed" },
        { value: "refunded", label: "Refunded" },
      ],
    },
    {
      key: "paymentMethod",
      label: "Payment Method",
      type: "select",
      options: [
        { value: "credit_card", label: "Credit Card" },
        { value: "bank_transfer", label: "Bank Transfer" },
        { value: "e_wallet", label: "E-Wallet" },
        { value: "other", label: "Other" },
      ],
    },
    {
      key: "dateRange",
      label: "Transaction Date",
      type: "dateRange",
    },
  ];

  const handleExportCsv = async () => {
    try {
      const blob = await TransactionService.exportTransactions();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `transactions-${new Date().toISOString().split("T")[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      a.remove();
    } catch (error) {
      console.error("Error exporting transactions:", error);
    }
  };

  const handleRowClick = (record: Transaction) => {
    setSelectedTransaction(record);
    setDetailModalVisible(true);
  };

  return (
    <>
      <BaseTable<Transaction>
        api={TransactionService.getTransactions}
        columns={columns}
        rowKey="id"
        title="Transaction History"
        showSearch={true}
        searchPlaceholder="Search by ID or user name..."
        filters={filters}
        showActions={false}
        onRowClick={handleRowClick}
        initialParams={{ sort: "-timestamp" }}
        createBtnText="Export to CSV"
        onCreate={handleExportCsv}
      />

      {/* Transaction Detail Modal */}
      <Modal
        title="Transaction Details"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={600}
      >
        {selectedTransaction && (
          <div className="transaction-detail">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Text type="secondary">Transaction ID</Text>
                <div>
                  <Text copyable>{selectedTransaction.id}</Text>
                </div>
              </div>
              <div>
                <Text type="secondary">Status</Text>
                <div>{getStatusTag(selectedTransaction.status)}</div>
              </div>
              <div>
                <Text type="secondary">User</Text>
                <div>{selectedTransaction.userName}</div>
              </div>
              <div>
                <Text type="secondary">User ID</Text>
                <div>
                  <Text copyable>{selectedTransaction.userId}</Text>
                </div>
              </div>
              <div>
                <Text type="secondary">Date & Time</Text>
                <div>
                  {dayjs(selectedTransaction.timestamp).format(
                    "MMM DD, YYYY HH:mm:ss"
                  )}
                </div>
              </div>
              <div>
                <Text type="secondary">Payment Method</Text>
                <div>
                  {selectedTransaction.paymentMethod === "credit_card" &&
                    "Credit Card"}
                  {selectedTransaction.paymentMethod === "bank_transfer" &&
                    "Bank Transfer"}
                  {selectedTransaction.paymentMethod === "e_wallet" &&
                    "E-Wallet"}
                  {selectedTransaction.paymentMethod === "other" && "Other"}
                </div>
              </div>
              <div>
                <Text type="secondary">Points</Text>
                <div>
                  <Text strong>
                    {selectedTransaction.points.toLocaleString()}
                  </Text>
                </div>
              </div>
              <div>
                <Text type="secondary">Amount</Text>
                <div>
                  <Text strong>
                    ${selectedTransaction.amount.toLocaleString()}
                  </Text>
                </div>
              </div>
            </div>
          </div>
        )}
      </Modal>
    </>
  );
};

export default TransactionList;
