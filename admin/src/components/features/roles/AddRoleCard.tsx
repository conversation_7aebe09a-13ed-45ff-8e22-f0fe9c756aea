"use client";
import BaseCheckbox from "@/components/ui/checkboxes/BaseCheckbox";
import BaseInput from "@/components/ui/inputs/BaseInput";
import BaseTextArea from "@/components/ui/inputs/BaseTextArea";
import BaseModal from "@/components/ui/modals/BaseModal";
import { useNotification } from "@/contexts/NotiContext";
import RoleService from "@/services/roleService";
import { RoleFormData } from "@/types/role";
import { PlusOutlined } from "@ant-design/icons";
import { Card, Form } from "antd";
import { useTranslations } from "next-intl";
import { useState } from "react";

interface IAddRoleCard {
  onSuccess?: () => void;
}

const AddRoleCard: React.FC<IAddRoleCard> = ({ onSuccess }) => {
  const [form] = Form.useForm();
  const t = useTranslations("roles");
  const notification = useNotification();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleCancel = () => {
    form.resetFields();
    setIsModalOpen(false);
  };

  const handleSubmit = async (values: RoleFormData) => {
    setIsSubmitting(true);
    try {
      await RoleService.create({ ...values, permissionIds: [] });

      notification.notifySuccess(t("role_created_successfully"));
      setIsModalOpen(false);
      form.resetFields();
      onSuccess?.();
    } catch (error) {
      console.error("Failed to add role:", error);
      notification.notifyError(t("role_creation_failed"));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <Card
        className="h-full cursor-pointer flex items-center justify-center [&_.ant-card-body]:w-full [&_.ant-card-body]:h-full [&_.ant-card-body]:flex [&_.ant-card-body]:flex-col [&_.ant-card-body]:items-center [&_.ant-card-body]:justify-center"
        onClick={showModal}
        hoverable
      >
        <div className="text-center py-8">
          <PlusOutlined style={{ fontSize: 28 }} />
          <p className="text-lg text-gray-600 font-medium">Add New Role</p>
        </div>
      </Card>

      <BaseModal
        isVisible={isModalOpen}
        title="Add Role"
        onClose={handleCancel}
        onSubmit={form.submit}
        submitText="Submit"
        confirmLoading={isSubmitting}
      >
        <Form form={form} layout="vertical" onFinish={handleSubmit}>
          <BaseInput
            name="name"
            label="Role name"
            required
            placeholder="Enter Role name"
          />

          <BaseTextArea
            name="description"
            label="Description"
            placeholder="Enter role description"
            rows={4}
          />

          <BaseCheckbox
            name="isDefault"
            checkboxLabel="Is default"
            helpText="Default role can not be deleted"
          />
        </Form>
      </BaseModal>
    </>
  );
};

export default AddRoleCard;
