"use client";
import {
  PayCircleOutlined,
  SmileOutlined,
  SolutionOutlined,
  UserOutlined,
} from "@ant-design/icons";
import {
  Alert,
  Card,
  Checkbox,
  Col,
  DatePicker,
  Divider,
  Flex,
  Form,
  InputNumber,
  message,
  Row,
  Steps,
  Typography,
} from "antd";
import dayjs from "dayjs";
import { useRouter } from "next/navigation";
import React, { useState } from "react";

import BaseButton from "@/components/ui/buttons/BaseButton";
import BaseInput from "@/components/ui/inputs/BaseInput";
import BaseSelect from "@/components/ui/selects/BaseSelect";

import BaseTextArea from "@/components/ui/inputs/BaseTextArea";
import ContractService from "@/services/contractService";
import {
  Contract,
  contractTypeOptions,
  paymentFrequencyOptions,
  workDayOptions,
} from "@/types/contract";
import { UserRole } from "@/constants/userRole";

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;

interface ContractFormProps {
  contract?: Contract;
  userRole?: UserRole;
  userId?: string;
  userName?: string;
}

const ContractForm: React.FC<ContractFormProps> = ({
  contract,
  userRole = UserRole.ADMIN,
  userId = "",
  userName = "",
}) => {
  console.log("🚀 ~ userName:", userName);
  console.log("🚀 ~ userId:", userId);
  const router = useRouter();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const isEditing = !!contract;

  const initialValues = contract
    ? {
        ...contract,
        contractDuration: [dayjs(contract.startDate), dayjs(contract.endDate)],
      }
    : {
        contractType: "part-time",
        paymentFrequency: "biweekly",
        workDays: ["monday", "tuesday", "wednesday", "thursday", "friday"],
        workingHoursPerWeek: 40,
        isFeePaid: false,
        status: "draft",
      };

  const handleSubmit = async (values: Contract) => {
    console.log("🚀 ~ handleSubmit ~ values:", values);
    try {
      setLoading(true);

      if (isEditing) {
        await ContractService.updateContract();
        message.success("Contract updated successfully!");
      } else {
        await ContractService.createContract();
        message.success("Contract created successfully!");
      }

      router.push("/contracts");
    } catch (error) {
      console.error("Error saving contract:", error);
      message.error("Failed to save contract");
    } finally {
      setLoading(false);
    }
  };

  const next = async () => {
    try {
      await form.validateFields();
      setCurrentStep(currentStep + 1);
    } catch (error) {
      console.error("Validation failed:", error);
    }
  };

  const prev = () => {
    setCurrentStep(currentStep - 1);
  };

  const steps = [
    {
      title: "Basic Info",
      icon: <UserOutlined />,
      content: (
        <>
          <Row gutter={16}>
            <Col span={24}>
              <BaseInput
                label="Contract Title"
                name="title"
                placeholder="e.g., Web Development Contract"
                required
              />
            </Col>
          </Row>

          <Row gutter={16}>
            <Col md={12} xs={24}>
              <BaseSelect
                label="Contract Type"
                name="contractType"
                options={contractTypeOptions}
                required
              />
            </Col>
            <Col md={12} xs={24}>
              <Form.Item
                label="Contract Duration"
                name="contractDuration"
                rules={[
                  {
                    required: true,
                    message: "Please select contract duration",
                  },
                ]}
              >
                <RangePicker style={{ width: "100%" }} format="YYYY-MM-DD" />
              </Form.Item>
            </Col>
          </Row>

          {userRole === UserRole.ADMIN && (
            <Row gutter={16}>
              <Col md={12} xs={24}>
                <BaseInput
                  label="Employer Name"
                  name="employerName"
                  placeholder="Enter employer name"
                  required
                />
                <BaseInput
                  label="Employer ID"
                  name="employerId"
                  placeholder="Enter employer ID"
                  required
                />
              </Col>
              <Col md={12} xs={24}>
                <BaseInput
                  label="Job Seeker Name"
                  name="jobSeekerName"
                  placeholder="Enter job seeker name"
                  required
                />
                <BaseInput
                  label="Job Seeker ID"
                  name="jobSeekerId"
                  placeholder="Enter job seeker ID"
                  required
                />
              </Col>
            </Row>
          )}

          {userRole === UserRole.EMPLOYER && !isEditing && (
            <Row gutter={16}>
              <Col span={24}>
                <BaseInput
                  label="Job Seeker Name"
                  name="jobSeekerName"
                  placeholder="Enter job seeker name"
                  required
                />
                <BaseInput
                  label="Job Seeker ID"
                  name="jobSeekerId"
                  placeholder="Enter job seeker ID"
                  required
                />
              </Col>
            </Row>
          )}

          {userRole === UserRole.JOB_SEEKER && !isEditing && (
            <Row gutter={16}>
              <Col span={24}>
                <BaseInput
                  label="Employer Name"
                  name="employerName"
                  placeholder="Enter employer name"
                  required
                />
                <BaseInput
                  label="Employer ID"
                  name="employerId"
                  placeholder="Enter employer ID"
                  required
                />
              </Col>
            </Row>
          )}
        </>
      ),
    },
    {
      title: "Work Schedule",
      icon: <SolutionOutlined />,
      content: (
        <>
          <Row gutter={16}>
            <Col md={12} xs={24}>
              <Form.Item
                label="Working Hours Per Week"
                name="workingHoursPerWeek"
                rules={[
                  { required: true, message: "Please enter working hours" },
                ]}
              >
                <InputNumber
                  min={1}
                  max={168}
                  style={{ width: "100%" }}
                  placeholder="e.g., 40"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                label="Work Days"
                name="workDays"
                rules={[
                  {
                    required: true,
                    message: "Please select at least one work day",
                  },
                ]}
              >
                <Checkbox.Group options={workDayOptions} />
              </Form.Item>
            </Col>
          </Row>
        </>
      ),
    },
    {
      title: "Payment Details",
      icon: <PayCircleOutlined />,
      content: (
        <>
          <Row gutter={16}>
            <Col md={12} xs={24}>
              <Form.Item
                label="Hourly Rate ($)"
                name="hourlyRate"
                rules={[
                  { required: true, message: "Please enter hourly rate" },
                ]}
              >
                <InputNumber
                  min={0}
                  step={0.01}
                  formatter={(value) => `$ ${value}`}
                  parser={() => 0}
                  style={{ width: "100%" }}
                  placeholder="e.g., 25.00"
                />
              </Form.Item>
            </Col>
            <Col md={12} xs={24}>
              <BaseSelect
                label="Payment Frequency"
                name="paymentFrequency"
                options={paymentFrequencyOptions}
                required
              />
            </Col>
          </Row>

          {userRole === UserRole.ADMIN && (
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item name="isFeePaid" valuePropName="checked">
                  <Checkbox>Contract fee has been paid</Checkbox>
                </Form.Item>
              </Col>
            </Row>
          )}

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                label="Additional Terms"
                name={["additionalTerms", "description"]}
              >
                <BaseTextArea
                  rows={4}
                  placeholder="Enter any additional terms or notes for this contract"
                />
              </Form.Item>
            </Col>
          </Row>
        </>
      ),
    },
    {
      title: "Review",
      icon: <SmileOutlined />,
      content: (
        <Card className="bg-gray-50">
          <Alert
            message="Review Contract Details"
            description="Please review all contract details before submitting. Once offered, the contract details cannot be modified until accepted or rejected by the other party."
            type="info"
            showIcon
            className="mb-4"
          />

          <Form.Item noStyle shouldUpdate>
            {({ getFieldsValue }) => {
              const values = getFieldsValue();
              const [startDate, endDate] = values.contractDuration || [];

              return (
                <>
                  <Row gutter={[16, 16]}>
                    <Col span={24}>
                      <Title level={5}>Basic Information</Title>
                      <Row gutter={16}>
                        <Col md={12} xs={24}>
                          <Text className="font-semibold">Contract Title:</Text>
                          <div>{values.title}</div>
                        </Col>
                        <Col md={12} xs={24}>
                          <Text className="font-semibold">Contract Type:</Text>
                          <div>{values.contractType}</div>
                        </Col>
                      </Row>

                      <Row gutter={16} className="mt-2">
                        <Col md={12} xs={24}>
                          <Text className="font-semibold">Start Date:</Text>
                          <div>{startDate?.format("YYYY-MM-DD")}</div>
                        </Col>
                        <Col md={12} xs={24}>
                          <Text className="font-semibold">End Date:</Text>
                          <div>{endDate?.format("YYYY-MM-DD")}</div>
                        </Col>
                      </Row>

                      <Row gutter={16} className="mt-2">
                        <Col md={12} xs={24}>
                          <Text className="font-semibold">Employer:</Text>
                          <div>{values.employerName}</div>
                        </Col>
                        <Col md={12} xs={24}>
                          <Text className="font-semibold">Job Seeker:</Text>
                          <div>{values.jobSeekerName}</div>
                        </Col>
                      </Row>
                    </Col>

                    <Col span={24}>
                      <Divider />
                      <Title level={5}>Work Schedule</Title>
                      <Row gutter={16}>
                        <Col md={12} xs={24}>
                          <Text className="font-semibold">
                            Working Hours Per Week:
                          </Text>
                          <div>{values.workingHoursPerWeek} hours</div>
                        </Col>
                        <Col md={12} xs={24}>
                          <Text className="font-semibold">Work Days:</Text>
                          <div>
                            {values.workDays
                              ?.map(
                                (day: string) =>
                                  day.charAt(0).toUpperCase() + day.slice(1)
                              )
                              .join(", ")}
                          </div>
                        </Col>
                      </Row>
                    </Col>

                    <Col span={24}>
                      <Divider />
                      <Title level={5}>Payment Details</Title>
                      <Row gutter={16}>
                        <Col md={12} xs={24}>
                          <Text className="font-semibold">Hourly Rate:</Text>
                          <div>${values.hourlyRate}</div>
                        </Col>
                        <Col md={12} xs={24}>
                          <Text className="font-semibold">
                            Payment Frequency:
                          </Text>
                          <div>{values.paymentFrequency}</div>
                        </Col>
                      </Row>

                      <Row gutter={16} className="mt-2">
                        <Col span={24}>
                          <Text className="font-semibold">
                            Additional Terms:
                          </Text>
                          <div>
                            {values.additionalTerms?.description || "None"}
                          </div>
                        </Col>
                      </Row>
                    </Col>
                  </Row>
                </>
              );
            }}
          </Form.Item>
        </Card>
      ),
    },
  ];

  return (
    <Card>
      <Title level={4}>
        {isEditing ? "Edit Contract" : "Create New Contract"}
      </Title>

      <Steps
        current={currentStep}
        items={steps.map((item) => ({
          title: item.title,
          icon: item.icon,
        }))}
        className="mb-8"
      />

      <Form
        form={form}
        layout="vertical"
        initialValues={initialValues}
        onFinish={handleSubmit}
      >
        <div className="py-4">{steps[currentStep].content}</div>

        <Flex justify="space-between" className="mt-6">
          {currentStep > 0 && (
            <BaseButton type="default" onClick={prev} label="Previous" />
          )}

          <Flex gap="small">
            {currentStep < steps.length - 1 && (
              <BaseButton type="primary" onClick={next} label="Next" />
            )}

            {currentStep === steps.length - 1 && (
              <>
                <BaseButton
                  type="default"
                  onClick={() => form.submit()}
                  loading={loading}
                  label="Save as Draft"
                />
                <BaseButton
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  label="Submit Contract"
                />
              </>
            )}
          </Flex>
        </Flex>
      </Form>
    </Card>
  );
};

export default ContractForm;
