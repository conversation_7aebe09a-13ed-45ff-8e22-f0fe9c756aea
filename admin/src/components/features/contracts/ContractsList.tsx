"use client";
import BaseTable from "@/components/ui/tables/BaseTable";
import { FilterConfig } from "@/components/ui/tables/TableFilter";
import ContractService from "@/services/contractService";
import { Contract, contractStatusOptions } from "@/types/contract";
import { Tag, Dropdown, Button, Modal, Select, message } from "antd";
import {
  MoreOutlined,
  EditOutlined,
  DeleteOutlined,
  SwapOutlined,
} from "@ant-design/icons";
import dayjs from "dayjs";
import { useRouter } from "next/navigation";
import React, { useState } from "react";
import { UserRole } from "@/constants/userRole";

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  }).format(amount);
};

interface ContractsListProps {
  userRole?: UserRole;
}

const ContractsList: React.FC<ContractsListProps> = ({
  userRole = UserRole.ADMIN,
}) => {
  const router = useRouter();
  const [statusModalVisible, setStatusModalVisible] = useState(false);
  const [selectedContract, setSelectedContract] = useState<Contract | null>(
    null
  );
  const [newStatus, setNewStatus] = useState<Contract["status"]>("draft");
  const [statusReason, setStatusReason] = useState("");

  const getStatusColor = (status: Contract["status"]) => {
    const statusColors: Record<Contract["status"], string> = {
      draft: "default",
      offered: "processing",
      active: "success",
      completed: "blue",
      terminated: "error",
    };
    return statusColors[status];
  };

  const columns = [
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      width: 80,
    },
    {
      title: "Title",
      dataIndex: "title",
      key: "title",
      render: (text: string) => <span className="font-medium">{text}</span>,
    },
    {
      title: "Employer",
      dataIndex: "employerName",
      key: "employerName",
      render: (text: string, record: Contract) => (
        <div>
          <div className="font-medium">{text}</div>
          <div className="text-xs text-gray-500">ID: {record.employerId}</div>
        </div>
      ),
      width: 150,
    },
    {
      title: "Job Seeker",
      dataIndex: "jobSeekerName",
      key: "jobSeekerName",
      render: (text: string, record: Contract) => (
        <div>
          <div className="font-medium">{text}</div>
          <div className="text-xs text-gray-500">ID: {record.jobSeekerId}</div>
        </div>
      ),
      width: 150,
    },
    {
      title: "Contract Type",
      dataIndex: "contractType",
      key: "contractType",
      render: (text: string) => (
        <span className="capitalize">{text.replace("-", " ")}</span>
      ),
      width: 140,
    },
    {
      title: "Hourly Rate",
      dataIndex: "hourlyRate",
      key: "hourlyRate",
      render: (rate: number) => formatCurrency(rate),
      width: 120,
    },
    {
      title: "Duration",
      key: "duration",
      render: (_: unknown, record: Contract) => (
        <span>
          {dayjs(record.startDate).format("MMM D, YYYY")} -
          {dayjs(record.endDate).format("MMM D, YYYY")}
        </span>
      ),
      width: 200,
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
      render: (status: Contract["status"]) => (
        <Tag color={getStatusColor(status)} className="capitalize">
          {status}
        </Tag>
      ),
      width: 100,
    },
    {
      title: "Created",
      dataIndex: "createdAt",
      key: "createdAt",
      render: (date: Date) => dayjs(date).format("MMM D, YYYY"),
      width: 120,
    },
    {
      title: "Actions",
      key: "actions",
      fixed: "right" as const,
      width: 80,
      render: (_, record: Contract) => {
        const items = [
          {
            key: "edit",
            label: "Edit",
            icon: <EditOutlined />,
            onClick: () => handleEdit(record),
          },
          {
            key: "changeStatus",
            label: "Change Status",
            icon: <SwapOutlined />,
            onClick: () => handleChangeStatus(record),
          },
          {
            key: "delete",
            label: "Delete",
            icon: <DeleteOutlined />,
            danger: true,
            onClick: () => {
              Modal.confirm({
                title: "Delete Contract",
                content:
                  "Are you sure you want to delete this contract? This action cannot be undone.",
                okText: "Delete",
                okType: "danger",
                cancelText: "Cancel",
                onOk: () => handleDeleteContract(record),
              });
            },
          },
        ];

        return (
          <div onClick={(e) => e.stopPropagation()}>
            <Dropdown menu={{ items }} trigger={["click"]}>
              <Button
                type="text"
                icon={<MoreOutlined />}
                size="small"
                onClick={(e) => e.stopPropagation()}
              />
            </Dropdown>
          </div>
        );
      },
    },
  ];

  // Filter columns based on user role
  const filteredColumns = columns.filter((col) => {
    // For admin role, show all columns including Job Seeker
    if (userRole === UserRole.ADMIN) return true;

    if (userRole === UserRole.EMPLOYER && col.key === "employerName")
      return false;
    if (userRole === UserRole.JOB_SEEKER && col.key === "jobSeekerName")
      return false;
    return true;
  });

  const filters: FilterConfig[] = [
    {
      key: "status",
      label: "Status",
      type: "select",
      options: contractStatusOptions,
    },
    {
      key: "contractType",
      label: "Contract Type",
      type: "select",
      options: [
        { value: "part-time", label: "Part-time" },
        { value: "freelance", label: "Freelance" },
        { value: "project", label: "Project" },
      ],
    },
    {
      key: "dateRange",
      label: "Created Date",
      type: "dateRange",
    },
  ];

  const handleEdit = (contract: Contract) => {
    if (userRole === UserRole.EMPLOYER) {
      router.push(`/employers-management/contracts/${contract.id}/edit`);
    } else {
      router.push(`/job-seekers-management/contracts/${contract.id}/edit`);
    }
  };

  const handleCreateContract = () => {
    if (userRole === UserRole.EMPLOYER) {
      router.push("/employers-management/contracts/create");
    } else {
      router.push("/job-seekers-management/contracts/create");
    }
  };

  const handleDeleteContract = async (contract: Contract) => {
    try {
      await ContractService.deleteContract(String(contract.id));
      message.success("Contract deleted successfully!");
      // You might want to trigger a table refresh here
    } catch (error) {
      console.error("Failed to delete contract:", error);
      message.error("Failed to delete contract");
    }
  };

  const handleChangeStatus = (contract: Contract) => {
    setSelectedContract(contract);
    setNewStatus(contract.status);
    setStatusReason("");
    setStatusModalVisible(true);
  };

  const handleStatusSubmit = async () => {
    if (!selectedContract) return;

    try {
      await ContractService.changeContractStatus(
        String(selectedContract.id),
        newStatus,
        statusReason
      );
      message.success("Contract status updated successfully!");
      setStatusModalVisible(false);
      setSelectedContract(null);
      // Trigger table refresh - you might need to add a ref to BaseTable
    } catch (error) {
      console.error("Failed to update contract status:", error);
      message.error("Failed to update contract status");
    }
  };

  const handleRowClick = (record: Contract) => {
    if (userRole === UserRole.EMPLOYER) {
      router.push(`/employers-management/contracts/${record.id}`);
    } else {
      router.push(`/job-seekers-management/contracts/${record.id}`);
    }
  };

  return (
    <>
      <BaseTable
        api={ContractService.getContracts}
        columns={filteredColumns}
        rowKey="id"
        title="Contracts"
        createBtnText="Create Contract"
        showSearch={true}
        searchPlaceholder="Search by title or ID..."
        onCreate={handleCreateContract}
        onRowClick={handleRowClick}
        filters={filters}
        showActions={false} // Disable default actions since we have custom actions
      />

      {/* Change Status Modal */}
      <Modal
        title="Change Contract Status"
        open={statusModalVisible}
        onOk={handleStatusSubmit}
        onCancel={() => setStatusModalVisible(false)}
        okText="Update Status"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">
              Current Status:{" "}
              <Tag color={getStatusColor(selectedContract?.status || "draft")}>
                {selectedContract?.status}
              </Tag>
            </label>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              New Status:
            </label>
            <Select
              value={newStatus}
              onChange={setNewStatus}
              className="w-full"
              options={contractStatusOptions}
            />
          </div>

          {(newStatus === "terminated" || newStatus === "completed") && (
            <div>
              <label className="block text-sm font-medium mb-2">
                Reason{" "}
                {newStatus === "terminated" ? "(Required)" : "(Optional)"}:
              </label>
              <Select
                value={statusReason}
                onChange={setStatusReason}
                className="w-full"
                placeholder="Select reason..."
                options={[
                  {
                    value: "completed_successfully",
                    label: "Completed Successfully",
                  },
                  { value: "mutual_agreement", label: "Mutual Agreement" },
                  { value: "breach_of_contract", label: "Breach of Contract" },
                  { value: "performance_issues", label: "Performance Issues" },
                  { value: "budget_constraints", label: "Budget Constraints" },
                  { value: "project_cancelled", label: "Project Cancelled" },
                  { value: "other", label: "Other" },
                ]}
              />
            </div>
          )}
        </div>
      </Modal>
    </>
  );
};

export default ContractsList;
