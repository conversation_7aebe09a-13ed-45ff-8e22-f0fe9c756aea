import { Contract } from "@/types/contract";
import { Card, Descriptions, Flex, Badge } from "antd";
import dayjs from "dayjs";
import React from "react";

interface GeneralInfoProps {
  contract: Contract;
}

const GeneralInfo = ({ contract }: GeneralInfoProps) => {
  const getStatusColor = (
    status: Contract["status"]
  ): "success" | "processing" | "error" | "default" | "warning" => {
    const statusColors: Record<
      Contract["status"],
      "success" | "processing" | "error" | "default" | "warning"
    > = {
      draft: "default",
      offered: "processing",
      active: "success",
      completed: "success",
      terminated: "error",
    };
    return statusColors[status];
  };

  return (
    <Card className="mb-4">
      <Descriptions
        title={
          <Flex align="center" gap="small">
            <span>{contract.title}</span>
            <Badge
              status={getStatusColor(contract.status)}
              text={contract.status.toUpperCase()}
            />
          </Flex>
        }
        bordered
        column={{ xxl: 4, xl: 3, lg: 3, md: 2, sm: 1, xs: 1 }}
      >
        <Descriptions.Item label="Contract ID">{contract.id}</Descriptions.Item>
        <Descriptions.Item label="Contract Type">
          <span className="capitalize">
            {contract.contractType.replace("-", " ")}
          </span>
        </Descriptions.Item>
        <Descriptions.Item label="Created">
          {dayjs(contract.createdAt).format("MMM D, YYYY")}
        </Descriptions.Item>
        {contract.activatedAt && (
          <Descriptions.Item label="Activated">
            {dayjs(contract.activatedAt).format("MMM D, YYYY")}
          </Descriptions.Item>
        )}

        <Descriptions.Item label="Start Date">
          {dayjs(contract.startDate).format("MMM D, YYYY")}
        </Descriptions.Item>
        <Descriptions.Item label="End Date">
          {dayjs(contract.endDate).format("MMM D, YYYY")}
        </Descriptions.Item>
        <Descriptions.Item label="Employer">
          {contract.employerName}
        </Descriptions.Item>
        <Descriptions.Item label="Job Seeker">
          {contract.jobSeekerName}
        </Descriptions.Item>

        <Descriptions.Item label="Hourly Rate">
          {contract.hourlyRate}
        </Descriptions.Item>
        <Descriptions.Item label="Payment Frequency">
          <span className="capitalize">{contract.paymentFrequency}</span>
        </Descriptions.Item>
        <Descriptions.Item label="Working Hours">
          {contract.workingHoursPerWeek} hours/week
        </Descriptions.Item>
        <Descriptions.Item label="Work Days">
          {contract.workDays
            .map((day: string) => day.charAt(0).toUpperCase() + day.slice(1))
            .join(", ")}
        </Descriptions.Item>

        {contract.terminatedAt && (
          <>
            <Descriptions.Item label="Terminated Date" span={2}>
              {dayjs(contract.terminatedAt).format("MMM D, YYYY")}
            </Descriptions.Item>
            <Descriptions.Item label="Termination Reason" span={2}>
              {contract.terminationReason}
            </Descriptions.Item>
          </>
        )}

        <Descriptions.Item label="Additional Terms" span={4}>
          {contract.additionalTerms.description as string}
        </Descriptions.Item>
      </Descriptions>
    </Card>
  );
};

export default GeneralInfo;
