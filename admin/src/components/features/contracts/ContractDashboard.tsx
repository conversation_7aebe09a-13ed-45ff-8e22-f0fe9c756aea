"use client";
import React, { useState, useEffect } from "react";
import {
  Card,
  Row,
  Col,
  Statistic,
  Progress,
  List,
  Tag,
  Flex,
  Space,
  Empty,
} from "antd";
import {
  FileTextOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  WarningOutlined,
  DollarOutlined,
  UserOutlined,
} from "@ant-design/icons";
import dayjs from "dayjs";
import ContractService from "@/services/contractService";
import { Contract } from "@/types/contract";
import { UserRole } from "@/constants/userRole";

interface ContractStats {
  total: number;
  active: number;
  completed: number;
  draft: number;
  offered: number;
  terminated: number;
  expiringSoon: number;
  recentPayments: number;
}

interface ContractDashboardProps {
  userRole?: UserRole;
  userId?: string;
}

const ContractDashboard: React.FC<ContractDashboardProps> = ({
  userRole = UserRole.ADMIN,
  userId = "",
}) => {
  const [loading, setLoading] = useState(true);
  const [contracts, setContracts] = useState<Contract[]>([]);
  const [stats, setStats] = useState<ContractStats>({
    total: 0,
    active: 0,
    completed: 0,
    draft: 0,
    offered: 0,
    terminated: 0,
    expiringSoon: 0,
    recentPayments: 0,
  });

  useEffect(() => {
    fetchContracts();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const fetchContracts = async () => {
    try {
      setLoading(true);

      // Fetch all contracts
      const response = await ContractService.getContracts({
        pageSize: 1000, // Get all for dashboard calculations
        ...(userRole === UserRole.EMPLOYER ? { employerId: userId } : {}),
        ...(userRole === UserRole.JOB_SEEKER ? { jobSeekerId: userId } : {}),
      });

      setContracts(response.data);

      // Calculate statistics
      const now = dayjs();
      const thirtyDaysFromNow = now.add(30, "day");

      const activeContracts = response.data.filter(
        (c) => c.status === "active"
      );
      const completedContracts = response.data.filter(
        (c) => c.status === "completed"
      );
      const draftContracts = response.data.filter((c) => c.status === "draft");
      const offeredContracts = response.data.filter(
        (c) => c.status === "offered"
      );
      const terminatedContracts = response.data.filter(
        (c) => c.status === "terminated"
      );

      // Contracts expiring in the next 30 days
      const expiringSoon = activeContracts.filter((c) =>
        dayjs(c.endDate).isBefore(thirtyDaysFromNow)
      );

      // Recent payments (last 30 days)
      const thirtyDaysAgo = now.subtract(30, "day");
      const recentPayments = response.data.reduce((total, contract) => {
        const recentPaymentsForContract =
          contract.payments?.filter(
            (p) =>
              dayjs(p.date).isAfter(thirtyDaysAgo) && p.status === "completed"
          ) || [];
        return total + recentPaymentsForContract.length;
      }, 0);

      setStats({
        total: response.data.length,
        active: activeContracts.length,
        completed: completedContracts.length,
        draft: draftContracts.length,
        offered: offeredContracts.length,
        terminated: terminatedContracts.length,
        expiringSoon: expiringSoon.length,
        recentPayments,
      });
    } catch (error) {
      console.error("Error fetching contracts for dashboard:", error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: Contract["status"]) => {
    const statusColors: Record<Contract["status"], string> = {
      draft: "default",
      offered: "processing",
      active: "success",
      completed: "blue",
      terminated: "error",
    };
    return statusColors[status];
  };

  const getExpiringContractsList = () => {
    const expiringContracts = contracts
      .filter((c) => c.status === "active")
      .filter((c) => dayjs(c.endDate).diff(dayjs(), "day") <= 30)
      .sort((a, b) => dayjs(a.endDate).diff(dayjs(b.endDate)));

    return expiringContracts.slice(0, 5);
  };

  const getRecentContracts = () => {
    return [...contracts]
      .sort((a, b) => dayjs(b.createdAt).diff(dayjs(a.createdAt)))
      .slice(0, 5);
  };

  // const calculateActiveContractsValue = () => {
  //   return contracts
  //     .filter((c) => c.status === "active")
  //     .reduce((total, contract) => {
  //       const startDate = dayjs(contract.startDate);
  //       const endDate = dayjs(contract.endDate);
  //       const duration = endDate.diff(startDate, "week");
  //       return (
  //         total + contract.hourlyRate * contract.workingHoursPerWeek * duration
  //       );
  //     }, 0);
  // };

  return (
    <div>
      <Row gutter={[16, 16]}>
        {/* Statistics Cards */}
        <Col xs={24} sm={12} md={8} lg={6}>
          <Card loading={loading}>
            <Statistic
              title="Total Contracts"
              value={stats.total}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>

        <Col xs={24} sm={12} md={8} lg={6}>
          <Card loading={loading}>
            <Statistic
              title="Active Contracts"
              value={stats.active}
              prefix={<CheckCircleOutlined style={{ color: "#52c41a" }} />}
            />
          </Card>
        </Col>

        <Col xs={24} sm={12} md={8} lg={6}>
          <Card loading={loading}>
            <Statistic
              title="Expiring Soon"
              value={stats.expiringSoon}
              prefix={<WarningOutlined style={{ color: "#faad14" }} />}
            />
          </Card>
        </Col>

        <Col xs={24} sm={12} md={8} lg={6}>
          <Card loading={loading}>
            <Statistic
              title="Recent Payments"
              value={stats.recentPayments}
              prefix={<DollarOutlined style={{ color: "#1890ff" }} />}
            />
          </Card>
        </Col>

        {/* Contract Status Chart */}
        <Col xs={24} lg={12}>
          <Card title="Contract Status" loading={loading}>
            {!loading && stats.total > 0 ? (
              <Flex vertical gap="middle">
                <Flex justify="space-between">
                  <div>Active</div>
                  <div>{stats.active}</div>
                </Flex>
                <Progress
                  percent={Math.round((stats.active / stats.total) * 100)}
                  status="active"
                  showInfo={false}
                  strokeColor="#52c41a"
                />

                <Flex justify="space-between">
                  <div>Offered</div>
                  <div>{stats.offered}</div>
                </Flex>
                <Progress
                  percent={Math.round((stats.offered / stats.total) * 100)}
                  status="active"
                  showInfo={false}
                  strokeColor="#1890ff"
                />

                <Flex justify="space-between">
                  <div>Draft</div>
                  <div>{stats.draft}</div>
                </Flex>
                <Progress
                  percent={Math.round((stats.draft / stats.total) * 100)}
                  status="active"
                  showInfo={false}
                  strokeColor="#8c8c8c"
                />

                <Flex justify="space-between">
                  <div>Completed</div>
                  <div>{stats.completed}</div>
                </Flex>
                <Progress
                  percent={Math.round((stats.completed / stats.total) * 100)}
                  status="active"
                  showInfo={false}
                  strokeColor="#13c2c2"
                />

                <Flex justify="space-between">
                  <div>Terminated</div>
                  <div>{stats.terminated}</div>
                </Flex>
                <Progress
                  percent={Math.round((stats.terminated / stats.total) * 100)}
                  status="active"
                  showInfo={false}
                  strokeColor="#f5222d"
                />
              </Flex>
            ) : (
              <Empty description="No contract data available" />
            )}
          </Card>
        </Col>

        {/* Contracts Expiring Soon */}
        <Col xs={24} lg={12}>
          <Card title="Contracts Expiring Soon" loading={loading}>
            {!loading && getExpiringContractsList().length > 0 ? (
              <List
                dataSource={getExpiringContractsList()}
                renderItem={(contract) => {
                  const daysLeft = dayjs(contract.endDate).diff(dayjs(), "day");
                  return (
                    <List.Item>
                      <Flex justify="space-between" style={{ width: "100%" }}>
                        <Flex vertical>
                          <span className="font-medium">{contract.title}</span>
                          <Space size="small">
                            <UserOutlined />
                            <span>
                              {userRole === UserRole.EMPLOYER
                                ? contract.jobSeekerName
                                : contract.employerName}
                            </span>
                          </Space>
                        </Flex>
                        <Flex vertical align="end">
                          <Tag color={daysLeft <= 7 ? "error" : "warning"}>
                            {daysLeft} days left
                          </Tag>
                          <span>
                            {dayjs(contract.endDate).format("MMM D, YYYY")}
                          </span>
                        </Flex>
                      </Flex>
                    </List.Item>
                  );
                }}
              />
            ) : (
              <Empty description="No contracts expiring soon" />
            )}
          </Card>
        </Col>

        {/* Recent Contracts */}
        <Col xs={24}>
          <Card title="Recent Contracts" loading={loading}>
            {!loading && getRecentContracts().length > 0 ? (
              <List
                dataSource={getRecentContracts()}
                renderItem={(contract) => (
                  <List.Item>
                    <Flex justify="space-between" style={{ width: "100%" }}>
                      <Flex vertical>
                        <span className="font-medium">{contract.title}</span>
                        <Space size="small">
                          <ClockCircleOutlined />
                          <span>
                            {dayjs(contract.createdAt).format("MMM D, YYYY")}
                          </span>
                        </Space>
                      </Flex>
                      <Flex vertical align="end">
                        <Space size="small">
                          <DollarOutlined />
                          <span>${contract.hourlyRate}/hr</span>
                        </Space>
                        <Tag color={getStatusColor(contract.status)}>
                          {contract.status}
                        </Tag>
                      </Flex>
                    </Flex>
                  </List.Item>
                )}
              />
            ) : (
              <Empty description="No recent contracts" />
            )}
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default ContractDashboard;
