"use client";
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  DollarOutlined,
  ExclamationCircleOutlined,
  FileTextOutlined,
  MessageOutlined,
} from "@ant-design/icons";
import {
  Card,
  Empty,
  Flex,
  message,
  Modal,
  Space,
  Tabs,
  Typography,
} from "antd";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";

import DetailTab from "@/components/features/contracts/detailTabs/DetailTab";
import MessageTab from "@/components/features/contracts/detailTabs/MessageTab";
import PaymentTab from "@/components/features/contracts/detailTabs/PaymentTab";
import TimeEntryTab from "@/components/features/contracts/detailTabs/TimeEntryTab";
import GeneralInfo from "@/components/features/contracts/GeneralInfo";
import BaseButton from "@/components/ui/buttons/BaseButton";
import ContractService from "@/services/contractService";
import { Contract } from "@/types/contract";
import { UserRole } from "@/constants/userRole";

const { Title } = Typography;
const { confirm } = Modal;

interface ContractDetailsProps {
  contractId: string;
  userRole: UserRole;
}

const ContractDetails: React.FC<ContractDetailsProps> = ({
  contractId,
  userRole,
}) => {
  const router = useRouter();
  const [contract, setContract] = useState<Contract | null>(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);
  const [currentTab, setCurrentTab] = useState("details");

  useEffect(() => {
    fetchContractDetails();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [contractId]);

  async function fetchContractDetails() {
    try {
      setLoading(true);
      const data = await ContractService.getContract(contractId);
      setContract(data as any);
    } catch (error) {
      console.error("Error fetching contract details:", error);
      message.error("Failed to load contract details");
    } finally {
      setLoading(false);
    }
  }

  const handleAcceptContract = async () => {
    if (!contract) return;

    try {
      setActionLoading(true);
      await ContractService.changeContractStatus(
        contract.id.toString(),
        "active"
      );
      message.success("Contract accepted successfully!");
      fetchContractDetails();
    } catch (error) {
      console.error("Error accepting contract:", error);
      message.error("Failed to accept contract");
    } finally {
      setActionLoading(false);
    }
  };

  const handleRejectContract = async () => {
    if (!contract) return;

    confirm({
      title: "Reject Contract",
      icon: <ExclamationCircleOutlined />,
      content:
        "Are you sure you want to reject this contract? This action cannot be undone.",
      onOk: async () => {
        try {
          setActionLoading(true);
          await ContractService.changeContractStatus(
            contract.id.toString(),
            "terminated",
            "Rejected by job seeker"
          );
          message.success("Contract rejected successfully!");
          fetchContractDetails();
        } catch (error) {
          console.error("Error rejecting contract:", error);
          message.error("Failed to reject contract");
        } finally {
          setActionLoading(false);
        }
      },
    });
  };

  const handleEditContract = () => {
    if (!contract) return;
    router.push(`/contracts/${contract.id}/edit`);
  };

  const handleCompleteContract = async () => {
    if (!contract) return;

    confirm({
      title: "Complete Contract",
      icon: <ExclamationCircleOutlined />,
      content:
        "Are you sure you want to mark this contract as completed? This action cannot be undone.",
      onOk: async () => {
        try {
          setActionLoading(true);
          await ContractService.changeContractStatus(
            contract.id.toString(),
            "completed"
          );
          message.success("Contract marked as completed!");
          fetchContractDetails();
        } catch (error) {
          console.error("Error completing contract:", error);
          message.error("Failed to complete contract");
        } finally {
          setActionLoading(false);
        }
      },
    });
  };

  const handleTerminateContract = () => {
    console.log("handleTerminateContract");
  };

  const renderContractActions = () => {
    if (userRole === UserRole.JOB_SEEKER && contract?.status === "offered") {
      return (
        <Space>
          <BaseButton
            type="primary"
            icon={<CheckCircleOutlined />}
            onClick={handleAcceptContract}
            loading={actionLoading}
            label="Accept Contract"
          />
          <BaseButton
            danger
            icon={<CloseCircleOutlined />}
            onClick={handleRejectContract}
            loading={actionLoading}
            label="Reject Contract"
          />
        </Space>
      );
    }

    if (userRole === UserRole.EMPLOYER && contract?.status === "active") {
      return (
        <Space>
          <BaseButton
            type="primary"
            onClick={handleCompleteContract}
            loading={actionLoading}
            label="Complete Contract"
          />
          <BaseButton
            danger
            onClick={handleTerminateContract}
            loading={actionLoading}
            label="Terminate Contract"
          />
        </Space>
      );
    }

    if (userRole === UserRole.ADMIN && contract) {
      return (
        <Space>
          <BaseButton
            type="primary"
            onClick={handleEditContract}
            label="Edit Contract"
          />
          {contract.status === "active" && (
            <>
              <BaseButton
                type="default"
                onClick={handleCompleteContract}
                loading={actionLoading}
                label="Complete Contract"
              />
              <BaseButton
                danger
                onClick={handleTerminateContract}
                loading={actionLoading}
                label="Terminate Contract"
              />
            </>
          )}
        </Space>
      );
    }

    return null;
  };

  if (loading) {
    return <div>Loading contract details...</div>;
  }

  if (!contract) {
    return <div>Contract not found</div>;
  }

  const tabItems = [
    {
      key: "details",
      label: "Details",
      icon: <FileTextOutlined />,
      children: <DetailTab contract={contract} />,
    },
    {
      key: "timeEntries",
      label: "Time Entries",
      icon: <CheckCircleOutlined />,
      children: <TimeEntryTab />,
    },
    {
      key: "payments",
      label: "Payments",
      icon: <DollarOutlined />,
      children: <PaymentTab />,
    },
    {
      key: "messages",
      label: "Messages",
      icon: <MessageOutlined />,
      children: (
        <div>
          {contract.messages && contract.messages.length > 0 ? (
            <div className="message-container">
              {/* {contract.messages.map((msg) => (
                <MessageTab key={msg.id} msg={msg} />
              ))} */}
            </div>
          ) : (
            <Empty description="No messages found" />
          )}
        </div>
      ),
    },
  ];

  return (
    <div>
      <Flex justify="space-between" align="center" className="!mb-4">
        <Title level={4}>Contract Details</Title>
        {renderContractActions()}
      </Flex>

      <GeneralInfo contract={contract} />

      <Card className="!mt-8">
        <Tabs
          activeKey={currentTab}
          onChange={setCurrentTab}
          items={tabItems}
        />
      </Card>
    </div>
  );
};

export default ContractDetails;
