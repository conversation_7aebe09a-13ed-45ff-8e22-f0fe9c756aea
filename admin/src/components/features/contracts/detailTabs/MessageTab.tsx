import { ContractMessage } from "@/types/contract";
import { Card, Flex, Typography } from "antd";
import dayjs from "dayjs";
import React from "react";

const { Text, Paragraph } = Typography;

interface MessageTabProps {
  msg: ContractMessage;
  userId: string;
}

const MessageTab = ({ msg, userId }: MessageTabProps) => {
  return (
    <Card
      key={msg.id}
      size="small"
      className={`!mb-3 ${msg.senderId === userId ? "ml-auto" : "mr-auto"}`}
      style={{
        marginLeft: msg.senderId === userId ? "auto" : "0",
      }}
    >
      <Flex vertical gap="small">
        <Flex justify="space-between">
          <Text strong>{msg.senderName}</Text>
          <Text type="secondary">
            {dayjs(msg.createdAt).format("MMM D, YYYY HH:mm")}
          </Text>
        </Flex>
        <Paragraph>{msg.message}</Paragraph>
      </Flex>
    </Card>
  );
};

export default MessageTab;
