"use client";

import TimeEntryModal from "@/components/features/contracts/modals/TimeEntryModal";
import BaseTable from "@/components/ui/tables/BaseTable";
import ContractService from "@/services/contractService";
import { TimeEntry } from "@/types/contract";
import { TableColumnsType, Tag } from "antd";
import dayjs from "dayjs";
import { useParams } from "next/navigation";
import { useState } from "react";

const TimeEntryTab = () => {
  const { id } = useParams();
  const [visible, setVisible] = useState(false);

  const columns: TableColumnsType<TimeEntry> = [
    {
      title: "Date",
      dataIndex: "date",
      key: "date",
      render: (date: string) => dayjs(date).format("MMM D, YYYY"),
    },
    {
      title: "Hours",
      dataIndex: "hoursWorked",
      key: "hoursWorked",
    },
    {
      title: "Description",
      dataIndex: "description",
      key: "description",
    },
    {
      title: "Status",
      dataIndex: "status",
      key: "status",
      render: (status: string) => {
        const colors: Record<string, string> = {
          pending: "processing",
          approved: "success",
          rejected: "error",
        };
        return <Tag color={colors[status]}>{status.toUpperCase()}</Tag>;
      },
    },
  ];

  return (
    <>
      <BaseTable
        api={(params) =>
          ContractService.getTimeEntries(id as string, params).then((res) => ({
            data: res.data,
            total: res.total,
          }))
        }
        columns={columns}
        rowKey="id"
        createBtnText="Add Time Entry"
        showActions={false}
        onCreate={() => setVisible(true)}
      />
      <TimeEntryModal
        contractId={id as string}
        visible={visible}
        setVisible={setVisible}
      />
    </>
  );
};

export default TimeEntryTab;
