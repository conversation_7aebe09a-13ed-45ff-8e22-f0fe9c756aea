"use client";
import React from "react";
import { Form } from "antd";
import { FormInstance } from "antd/lib/form";
import BaseTextArea from "@/components/ui/inputs/BaseTextArea";
import BaseModal from "@/components/ui/modals/BaseModal";
import { Contract } from "@/types/contract";

interface MessageModalProps {
  contract: Contract;
  visible: boolean;
  setVisible: (visible: boolean) => void;
  form: FormInstance;
  loading: boolean;
  onSubmit: () => Promise<void>;
}

const MessageModal: React.FC<MessageModalProps> = ({
  visible,
  setVisible,
  form,
  loading,
  onSubmit,
}) => {
  return (
    <BaseModal
      title="Send Message"
      isVisible={visible}
      onClose={() => setVisible(false)}
      onSubmit={onSubmit}
      submitText="Send"
      confirmLoading={loading}
    >
      <Form form={form} layout="vertical" onFinish={onSubmit}>
        <BaseTextArea
          name="message"
          required={true}
          rows={4}
          placeholder="Type your message here..."
        />
      </Form>
    </BaseModal>
  );
};

export default MessageModal;
