"use client";
import BaseDatePicker from "@/components/ui/datepickers/BaseDatePicker";
import BaseInputNumber from "@/components/ui/inputs/BaseInputNumber";
import BaseTextArea from "@/components/ui/inputs/BaseTextArea";
import BaseModal from "@/components/ui/modals/BaseModal";
import BaseSelect from "@/components/ui/selects/BaseSelect";
import { Form } from "antd";
import React, { useState } from "react";

interface PaymentModalProps {
  contractId: string;
  visible: boolean;
  setVisible: (visible: boolean) => void;
}

const PaymentModal: React.FC<PaymentModalProps> = ({
  contractId,
  visible,
  setVisible,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleSubmit = () => {
    setLoading(true);
    console.log("Submit: ", contractId);
    setVisible(false);
    setLoading(false);
  };

  const paymentMethodOptions = [
    { value: "bank_transfer", label: "Bank Transfer" },
    { value: "credit_card", label: "Credit Card" },
    { value: "paypal", label: "PayPal" },
    { value: "other", label: "Other" },
  ];

  return (
    <BaseModal
      title="Add Payment"
      isVisible={visible}
      onClose={() => setVisible(false)}
      onSubmit={handleSubmit}
      submitText="Submit Payment"
      confirmLoading={loading}
    >
      <Form form={form} layout="vertical" onFinish={handleSubmit}>
        <BaseDatePicker name="date" label="Payment Date" required={true} />

        <BaseInputNumber
          name="amount"
          label="Amount ($)"
          required={true}
          min={0.01}
          step={0.01}
          formatter={(value) => `$ ${value}`}
          parser={(value) => (value ? value.replace(/\$\s?|(,*)/g, "") : "")}
          placeholder="Enter payment amount"
        />

        <BaseTextArea
          name="description"
          label="Description"
          required={true}
          rows={3}
          placeholder="Enter payment description"
        />

        <BaseSelect
          name="paymentMethod"
          label="Payment Method"
          required={true}
          options={paymentMethodOptions}
          placeholder="Select payment method"
        />
      </Form>
    </BaseModal>
  );
};

export default PaymentModal;
