"use client";
import { useState } from "react";
import { Permission, PermissionFormData } from "@/types/permission";
import BaseModal from "@/components/ui/modals/BaseModal";
import PermissionService from "@/services/permissionService";
import PermissionForm from "./PermissionForm";
import { useTranslations } from "next-intl";
import { useNotification } from "@/contexts/NotiContext";

interface PermissionFormModalProps {
  isVisible: boolean;
  onClose: () => void;
  selectedPermission: Permission | null;
  onSuccess: () => void;
}

const PermissionFormModal = ({
  isVisible,
  onClose,
  selectedPermission,
  onSuccess,
}: PermissionFormModalProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const t = useTranslations("permissions");
  const tForm = useTranslations("form");
  const notification = useNotification();

  const handleSubmit = async (data: PermissionFormData) => {
    setIsSubmitting(true);
    try {
      if (selectedPermission) {
        await PermissionService.update(selectedPermission.id, data);
        notification.notifySuccess(t("permission_updated_successfully"));
      } else {
        await PermissionService.create(data);
        notification.notifySuccess(t("permission_created_successfully"));
      }

      onSuccess();
      onClose();
    } catch (error: any) {
      notification.notifyError(
        error?.response?.data?.message || tForm("default_error")
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <BaseModal
      isVisible={isVisible}
      title={!selectedPermission ? t("add_permission") : t("edit_permission")}
      onClose={onClose}
      footer={null}
    >
      <PermissionForm
        initialData={selectedPermission || undefined}
        onSubmit={handleSubmit}
        onCancel={onClose}
        isSubmitting={isSubmitting}
      />
    </BaseModal>
  );
};

export default PermissionFormModal;
