"use client";
import { useEffect } from "react";
import { Form, Input, Button, Flex } from "antd";
import { Permission, PermissionFormData } from "@/types/permission";
import BaseInput from "@/components/ui/inputs/BaseInput";
import { useTranslations } from "next-intl";

interface PermissionFormProps {
  initialData?: Permission;
  onSubmit: (data: PermissionFormData) => Promise<void>;
  onCancel: () => void;
  isSubmitting: boolean;
}

const PermissionForm = ({
  initialData,
  onSubmit,
  onCancel,
  isSubmitting,
}: PermissionFormProps) => {
  const [form] = Form.useForm();
  const t = useTranslations("permissions");
  const tCommon = useTranslations("common");
  const tForm = useTranslations("form");

  useEffect(() => {
    if (initialData) {
      form.setFieldsValue(initialData);
    } else {
      form.resetFields();
    }
  }, [initialData, form]);

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={onSubmit}
      initialValues={initialData || { name: "", description: "" }}
    >
      <BaseInput
        name="name"
        label={t("permission_name")}
        placeholder={t("enter_permission_name")}
        required
        helpText={t("enter_permission_name_subtitle")}
        disabled={true}
      />

      <Form.Item
        name="description"
        label={tCommon("fields.description")}
        rules={[{ required: true, message: tForm("required") }]}
      >
        <Input.TextArea
          rows={3}
          placeholder={t("permission_description_placeholder")}
          disabled={true}
        />
      </Form.Item>
    </Form>
  );
};

export default PermissionForm;
