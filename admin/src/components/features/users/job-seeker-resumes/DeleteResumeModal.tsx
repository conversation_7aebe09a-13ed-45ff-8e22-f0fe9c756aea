// components/features/resumes/DeleteResumeModal.tsx
import React from "react";
import BaseModal from "@/components/ui/modals/BaseModal";
import { Resume } from "@/types/resume";

interface DeleteResumeModalProps {
  resume: Resume | null;
  isVisible: boolean;
  isLoading: boolean;
  onClose: () => void;
  onConfirm: () => Promise<void>;
}

const DeleteResumeModal: React.FC<DeleteResumeModalProps> = ({
  resume,
  isVisible,
  isLoading,
  onClose,
  onConfirm,
}) => {
  if (!resume) return null;

  return (
    <BaseModal
      title="Delete Resume"
      isVisible={isVisible}
      onClose={onClose}
      onSubmit={onConfirm}
      submitText="Delete"
      okButtonProps={{ danger: true }}
      confirmLoading={isLoading}
    >
      <p>
        Are you sure you want to delete the resume {resume.name}? This action
        cannot be undone.
      </p>
      {resume.isActive && (
        <div className="bg-yellow-50 border border-yellow-100 p-3 rounded-md mt-4">
          <p className="text-yellow-700">
            <b>Warning:</b> This is your active resume. Deleting it will require
            you to set another resume as active.
          </p>
        </div>
      )}
    </BaseModal>
  );
};

export default DeleteResumeModal;
