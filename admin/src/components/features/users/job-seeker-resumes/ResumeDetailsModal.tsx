import ResumeView from "@/components/features/users/job-seeker-resumes/ResumeView";
import BaseButton from "@/components/ui/buttons/BaseButton";
import BaseModal from "@/components/ui/modals/BaseModal";
import { Resume } from "@/types/resume";
import React from "react";
import { PermissionEnum } from "@/constants/_permissionEnum";

interface ResumeDetailsModalProps {
  setIsViewModalVisible: (visible: boolean) => void;
  currentResume: Resume | null;
  handleEdit?: (resume: Resume) => void;
  isViewModalVisible: boolean;
  access?: any;
}

const ResumeDetailsModal: React.FC<ResumeDetailsModalProps> = ({
  currentResume,
  isViewModalVisible,
  setIsViewModalVisible,
  handleEdit,
  access,
}) => {
  const handleSetActive = async (resume: Resume) => {
    console.log("🚀 ~ handleSetActive ~ resume:", resume);
    try {
      setIsViewModalVisible(false);
    } catch (error) {
      console.log("🚀 ~ handleSetActive ~ error:", error);
    }
  };

  return (
    <BaseModal
      title={currentResume?.name || "Resume Details"}
      isVisible={isViewModalVisible}
      onClose={() => setIsViewModalVisible(false)}
      onSubmit={() => setIsViewModalVisible(false)}
      submitText="Close"
      width={1500}
      footer={
        <div className="flex justify-between">
          <div>
            {currentResume && !currentResume.isActive && (
              <BaseButton
                label="Set as Active Resume"
                type="primary"
                onClick={() => handleSetActive(currentResume)}
              />
            )}
          </div>
          <div>
            <BaseButton
              label="Close"
              className="mr-2"
              onClick={() => setIsViewModalVisible(false)}
            />
            {currentResume &&
              access?.[PermissionEnum.RESUME_UPDATE] &&
              handleEdit && (
                <BaseButton
                  label="Edit Resume"
                  type="primary"
                  onClick={() => {
                    setIsViewModalVisible(false);
                    handleEdit(currentResume);
                  }}
                />
              )}
          </div>
        </div>
      }
    >
      {currentResume && <ResumeView resume={currentResume} />}
    </BaseModal>
  );
};

export default ResumeDetailsModal;
