// components/features/resumes/ResumeView.tsx
import React from "react";
import { Resume } from "@/types/resume";
import { ProfileView } from "./components";
import { resumeToProfileView } from "@/utils/profileAdapters";

interface ResumeViewProps {
  resume: Resume;
  className?: string;
}

const ResumeView: React.FC<ResumeViewProps> = ({ resume, className }) => {
  return <ProfileView {...resumeToProfileView(resume)} className={className} />;
};

export default ResumeView;
