import React from "react";
import BaseInput from "@/components/ui/inputs/BaseInput";

const ContactInfoTab: React.FC = () => {
  return (
    <>
      <BaseInput
        label="Phone Number"
        name={["contactInfo", "phoneNumber"]}
        required
        placeholder="Enter your phone number"
      />

      <BaseInput
        label="Email"
        name={["contactInfo", "email"]}
        required
        placeholder="Enter your email address"
      />

      <BaseInput
        label="Address"
        name={["contactInfo", "address"]}
        placeholder="Enter your street address"
      />

      <div className="flex flex-col md:flex-row gap-4">
        <BaseInput
          label="City"
          name={["contactInfo", "city"]}
          placeholder="Enter your city"
          className="md:w-1/2"
        />

        <BaseInput
          label="State/Province"
          name={["contactInfo", "state"]}
          placeholder="Enter your state/province"
          className="md:w-1/2"
        />
      </div>

      <div className="flex flex-col md:flex-row gap-4">
        <BaseInput
          label="Zip Code"
          name={["contactInfo", "zipCode"]}
          placeholder="Enter your zip code"
          className="md:w-1/2"
        />

        <BaseInput
          label="Country"
          name={["contactInfo", "country"]}
          placeholder="Enter your country"
          className="md:w-1/2"
        />
      </div>

      <BaseInput
        label="LinkedIn URL"
        name={["contactInfo", "linkedInUrl"]}
        placeholder="https://linkedin.com/in/username"
      />

      <BaseInput
        label="Portfolio URL"
        name={["contactInfo", "portfolioUrl"]}
        placeholder="https://yourportfolio.com"
      />
    </>
  );
};

export default ContactInfoTab;
