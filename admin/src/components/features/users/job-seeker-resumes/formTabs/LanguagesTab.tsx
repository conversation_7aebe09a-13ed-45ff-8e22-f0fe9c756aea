import React from "react";
import { Button, Form, Input, Select } from "antd";
import { DeleteOutlined, PlusOutlined } from "@ant-design/icons";

const LANGUAGE_LEVELS = [
  "Native",
  "Fluent",
  "Advanced",
  "Intermediate",
  "Basic",
];

const LanguagesTab: React.FC = () => {
  return (
    <Form.List name="languages">
      {(fields, { add, remove }) => (
        <>
          {fields.map(({ key, name, ...restField }) => (
            <div
              key={key}
              className="border border-gray-300 p-4 rounded-md mb-4"
            >
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-base font-medium">Language #{name + 1}</h3>
                {fields.length > 1 && (
                  <Button
                    danger
                    type="text"
                    onClick={() => remove(name)}
                    icon={<DeleteOutlined />}
                  />
                )}
              </div>

              <div className="flex flex-col md:flex-row gap-4">
                <Form.Item
                  {...restField}
                  label="Language"
                  name={[name, "language"]}
                  rules={[
                    { required: true, message: "Please enter language name" },
                  ]}
                  className="md:w-1/2"
                >
                  <Input placeholder="e.g., English, Vietnamese, Japanese" />
                </Form.Item>

                <Form.Item
                  {...restField}
                  label="Level"
                  name={[name, "level"]}
                  rules={[
                    { required: true, message: "Please select language level" },
                  ]}
                  className="md:w-1/2"
                >
                  <Select placeholder="Select proficiency level">
                    {LANGUAGE_LEVELS.map((level) => (
                      <Select.Option key={level} value={level}>
                        {level}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </div>
            </div>
          ))}

          <Form.Item>
            <Button
              type="dashed"
              onClick={() => add()}
              block
              icon={<PlusOutlined />}
            >
              Add Language
            </Button>
          </Form.Item>
        </>
      )}
    </Form.List>
  );
};

export default LanguagesTab;
