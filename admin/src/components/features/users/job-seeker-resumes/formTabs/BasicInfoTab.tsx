import React from "react";
import BaseInput from "@/components/ui/inputs/BaseInput";
import BaseTextArea from "@/components/ui/inputs/BaseTextArea";
import BaseCheckbox from "@/components/ui/checkboxes/BaseCheckbox";
import DebounceSelect from "@/components/ui/selects/DebounceSelect";
import UserService from "@/services/userService";
import { Form } from "antd";

interface BasicInfoTabProps {
  userSelectable?: boolean;
}

const fetchUserOptions = async (search: string) => {
  const res = await UserService.getList({ search });
  return { data: res.data };
};

const BasicInfoTab: React.FC<BasicInfoTabProps> = ({ userSelectable }) => {
  return (
    <>
      {userSelectable && (
        <DebounceSelect
          name="userId"
          label="User"
          required
          fetchOptions={fetchUserOptions}
          placeholder="Search and select a user..."
        />
      )}
      <BaseInput
        label="Resume Name"
        name="name"
        required
        placeholder="e.g., Main Resume, Technical Resume, etc."
      />

      <BaseTextArea
        label="Resume Summary/Description"
        name="description"
        placeholder="Brief summary of your qualifications and career objectives"
        rows={4}
      />

      <Form.Item
        label="Skills"
        name="skills"
        required
        rules={[
          { required: true, message: "Please enter at least one skill" },
          {
            transform: (value) => {
              if (!value) return [];
              return value
                .split(",")
                .map((skill: string) => skill.trim())
                .filter(Boolean);
            },
          },
        ]}
        getValueFromEvent={(e) => e.target.value}
        getValueProps={(value) => ({
          value: Array.isArray(value) ? value.join(", ") : value,
        })}
      >
        <BaseInput
          placeholder="e.g., JavaScript, React, Node.js"
          helpText="Enter your skills separated by commas. Example: JavaScript, React, Node.js"
        />
      </Form.Item>

      <BaseCheckbox
        name="isActive"
        checkboxLabel="Set as active resume"
        helpText="Only one resume can be active at a time. The active resume is used for job applications."
      />
    </>
  );
};

export default BasicInfoTab;
