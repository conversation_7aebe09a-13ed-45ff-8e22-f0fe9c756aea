import React from "react";
import { Button, Form, Input, DatePicker } from "antd";
import { DeleteOutlined, PlusOutlined } from "@ant-design/icons";

const EducationTab: React.FC = () => {
  return (
    <Form.List name="educations">
      {(fields, { add, remove }) => (
        <>
          {fields.map(({ key, name, ...restField }) => (
            <div
              key={key}
              className="border border-gray-300 p-4 rounded-md mb-4"
            >
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-base font-medium">Education #{name + 1}</h3>
                {fields.length > 1 && (
                  <Button
                    danger
                    type="text"
                    onClick={() => remove(name)}
                    icon={<DeleteOutlined />}
                  />
                )}
              </div>

              <Form.Item
                {...restField}
                label="Institution"
                name={[name, "institution"]}
                rules={[
                  { required: true, message: "Please enter institution name" },
                ]}
              >
                <Input placeholder="Name of institution" />
              </Form.Item>

              <Form.Item
                {...restField}
                label="Degree"
                name={[name, "degree"]}
                rules={[{ required: true, message: "Please enter degree" }]}
              >
                <Input placeholder="e.g., Bachelor of Science, Master's Degree" />
              </Form.Item>

              <Form.Item
                {...restField}
                label="Field of Study"
                name={[name, "fieldOfStudy"]}
                rules={[
                  { required: true, message: "Please enter field of study" },
                ]}
              >
                <Input placeholder="e.g., Computer Science, Business Administration" />
              </Form.Item>

              <div className="flex flex-col md:flex-row gap-4">
                <Form.Item
                  {...restField}
                  label="Start Date"
                  name={[name, "startDate"]}
                  rules={[
                    { required: true, message: "Please select start date" },
                  ]}
                  className="md:w-1/2"
                >
                  <DatePicker
                    placeholder="Select start date"
                    format="YYYY-MM-DD"
                    className="w-full"
                  />
                </Form.Item>

                <Form.Item
                  {...restField}
                  label="End Date"
                  name={[name, "endDate"]}
                  className="md:w-1/2"
                >
                  <DatePicker
                    placeholder="Select end date (leave blank if still studying)"
                    format="YYYY-MM-DD"
                    className="w-full"
                  />
                </Form.Item>
              </div>

              <Form.Item
                {...restField}
                label="Description"
                name={[name, "description"]}
              >
                <Input.TextArea
                  placeholder="Describe your achievements, GPA, relevant coursework, etc."
                  rows={3}
                />
              </Form.Item>
            </div>
          ))}

          <Form.Item>
            <Button
              type="dashed"
              onClick={() => add()}
              block
              icon={<PlusOutlined />}
            >
              Add Education
            </Button>
          </Form.Item>
        </>
      )}
    </Form.List>
  );
};

export default EducationTab;
