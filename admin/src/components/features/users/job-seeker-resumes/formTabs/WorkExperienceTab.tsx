import React from "react";
import { Button, Form, Input, DatePicker } from "antd";
import { DeleteOutlined, PlusOutlined } from "@ant-design/icons";

const WorkExperienceTab: React.FC = () => {
  return (
    <Form.List name="workExperiences">
      {(fields, { add, remove }) => (
        <>
          {fields.map(({ key, name, ...restField }) => (
            <div
              key={key}
              className="border border-gray-300 p-4 rounded-md mb-4"
            >
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-base font-medium">
                  Work Experience #{name + 1}
                </h3>
                {fields.length > 1 && (
                  <Button
                    danger
                    type="text"
                    onClick={() => remove(name)}
                    icon={<DeleteOutlined />}
                  />
                )}
              </div>

              <Form.Item
                {...restField}
                label="Company"
                name={[name, "company"]}
                rules={[
                  { required: true, message: "Please enter company name" },
                ]}
              >
                <Input placeholder="Company name" />
              </Form.Item>

              <Form.Item
                {...restField}
                label="Position"
                name={[name, "position"]}
                rules={[{ required: true, message: "Please enter position" }]}
              >
                <Input placeholder="Job title" />
              </Form.Item>

              <div className="flex flex-col md:flex-row gap-4">
                <Form.Item
                  {...restField}
                  label="Start Date"
                  name={[name, "startDate"]}
                  rules={[
                    { required: true, message: "Please select start date" },
                  ]}
                  className="md:w-1/2"
                >
                  <DatePicker
                    placeholder="Select start date"
                    format="YYYY-MM-DD"
                    className="w-full"
                  />
                </Form.Item>

                <Form.Item
                  {...restField}
                  label="End Date"
                  name={[name, "endDate"]}
                  className="md:w-1/2"
                >
                  <DatePicker
                    placeholder="Select end date (leave blank for current job)"
                    format="YYYY-MM-DD"
                    className="w-full"
                  />
                </Form.Item>
              </div>

              <Form.Item
                {...restField}
                label="Description"
                name={[name, "description"]}
              >
                <Input.TextArea
                  placeholder="Describe your responsibilities and achievements"
                  rows={3}
                />
              </Form.Item>
            </div>
          ))}

          <Form.Item>
            <Button
              type="dashed"
              onClick={() => add()}
              block
              icon={<PlusOutlined />}
            >
              Add Work Experience
            </Button>
          </Form.Item>
        </>
      )}
    </Form.List>
  );
};

export default WorkExperienceTab;
