import React from "react";
import { Form } from "antd";
import BaseInput from "@/components/ui/inputs/BaseInput";
import BaseTextArea from "@/components/ui/inputs/BaseTextArea";
import BaseCheckbox from "@/components/ui/checkboxes/BaseCheckbox";
import BaseSelect from "@/components/ui/selects/BaseSelect";

const DAYS_OF_WEEK = [
  "MONDAY",
  "TUESDAY",
  "WEDNESDAY",
  "THURSDAY",
  "FRIDAY",
  "SATURDAY",
  "SUNDAY",
];

const TIME_SLOTS = ["MORNING", "AFTERNOON", "EVENING", "NIGHT"];

const JOB_TYPES = ["REMOTE", "HYBRID", "ONSITE", "FREELANCE"];

const PartTimePreferencesTab: React.FC = () => {
  return (
    <>
      <BaseCheckbox
        name={["partTime", "isPartTime"]}
        checkboxLabel="This is a part-time resume"
        helpText="Enable this if you're seeking part-time opportunities"
      />
      <Form.Item
        noStyle
        shouldUpdate={(prev, curr) =>
          prev.partTime?.isPartTime !== curr.partTime?.isPartTime
        }
      >
        {(form) =>
          form.getFieldValue(["partTime", "isPartTime"]) ? (
            <div className="space-y-4 mt-4">
              <BaseInput
                label="Minimum Hourly Rate (VND)"
                name={["partTimePreference", "minHourlyRate"]}
                type="number"
                min={0}
                placeholder="e.g., 500000"
                required
              />

              <BaseInput
                label="Maximum Hours per Week"
                name={["partTimePreference", "maxHoursPerWeek"]}
                type="number"
                min={1}
                max={40}
                required
                placeholder="e.g., 30"
              />

              <BaseSelect
                label="Available Days"
                name={["partTimePreference", "availableDays"]}
                mode="multiple"
                placeholder="Select available days"
                options={DAYS_OF_WEEK.map((day) => ({
                  label: day,
                  value: day,
                }))}
              />

              <BaseSelect
                label="Available Time Slots"
                name={["partTimePreference", "availableTimeSlots"]}
                mode="multiple"
                placeholder="Select available time slots"
                options={TIME_SLOTS.map((slot) => ({
                  label: slot,
                  value: slot,
                }))}
              />

              <BaseSelect
                label="Preferred Job Types"
                name={["partTimePreference", "preferredJobTypes"]}
                mode="multiple"
                placeholder="Select preferred job types"
                options={JOB_TYPES.map((type) => ({
                  label: type,
                  value: type,
                }))}
              />

              <BaseSelect
                label="Preferred Locations"
                name={["partTimePreference", "preferredLocations"]}
                mode="tags"
                placeholder="Enter preferred locations (e.g., Ho Chi Minh City, Da Nang)"
                options={[]}
              />

              <BaseCheckbox
                name={["partTimePreference", "remoteOnly"]}
                checkboxLabel="Remote work only"
              />

              <BaseInput
                label="Maximum Travel Distance (km)"
                name={["partTimePreference", "maxTravelDistance"]}
                type="number"
                min={0}
                placeholder="e.g., 20"
              />

              <BaseCheckbox
                name={["partTimePreference", "isStudent"]}
                checkboxLabel="I am currently a student"
              />

              <Form.Item
                noStyle
                shouldUpdate={(prev, curr) =>
                  prev.partTimePreference?.isStudent !==
                  curr.partTimePreference?.isStudent
                }
              >
                {(form) =>
                  form.getFieldValue(["partTimePreference", "isStudent"]) ? (
                    <BaseInput
                      label="Study Major"
                      name={["partTimePreference", "studyMajor"]}
                      placeholder="e.g., Computer Science"
                    />
                  ) : null
                }
              </Form.Item>

              <BaseTextArea
                label="Additional Notes"
                name="partTimePreference.additionalNotes"
                placeholder="Any additional information regarding your part-time work preferences"
                rows={4}
              />
            </div>
          ) : null
        }
      </Form.Item>
    </>
  );
};

export default PartTimePreferencesTab;
