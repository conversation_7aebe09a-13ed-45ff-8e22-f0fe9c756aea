import React from "react";
import { Space } from "antd";
import BasicInfo from "./BasicInfo";
import ContactInfo from "./ContactInfo";
import Skills from "./Skills";
import Languages from "./Languages";
import WorkExperience from "./WorkExperience";
import Education from "./Education";
import PartTimePreferences from "./PartTimePreferences";

interface ProfileViewProps {
  // Basic Information
  basicInfo: {
    name: string;
    isActive: boolean;
    description?: string;
  };
  
  // Contact Information (optional)
  contactInfo?: {
    phoneNumber?: string;
    phone?: string;
    email?: string;
    address?: string;
    linkedInUrl?: string;
    portfolioUrl?: string;
  };
  
  // Skills (optional)
  skills?: string[];
  
  // Languages (optional)
  languages?: Array<{
    language: string;
    level: string;
  }>;
  
  // Work Experience (optional)
  workExperiences?: Array<{
    position: string;
    company: string;
    startDate: string;
    endDate?: string;
    description?: string;
  }>;
  
  // Education (optional)
  educations?: Array<{
    degree: string;
    institution?: string;
    school?: string;
    startDate: string;
    endDate?: string;
    fieldOfStudy?: string;
    major?: string;
    description?: string;
  }>;
  
  // Part-time Preferences (optional)
  partTimePreference?: {
    minHourlyRate?: number;
    maxHoursPerWeek?: number;
    availableDays?: string[];
    availableTimeSlots?: string[];
    preferredJobTypes?: string[];
    remoteOnly?: boolean;
    maxTravelDistance?: string | number;
    isStudent?: boolean;
    studyMajor?: string;
    additionalNotes?: string;
  };
  
  // Layout options
  showHeader?: boolean;
  showContactInHeader?: boolean;
  showSkillsInHeader?: boolean;
  className?: string;
}

const ProfileView: React.FC<ProfileViewProps> = ({
  basicInfo,
  contactInfo,
  skills,
  languages,
  workExperiences,
  educations,
  partTimePreference,
  showHeader = true,
  showContactInHeader = true,
  showSkillsInHeader = true,
  className,
}) => {
  return (
    <Space
      direction="vertical"
      size="large"
      style={{ width: "100%" }}
      className={className}
    >
      {/* Header Section */}
      {showHeader && (
        <div>
          <BasicInfo basicInfo={basicInfo} />
          {showContactInHeader && contactInfo && (
            <div className="mt-4">
              <ContactInfo contactInfo={contactInfo} />
            </div>
          )}
          {showSkillsInHeader && skills && (
            <div className="mt-4">
              <Skills skills={skills} />
            </div>
          )}
        </div>
      )}

      {/* Skills Section (if not shown in header) */}
      {!showSkillsInHeader && skills && (
        <Skills skills={skills} showAsCard={true} />
      )}

      {/* Contact Info Section (if not shown in header) */}
      {!showContactInHeader && contactInfo && (
        <ContactInfo contactInfo={contactInfo} showTitle={true} />
      )}

      {/* Languages Section */}
      {languages && <Languages languages={languages} />}

      {/* Work Experience Section */}
      {workExperiences && <WorkExperience workExperiences={workExperiences} />}

      {/* Education Section */}
      {educations && <Education educations={educations} />}

      {/* Part-time Preferences Section */}
      {partTimePreference && (
        <PartTimePreferences partTimePreference={partTimePreference} />
      )}
    </Space>
  );
};

export default ProfileView;
