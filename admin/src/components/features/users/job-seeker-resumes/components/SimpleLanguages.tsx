import React from "react";
import { Tag, Card, Typography, Space } from "antd";
import { GlobalOutlined } from "@ant-design/icons";
import { useTranslations } from "next-intl";

const { Title, Text } = Typography;

interface SimpleLanguagesProps {
  languages?: string[];
}

const SimpleLanguages: React.FC<SimpleLanguagesProps> = ({ languages }) => {
  const t = useTranslations("resume");

  return (
    <Card
      title={
        <Title level={4} style={{ margin: 0 }}>
          <GlobalOutlined /> {t("languages")}
        </Title>
      }
    >
      {languages && languages.length > 0 ? (
        <Space wrap>
          {languages.map((language, idx) => (
            <Tag
              key={idx}
              color="blue"
              style={{ fontSize: "13px", padding: "6px 12px" }}
            >
              {language}
            </Tag>
          ))}
        </Space>
      ) : (
        <Text type="secondary">{t("no_languages")}</Text>
      )}
    </Card>
  );
};

export default SimpleLanguages;
