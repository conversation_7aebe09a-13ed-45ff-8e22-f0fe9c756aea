import React from "react";
import { Tag, Row, Col, Typography, Space, Avatar } from "antd";
import { UserOutlined } from "@ant-design/icons";
import { useTranslations } from "next-intl";

const { Title, Paragraph } = Typography;

interface BasicInfoProps {
  basicInfo: {
    name: string;
    isActive: boolean;
    description?: string;
  };
  showAvatar?: boolean;
}

const BasicInfo: React.FC<BasicInfoProps> = ({
  basicInfo,
  showAvatar = true,
}) => {
  const t = useTranslations("resume");

  return (
    <Row gutter={[16, 16]} align="middle">
      {showAvatar && (
        <Col xs={6} sm={4}>
          <Avatar size={80} icon={<UserOutlined />} />
        </Col>
      )}
      <Col xs={showAvatar ? 18 : 24} sm={showAvatar ? 20 : 24}>
        <Space direction="vertical" size="small" style={{ width: "100%" }}>
          <Title level={2} style={{ margin: 0 }}>
            {basicInfo.name}
          </Title>
          <Space>
            <Tag
              color={basicInfo.isActive ? "green" : "orange"}
              style={{ fontSize: "14px", padding: "4px 12px" }}
            >
              {basicInfo.isActive ? t("active_resume") : t("inactive")}
            </Tag>
          </Space>
          <Paragraph style={{ margin: 0, fontSize: "16px" }}>
            {basicInfo.description || t("no_description")}
          </Paragraph>
        </Space>
      </Col>
    </Row>
  );
};

export default BasicInfo;
