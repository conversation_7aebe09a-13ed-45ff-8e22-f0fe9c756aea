# Generic Profile Components

This directory contains reusable, generic components for displaying profile information. These components have been extracted from the original `ResumeView` component and made generic to allow for better modularity and reusability across different contexts.

## Main Components

### Header

Complete header section with basic info, contact info, and skills.

```tsx
import { Header } from "./components";

<Header
  basicInfo={{
    name: "<PERSON>",
    isActive: true,
    description: "Software Developer",
  }}
  contactInfo={{
    email: "<EMAIL>",
    phone: "+1234567890",
    address: "New York, NY",
  }}
  skills={["React", "TypeScript", "Node.js"]}
/>;
```

### WorkExperience

Displays work experience section.

```tsx
import { WorkExperience } from "./components";

<WorkExperience
  workExperiences={[
    {
      position: "Senior Developer",
      company: "Tech Corp",
      startDate: "2020-01-01",
      endDate: "2023-12-31",
      description: "Led development team...",
    },
  ]}
/>;
```

### Education

Displays education section.

```tsx
import { Education } from "./components";

<Education
  educations={[
    {
      degree: "Bachelor of Computer Science",
      institution: "University of Technology",
      startDate: "2016-09-01",
      endDate: "2020-06-30",
      major: "Computer Science",
    },
  ]}
/>;
```

### Languages

Displays languages section.

```tsx
import { Languages } from "./components";

<Languages
  languages={[
    { language: "English", level: "Native" },
    { language: "Spanish", level: "Intermediate" },
  ]}
/>;
```

### PartTimePreferences

Displays part-time preferences section (only shows if data exists).

```tsx
import { PartTimePreferences } from "./components";

<PartTimePreferences
  partTimePreference={{
    minHourlyRate: 25,
    maxHoursPerWeek: 20,
    availableDays: ["Monday", "Tuesday", "Wednesday"],
    remoteOnly: true,
  }}
/>;
```

### ProfileView

Complete profile view component that combines all sections with flexible layout options.

```tsx
import { ProfileView } from "./components";

<ProfileView
  basicInfo={{
    name: "John Doe",
    isActive: true,
    description: "Software Developer",
  }}
  contactInfo={{
    email: "<EMAIL>",
    phone: "+1234567890",
  }}
  skills={["React", "TypeScript"]}
  languages={[{ language: "English", level: "Native" }]}
  workExperiences={[
    {
      position: "Senior Developer",
      company: "Tech Corp",
      startDate: "2020-01-01",
      description: "Led development team...",
    },
  ]}
  showHeader={true}
  showContactInHeader={true}
  showSkillsInHeader={true}
/>;
```

## Sub-Components (for granular reuse)

### BasicInfo

Basic information with name, status, and description.

```tsx
import { BasicInfo } from "./components";

<BasicInfo
  basicInfo={{
    name: "John Doe",
    isActive: true,
    description: "Software Developer",
  }}
  showAvatar={true}
/>;
```

### ContactInfo

Contact information section.

```tsx
import { ContactInfo } from "./components";

<ContactInfo
  contactInfo={{
    email: "<EMAIL>",
    phone: "+1234567890",
    address: "New York, NY",
    linkedInUrl: "https://linkedin.com/in/johndoe",
    portfolioUrl: "https://johndoe.dev",
  }}
  showTitle={true}
/>;
```

### Skills

Skills section that can be displayed as inline or as a card.

```tsx
import { Skills } from "./components";

<Skills skills={["React", "TypeScript", "Node.js"]} showAsCard={false} />;
```

## Usage Examples

### Full Profile View

```tsx
import {
  Header,
  WorkExperience,
  Education,
  Languages,
  PartTimePreferences,
} from "./components";

const MyProfileView = ({ profileData }) => (
  <Space direction="vertical" size="large">
    <Header
      basicInfo={{
        name: profileData.name,
        isActive: profileData.isActive,
        description: profileData.description,
      }}
      contactInfo={profileData.contactInfo}
      skills={profileData.skills}
    />
    <WorkExperience workExperiences={profileData.workExperiences} />
    <Education educations={profileData.educations} />
    <Languages languages={profileData.languages} />
    <PartTimePreferences partTimePreference={profileData.partTimePreference} />
  </Space>
);
```

### Custom Layout with Sub-Components

```tsx
import { BasicInfo, ContactInfo, Skills } from "./components";

const CompactProfileCard = ({ profileData }) => (
  <Card>
    <Row gutter={16}>
      <Col span={12}>
        <BasicInfo
          basicInfo={{
            name: profileData.name,
            isActive: profileData.isActive,
            description: profileData.description,
          }}
          showAvatar={false}
        />
      </Col>
      <Col span={12}>
        <ContactInfo contactInfo={profileData.contactInfo} showTitle={false} />
      </Col>
    </Row>
    <Skills skills={profileData.skills} showAsCard={false} />
  </Card>
);
```

### Skills Only Component

```tsx
import { Skills } from "./components";

const SkillsCard = ({ skills }) => <Skills skills={skills} showAsCard={true} />;
```

## Props Reference

### Header

- `basicInfo: { name: string; isActive: boolean; description?: string }` - Basic profile information
- `contactInfo?: { phoneNumber?: string; phone?: string; email?: string; address?: string; linkedInUrl?: string; portfolioUrl?: string }` - Contact information object
- `skills?: string[]` - Array of skills

### WorkExperience

- `workExperiences?: Array<{ position: string; company: string; startDate: string; endDate?: string; description?: string }>` - Array of work experiences

### Education

- `educations?: Array<{ degree: string; institution?: string; school?: string; startDate: string; endDate?: string; fieldOfStudy?: string; major?: string; description?: string }>` - Array of education entries

### Languages

- `languages?: Array<{ language: string; level: string }>` - Array of language skills

### PartTimePreferences

- `partTimePreference?: { minHourlyRate?: number; maxHoursPerWeek?: number; availableDays?: string[]; availableTimeSlots?: string[]; preferredJobTypes?: string[]; remoteOnly?: boolean; maxTravelDistance?: string | number; isStudent?: boolean; studyMajor?: string; additionalNotes?: string }` - Part-time preferences object

### BasicInfo

- `basicInfo: { name: string; isActive: boolean; description?: string }` - Basic profile information
- `showAvatar?: boolean` - Whether to show avatar (default: true)

### ContactInfo

- `contactInfo?: { phoneNumber?: string; phone?: string; email?: string; address?: string; linkedInUrl?: string; portfolioUrl?: string }` - Contact information object
- `showTitle?: boolean` - Whether to show section title (default: true)

### Skills

- `skills?: string[]` - Array of skills
- `showAsCard?: boolean` - Whether to wrap in Card component (default: false)

## Legacy Support

For backward compatibility, the old component names are still exported:

- `ResumeHeader` → `Header`
- `ResumeWorkExperience` → `WorkExperience`
- `ResumeEducation` → `Education`
- `ResumeLanguages` → `Languages`
- `ResumePartTimePreferences` → `PartTimePreferences`
- `ResumeBasicInfo` → `BasicInfo`
- `ResumeContactInfo` → `ContactInfo`
- `ResumeSkills` → `Skills`

However, it's recommended to use the new generic names for better reusability.

## Data Adapters

To make it easier to use these components with different data structures, we provide adapter functions in `@/utils/profileAdapters`:

### jobSeekerPostToProfileView

Converts JobSeekerPost data to ProfileView props:

```tsx
import { ProfileView } from "./components";
import { jobSeekerPostToProfileView } from "@/utils/profileAdapters";

const JobSeekerPostDetail = ({ post }) => (
  <ProfileView {...jobSeekerPostToProfileView(post)} showHeader={false} />
);
```

### resumeToProfileView

Converts Resume data to ProfileView props:

```tsx
import { ProfileView } from "./components";
import { resumeToProfileView } from "@/utils/profileAdapters";

const ResumeDetail = ({ resume }) => (
  <ProfileView {...resumeToProfileView(resume)} />
);
```

### genericToProfileView

Generic adapter for any object with profile-like data:

```tsx
import { ProfileView } from "./components";
import { genericToProfileView } from "@/utils/profileAdapters";

const GenericProfile = ({ data }) => (
  <ProfileView {...genericToProfileView(data)} />
);
```

## Best Practices

1. **Use ProfileView for complete profile displays** - It handles layout and spacing automatically
2. **Use individual components for custom layouts** - Mix and match as needed
3. **Use adapters for different data sources** - Convert your data structure to component props
4. **Leverage the showHeader options** - Control which sections appear in the header vs. separate cards
5. **Consider i18n** - All components support internationalization out of the box
