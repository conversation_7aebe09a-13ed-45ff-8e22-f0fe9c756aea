import React from "react";
import { Tag, Card, Typography, Space } from "antd";
import { TrophyOutlined } from "@ant-design/icons";
import { useTranslations } from "next-intl";

const { Title, Text } = Typography;

interface SkillsProps {
  skills?: string[];
  showAsCard?: boolean;
}

const Skills: React.FC<SkillsProps> = ({ skills, showAsCard = false }) => {
  const t = useTranslations("resume");

  const skillsContent = (
    <>
      <Title level={4} style={{ marginBottom: 12 }}>
        <TrophyOutlined /> {t("skills")}
      </Title>
      {skills && skills.length > 0 ? (
        <Space wrap>
          {skills.map((skill, index) => (
            <Tag
              key={index}
              color="blue"
              style={{ fontSize: "13px", padding: "6px 12px" }}
            >
              {skill}
            </Tag>
          ))}
        </Space>
      ) : (
        <Text type="secondary">{t("no_skills")}</Text>
      )}
    </>
  );

  if (showAsCard) {
    return <Card>{skillsContent}</Card>;
  }

  return <div style={{ marginTop: 24 }}>{skillsContent}</div>;
};

export default Skills;
