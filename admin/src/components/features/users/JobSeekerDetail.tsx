import { ExtendedUser } from "@/types/user";
import { utils } from "@/utils";
import { Tag } from "antd";
import BaseDescription, {
  DescriptionItem,
} from "@/components/ui/descriptions/BaseDescription";
import JobSeekerResumes from "@/components/features/users/job-seeker-resumes/JobSeekerResumes";

interface JobSeekerDetailProps {
  user: ExtendedUser;
}

const JobSeekerDetail = ({ user }: JobSeekerDetailProps) => {
  const professionalInfoItems: DescriptionItem[] = [
    {
      label: "Gender",
      value: user.gender
        ? user.gender.charAt(0).toUpperCase() + user.gender.slice(1)
        : null,
    },
    {
      label: "Birth Date",
      value: user.dateOfBirth ? utils.formatBirthDate(user.dateOfBirth) : null,
    },
    {
      label: "Skills",
      value:
        user.skills && user.skills.length > 0 ? (
          <div>
            {user.skills.map((skill, index) => (
              <Tag key={index} color="blue">
                {skill}
              </Tag>
            ))}
          </div>
        ) : null,
    },
    {
      label: "Interests",
      value:
        user.interests && user.interests.length > 0 ? (
          <div>
            {user.interests.map((interest, index) => (
              <Tag key={index} color="purple">
                {interest}
              </Tag>
            ))}
          </div>
        ) : null,
    },
    {
      label: "ID Verification",
      value: (
        <Tag color={user.isPhoneVerified ? "green" : "orange"}>
          {user.isPhoneVerified ? "Verified" : "Not Verified"}
        </Tag>
      ),
    },
  ];

  return (
    <>
      <div className="mb-6">
        <BaseDescription
          title="Professional Information"
          items={professionalInfoItems}
          bordered
          column={1}
          layout="horizontal"
        />
      </div>
      <JobSeekerResumes userId={user.id} userInfo={user} />
    </>
  );
};

export default JobSeekerDetail;
