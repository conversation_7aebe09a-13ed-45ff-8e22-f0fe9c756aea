import EmployerDetail from "@/components/features/users/EmployerDetail";
import JobSeekerDetail from "@/components/features/users/JobSeekerDetail";
import { ExtendedUser } from "@/types/user";
import { UserRole } from "@/constants/userRole";
import React from "react";

interface UserProfileTabProps {
  user: ExtendedUser;
}

const UserProfileTab: React.FC<UserProfileTabProps> = ({ user }) => {
  return user.role === UserRole.EMPLOYER ? (
    <EmployerDetail user={user} />
  ) : (
    <JobSeekerDetail user={user} />
  );
};

export default UserProfileTab;
