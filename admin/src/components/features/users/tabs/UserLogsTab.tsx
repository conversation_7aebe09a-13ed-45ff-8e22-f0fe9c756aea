import React from "react";
import { Card, Table } from "antd";
import { utils } from "@/utils";
import { ExtendedUser } from "@/types/user";
import { loginHistoryData } from "@/mocks/user";

interface UserLogsTabProps {
  user: ExtendedUser;
}

const UserLogsTab: React.FC<UserLogsTabProps> = ({ user }) => {
  console.log("🚀 ~ user:", user);
  // Log columns
  const logColumns = [
    {
      title: "Date & Time",
      dataIndex: "timestamp",
      key: "timestamp",
      render: (text: string) => utils.formatTableDate(text),
    },
    {
      title: "Activity",
      dataIndex: "activity",
      key: "activity",
    },
    {
      title: "IP Address",
      dataIndex: "ipAddress",
      key: "ipAddress",
    },
    {
      title: "Device",
      dataIndex: "device",
      key: "device",
    },
  ];

  return (
    <Card variant="outlined" title="Login History">
      <Table
        columns={logColumns}
        dataSource={loginHistoryData}
        pagination={{ pageSize: 10 }}
      />
    </Card>
  );
};

export default UserLogsTab;
