import { ExtendedUser } from "@/types/user";
import { utils } from "@/utils";
import { Tag } from "antd";
import BaseDescription, {
  DescriptionItem,
} from "@/components/ui/descriptions/BaseDescription";

interface EmployerDetailProps {
  user: ExtendedUser;
}

const EmployerDetail = ({ user }: EmployerDetailProps) => {
  // Extract company data from user if available
  const companyName = user.name; // Assuming company name is stored in user name for employers
  const industry = "N/A"; // Not in ExtendedUser, add if available
  const companySize = "N/A"; // Not in ExtendedUser, add if available

  // Create description items array
  const companyInfoItems: DescriptionItem[] = [
    {
      label: "Company Name",
      value: companyName,
    },
    {
      label: "Industry",
      value: industry,
    },
    {
      label: "Company Size",
      value: companySize,
    },
    {
      label: "Business License",
      value: <Tag color="orange">Not Verified</Tag>,
    },
    {
      label: "Company Address",
      value: utils.formatAddress(user.address),
    },
    {
      label: "Registration Date",
      value: new Date(user.createdAt).toLocaleDateString(),
    },
  ];

  return (
    <>
      <div className="mb-6">
        <BaseDescription
          title="Company Information"
          items={companyInfoItems}
          bordered
        />
      </div>
    </>
  );
};

export default EmployerDetail;
