// components/features/notifications/NotificationDetailModal.tsx
"use client";

import BaseDescription, {
  DescriptionItem,
} from "@/components/ui/descriptions/BaseDescription";
import {
  NOTIFICATION_CHANNELS,
  NOTIFICATION_STATUSES,
  NOTIFICATION_TYPES,
  getChannelIcon,
  getStatusColor,
} from "@/constants/notifications";
import { useNotification } from "@/contexts/NotiContext";
import NotificationService from "@/services/notificationService";
import { Notification } from "@/types/notifications";
import {
  CalendarOutlined,
  CloseOutlined,
  EyeOutlined,
  MessageOutlined,
  RedoOutlined,
  UserOutlined,
} from "@ant-design/icons";
import { Al<PERSON>, Button, Divider, Flex, Modal, Tag, Typography } from "antd";
import React, { useState } from "react";

const { Title, Text, Paragraph } = Typography;

interface NotificationDetailModalProps {
  notification: Notification | null;
  visible: boolean;
  onClose: () => void;
  onUpdate?: () => void;
}

const NotificationDetailModal: React.FC<NotificationDetailModalProps> = ({
  notification,
  visible,
  onClose,
  onUpdate,
}) => {
  const [loading, setLoading] = useState(false);
  const notificationContext = useNotification();

  if (!notification) return null;

  // Handle resend notification
  const handleResend = async () => {
    try {
      setLoading(true);
      await NotificationService.resend(notification.id);
      notificationContext.notifySuccess("Notification resent successfully");
      onUpdate?.();
    } catch (error) {
      console.log("🚀 ~ handleResend ~ error:", error);
      notificationContext.notifyError("Failed to resend notification");
    } finally {
      setLoading(false);
    }
  };

  // Handle cancel notification
  const handleCancel = async () => {
    try {
      setLoading(true);
      await NotificationService.cancel(notification.id);
      notificationContext.notifySuccess("Notification cancelled successfully");
      onUpdate?.();
      onClose();
    } catch (error) {
      console.log("🚀 ~ handleCancel ~ error:", error);
      notificationContext.notifyError("Failed to cancel notification");
    } finally {
      setLoading(false);
    }
  };

  // Get type and channel labels
  const typeConfig = NOTIFICATION_TYPES.find(
    (t) => t.value === notification.type
  );
  const channelConfig = NOTIFICATION_CHANNELS.find(
    (c) => c.value === notification.channel
  );
  const statusConfig = NOTIFICATION_STATUSES.find(
    (s) => s.value === notification.status
  );

  // Prepare description items
  const basicInfoItems: DescriptionItem[] = [
    {
      label: "Notification ID",
      value: (
        <code className="bg-gray-100 px-2 py-1 rounded-sm text-xs">
          {notification.id}
        </code>
      ),
    },
    {
      label: "Type",
      value: <Tag color="blue">{typeConfig?.label || notification.type}</Tag>,
    },
    {
      label: "Channel",
      value: (
        <Flex align="center" gap="small">
          <span>{getChannelIcon(notification.channel)}</span>
          <span>{channelConfig?.label || notification.channel}</span>
        </Flex>
      ),
    },
    {
      label: "Status",
      value: (
        <Tag color={getStatusColor(notification.status)}>
          {statusConfig?.label || notification.status}
        </Tag>
      ),
    },
  ];

  const userInfoItems: DescriptionItem[] = [
    {
      label: "Recipient Name",
      value: notification.user_name || "N/A",
    },
    {
      label: "Email",
      value: notification.user_email || "N/A",
    },
    {
      label: "User ID",
      value: (
        <code className="bg-gray-100 px-2 py-1 rounded-sm text-xs">
          {notification.user_id}
        </code>
      ),
    },
  ];

  const timestampItems: DescriptionItem[] = [
    {
      label: "Created At",
      value: new Date(notification.created_at).toLocaleString(),
    },
    {
      label: "Sent At",
      value: notification.sent_at
        ? new Date(notification.sent_at).toLocaleString()
        : "Not sent",
    },
    {
      label: "Read At",
      value: notification.read_at
        ? new Date(notification.read_at).toLocaleString()
        : "Not read",
    },
    {
      label: "Clicked At",
      value: notification.clicked_at
        ? new Date(notification.clicked_at).toLocaleString()
        : "Not clicked",
    },
  ];

  // Calculate delivery metrics
  const getDeliveryMetrics = () => {
    const sentTime = notification.sent_at
      ? new Date(notification.sent_at)
      : null;
    const readTime = notification.read_at
      ? new Date(notification.read_at)
      : null;
    const clickTime = notification.clicked_at
      ? new Date(notification.clicked_at)
      : null;

    let timeToRead = null;
    let timeToClick = null;

    if (sentTime && readTime) {
      const diffMinutes = Math.round(
        (readTime.getTime() - sentTime.getTime()) / (1000 * 60)
      );
      timeToRead =
        diffMinutes < 60
          ? `${diffMinutes}m`
          : `${Math.round(diffMinutes / 60)}h`;
    }

    if (sentTime && clickTime) {
      const diffMinutes = Math.round(
        (clickTime.getTime() - sentTime.getTime()) / (1000 * 60)
      );
      timeToClick =
        diffMinutes < 60
          ? `${diffMinutes}m`
          : `${Math.round(diffMinutes / 60)}h`;
    }

    return { timeToRead, timeToClick };
  };

  const { timeToRead, timeToClick } = getDeliveryMetrics();

  // Action buttons based on status
  const getActionButtons = () => {
    const buttons = [];

    if (notification.status === "failed") {
      buttons.push(
        <Button
          key="resend"
          type="primary"
          icon={<RedoOutlined />}
          onClick={handleResend}
          loading={loading}
        >
          Resend
        </Button>
      );
    }

    if (notification.status === "pending") {
      buttons.push(
        <Button
          key="cancel"
          danger
          icon={<CloseOutlined />}
          onClick={handleCancel}
          loading={loading}
        >
          Cancel
        </Button>
      );
    }

    return buttons;
  };

  return (
    <Modal
      title={
        <Flex align="center" gap="middle">
          <span>Notification Details</span>
        </Flex>
      }
      open={visible}
      onCancel={onClose}
      width={800}
      footer={[
        <Button key="close" onClick={onClose}>
          Close
        </Button>,
        ...getActionButtons(),
      ]}
    >
      <div className="space-y-6">
        {/* Status Alert */}
        {notification.status === "failed" && notification.failed_reason && (
          <Alert
            message="Delivery Failed"
            description={notification.failed_reason}
            type="error"
            showIcon
          />
        )}

        {notification.status === "pending" && (
          <Alert
            message="Notification Pending"
            description="This notification is scheduled to be sent."
            type="info"
            showIcon
          />
        )}

        {/* Basic Information */}
        <div>
          <Title level={5}>
            <Flex align="center" gap="small">
              <MessageOutlined />
              Basic Information
            </Flex>
          </Title>
          <BaseDescription items={basicInfoItems} column={2} />
        </div>

        <Divider />

        {/* Recipient Information */}
        <div>
          <Title level={5}>
            <Flex align="center" gap="small">
              <UserOutlined />
              Recipient Information
            </Flex>
          </Title>
          <BaseDescription items={userInfoItems} column={2} />
        </div>

        <Divider />

        {/* Message Content */}
        <div>
          <Title level={5}>Message Content</Title>
          <div className="bg-gray-50 p-4 rounded-sm border">
            <Title level={5} className="mb-2">
              Subject/Title:
            </Title>
            <Text strong className="block mb-3">
              {notification.title}
            </Text>

            <Title level={5} className="mb-2">
              Content:
            </Title>
            <Paragraph className="whitespace-pre-wrap">
              {notification.content}
            </Paragraph>
          </div>
        </div>

        <Divider />

        {/* Timeline Information */}
        <div>
          <Title level={5}>
            <Flex align="center" gap="small">
              <CalendarOutlined />
              Timeline
            </Flex>
          </Title>
          <BaseDescription items={timestampItems} column={2} />
        </div>

        {/* Engagement Metrics */}
        {(timeToRead || timeToClick) && (
          <>
            <Divider />
            <div>
              <Title level={5}>Engagement Metrics</Title>
              <Flex gap="large">
                {timeToRead && (
                  <div className="flex items-center gap-2 p-3 bg-green-50 rounded-sm">
                    <EyeOutlined className="text-green-600" />
                    <div>
                      <div className="text-sm text-gray-600">Time to Read</div>
                      <div className="font-semibold text-green-600">
                        {timeToRead}
                      </div>
                    </div>
                  </div>
                )}
                {timeToClick && (
                  <div className="flex items-center gap-2 p-3 bg-purple-50 rounded-sm">
                    {/* <ClickOutlined className="text-purple-600" /> */}
                    <div>
                      <div className="text-sm text-gray-600">Time to Click</div>
                      <div className="font-semibold text-purple-600">
                        {timeToClick}
                      </div>
                    </div>
                  </div>
                )}
              </Flex>
            </div>
          </>
        )}

        {/* Metadata */}
        {notification.metadata &&
          Object.keys(notification.metadata).length > 0 && (
            <>
              <Divider />
              <div>
                <Title level={5}>Additional Information</Title>
                <div className="bg-gray-50 p-3 rounded-sm">
                  <pre className="text-xs overflow-auto">
                    {JSON.stringify(notification.metadata, null, 2)}
                  </pre>
                </div>
              </div>
            </>
          )}
      </div>
    </Modal>
  );
};

export default NotificationDetailModal;
