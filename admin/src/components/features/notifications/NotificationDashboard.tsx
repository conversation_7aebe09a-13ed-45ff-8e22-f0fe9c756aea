/* eslint-disable react-hooks/exhaustive-deps */
// components/features/notifications/NotificationDashboard.tsx
"use client";

import React, { useEffect, useState } from "react";
import { Card, Col, Row, Statistic, Flex, Typography, Spin, Alert } from "antd";
// import { Line, Column, Pie } from "@ant-design/plots";
import {
  MailOutlined,
  MessageOutlined,
  BellOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  EyeOutlined,
} from "@ant-design/icons";

import { NotificationStats } from "@/types/notifications";
import { useNotification } from "@/contexts/NotiContext";
import NotificationService from "@/services/notificationService";

const { Title } = Typography;

interface AnalyticsData {
  date: string;
  sent: number;
  delivered: number;
  read: number;
  clicked: number;
  failed: number;
}

interface ChannelDistribution {
  channel: string;
  count: number;
  percentage: number;
}

const NotificationDashboard: React.FC = () => {
  const [stats, setStats] = useState<NotificationStats | null>(null);
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData[]>([]);
  const [channelData, setChannelData] = useState<ChannelDistribution[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const notification = useNotification();

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [statsResponse, analyticsResponse] = await Promise.all([
        NotificationService.getStats(),
        NotificationService.getAnalytics("week"),
      ]);

      setStats(statsResponse.data);
      setAnalyticsData(
        (analyticsResponse as { timeline: AnalyticsData[] }).timeline || []
      );
      setChannelData(
        (analyticsResponse as { channels: ChannelDistribution[] }).channels ||
          []
      );
    } catch (err) {
      setError("Failed to load dashboard data");
      notification.notifyError("Failed to load notification dashboard");
      console.error("Dashboard error:", err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message="Error"
        description={error}
        type="error"
        showIcon
        action={
          <button onClick={fetchDashboardData} className="underline">
            Retry
          </button>
        }
      />
    );
  }

  // Line chart configuration for notification trends
  // const lineConfig = {
  //   data: analyticsData,
  //   xField: "date",
  //   yField: "sent",
  //   seriesField: "type",
  //   smooth: true,
  //   animation: {
  //     appear: {
  //       animation: "path-in",
  //       duration: 1000,
  //     },
  //   },
  // };

  // // Column chart for delivery status
  // const columnConfig = {
  //   data: analyticsData,
  //   xField: "date",
  //   yField: "delivered",
  //   color: "#1890ff",
  //   animation: {
  //     appear: {
  //       animation: "grow-in-y",
  //       duration: 1000,
  //     },
  //   },
  // };

  // // Pie chart for channel distribution
  // const pieConfig = {
  //   data: channelData,
  //   angleField: "count",
  //   colorField: "channel",
  //   radius: 0.8,
  //   label: {
  //     type: "outer",
  //     content: "{name} ({percentage}%)",
  //   },
  //   interactions: [
  //     {
  //       type: "element-active",
  //     },
  //   ],
  // };

  return (
    <div className="p-6">
      <Title level={2}>Notification Dashboard</Title>

      {/* Statistics Cards */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Total Sent"
              value={stats?.total_sent || 0}
              prefix={<MailOutlined />}
              valueStyle={{ color: "#1890ff" }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Delivered"
              value={stats?.total_delivered || 0}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: "#52c41a" }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Read"
              value={stats?.total_read || 0}
              prefix={<EyeOutlined />}
              valueStyle={{ color: "#722ed1" }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Failed"
              value={stats?.total_failed || 0}
              prefix={<CloseCircleOutlined />}
              valueStyle={{ color: "#ff4d4f" }}
            />
          </Card>
        </Col>
      </Row>

      {/* Performance Metrics */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="Delivery Rate"
              value={stats?.delivery_rate || 0}
              suffix="%"
              precision={1}
              valueStyle={{
                color:
                  stats?.delivery_rate && stats.delivery_rate > 90
                    ? "#52c41a"
                    : "#faad14",
              }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="Open Rate"
              value={stats?.open_rate || 0}
              suffix="%"
              precision={1}
              valueStyle={{
                color:
                  stats?.open_rate && stats.open_rate > 20
                    ? "#52c41a"
                    : "#faad14",
              }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="Click Rate"
              value={stats?.click_rate || 0}
              suffix="%"
              precision={1}
              // prefix={<ClickOutlined />}
              valueStyle={{
                color:
                  stats?.click_rate && stats.click_rate > 5
                    ? "#52c41a"
                    : "#faad14",
              }}
            />
          </Card>
        </Col>
      </Row>

      {/* Charts */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card
            title="Notification Trends (Last 7 Days)"
            extra={<BellOutlined />}
          >
            {/* <Line {...lineConfig} height={300} /> */}
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="Delivery Performance" extra={<CheckCircleOutlined />}>
            {/* <Column {...columnConfig} height={300} /> */}
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} className="mt-4">
        <Col xs={24} lg={12}>
          <Card title="Channel Distribution" extra={<MessageOutlined />}>
            {/* <Pie {...pieConfig} height={300} /> */}
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="Recent Activity">
            <Flex vertical gap="middle">
              <div className="flex justify-between items-center p-3 bg-blue-50 rounded-sm">
                <span>Welcome emails sent today</span>
                <span className="font-semibold text-blue-600">
                  {analyticsData[analyticsData.length - 1]?.sent || 0}
                </span>
              </div>
              <div className="flex justify-between items-center p-3 bg-green-50 rounded-sm">
                <span>Successful deliveries today</span>
                <span className="font-semibold text-green-600">
                  {analyticsData[analyticsData.length - 1]?.delivered || 0}
                </span>
              </div>
              <div className="flex justify-between items-center p-3 bg-purple-50 rounded-sm">
                <span>Messages read today</span>
                <span className="font-semibold text-purple-600">
                  {analyticsData[analyticsData.length - 1]?.read || 0}
                </span>
              </div>
              <div className="flex justify-between items-center p-3 bg-red-50 rounded-sm">
                <span>Failed deliveries today</span>
                <span className="font-semibold text-red-600">
                  {analyticsData[analyticsData.length - 1]?.failed || 0}
                </span>
              </div>
            </Flex>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default NotificationDashboard;
