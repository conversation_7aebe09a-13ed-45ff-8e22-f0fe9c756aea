// components/features/interviews/InterviewStats.tsx
"use client";
import { INTERVIEW_TYPE_OPTIONS } from "@/constants/interview";
import InterviewService from "@/services/interviewService";
import {
  InterviewStats,
  InterviewStatus,
  InterviewType,
} from "@/types/interview";
import {
  CalendarOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  CloseCircleOutlined,
  EnvironmentOutlined,
  PhoneOutlined,
  ReloadOutlined,
  ScheduleOutlined,
  VideoCameraOutlined,
} from "@ant-design/icons";
import {
  Card,
  Col,
  Flex,
  Progress,
  Row,
  Segmented,
  Spin,
  Statistic,
  Tooltip,
} from "antd";
import React, { useEffect, useState } from "react";

interface InterviewStatsProps {
  period?: "day" | "week" | "month" | "all";
  onRefresh?: () => void;
}

const InterviewStatsComponent: React.FC<InterviewStatsProps> = ({
  period = "month",
  onRefresh,
}) => {
  const [stats, setStats] = useState<InterviewStats | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [selectedPeriod, setSelectedPeriod] = useState<string>(period);

  const fetchStats = async () => {
    setLoading(true);
    try {
      const response = await InterviewService.getInterviewStats({
        period: selectedPeriod,
      });
      setStats(response);
    } catch (error) {
      console.error("Failed to fetch interview stats:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedPeriod]);

  const handleRefresh = () => {
    fetchStats();
    if (onRefresh) onRefresh();
  };

  const handlePeriodChange = (value: string) => {
    setSelectedPeriod(value);
  };

  if (loading) {
    return (
      <Flex align="center" justify="center" className="p-10">
        <Spin size="large" />
      </Flex>
    );
  }

  if (!stats) {
    return (
      <Card title="Interview Statistics" className="mb-6">
        <p>No statistics available.</p>
      </Card>
    );
  }

  // Find the most common interview type
  const mostCommonType = Object.entries(stats.interviewsByType).reduce(
    (max, [type, count]) => (count > max.count ? { type, count } : max),
    { type: "", count: 0 }
  );

  // Find the most common interview status
  const mostCommonStatus = Object.entries(stats.interviewsByStatus).reduce(
    (max, [status, count]) => (count > max.count ? { status, count } : max),
    { status: "", count: 0 }
  );

  // Get the type label
  const getTypeLabel = (typeValue: string) => {
    const type = INTERVIEW_TYPE_OPTIONS.find((t) => t.value === typeValue);
    return type ? type.label : typeValue;
  };

  return (
    <div className="mb-6">
      <Flex justify="space-between" align="center" className="mb-4!">
        <h2 className="text-xl font-semibold m-0">Interview Statistics</h2>
        <Flex gap="small">
          <Segmented
            options={[
              { label: "Day", value: "day" },
              { label: "Week", value: "week" },
              { label: "Month", value: "month" },
              { label: "All Time", value: "all" },
            ]}
            value={selectedPeriod}
            onChange={handlePeriodChange}
          />
          <Tooltip title="Refresh data">
            <ReloadOutlined
              onClick={handleRefresh}
              className="cursor-pointer p-2"
            />
          </Tooltip>
        </Flex>
      </Flex>

      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Total Interviews"
              value={stats.totalInterviews}
              prefix={<CalendarOutlined />}
            />
          </Card>
        </Col>

        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Scheduled"
              value={stats.scheduledInterviews + stats.confirmedInterviews}
              prefix={<ScheduleOutlined style={{ color: "#1890ff" }} />}
              valueStyle={{ color: "#1890ff" }}
            />
          </Card>
        </Col>

        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Completed"
              value={stats.completedInterviews}
              prefix={<CheckCircleOutlined style={{ color: "#52c41a" }} />}
              valueStyle={{ color: "#52c41a" }}
            />
          </Card>
        </Col>

        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="Cancelled"
              value={stats.cancelledInterviews}
              prefix={<CloseCircleOutlined style={{ color: "#ff4d4f" }} />}
              valueStyle={{ color: "#ff4d4f" }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} className="mt-4">
        <Col xs={24} sm={12} md={8}>
          <Card title="Completion Rate">
            <Flex vertical align="center">
              <Progress
                type="circle"
                percent={Math.round(stats.completionRate * 100)}
                format={(percent) => `${percent}%`}
                strokeColor={
                  stats.completionRate >= 0.7
                    ? "#52c41a"
                    : stats.completionRate >= 0.5
                    ? "#faad14"
                    : "#ff4d4f"
                }
              />
              <p className="mt-2 text-center text-gray-500">
                {stats.completedInterviews} completed out of{" "}
                {stats.totalInterviews} total
              </p>
            </Flex>
          </Card>
        </Col>

        <Col xs={24} sm={12} md={8}>
          <Card title="Average Duration">
            <Flex align="center" justify="center" className="h-full">
              <Statistic
                value={stats.averageDuration}
                suffix="minutes"
                prefix={<ClockCircleOutlined />}
              />
            </Flex>
          </Card>
        </Col>

        <Col xs={24} md={8}>
          <Card title="Most Common">
            <Flex vertical gap="middle">
              <div>
                <p className="text-gray-500 mb-1">Interview Type</p>
                <Flex align="center" className="text-lg font-medium">
                  {mostCommonType.type === InterviewType.VIDEO_CALL && (
                    <VideoCameraOutlined className="mr-1" />
                  )}
                  {mostCommonType.type === InterviewType.PHONE_CALL && (
                    <PhoneOutlined className="mr-1" />
                  )}
                  {mostCommonType.type === InterviewType.IN_PERSON && (
                    <EnvironmentOutlined className="mr-1" />
                  )}
                  {getTypeLabel(mostCommonType.type)}
                  <span className="text-xs text-gray-400 ml-2">
                    ({mostCommonType.count} interviews)
                  </span>
                </Flex>
              </div>

              <div>
                <p className="text-gray-500 mb-1">Status</p>
                <p className="text-lg font-medium">
                  {mostCommonStatus.status === InterviewStatus.SCHEDULED && (
                    <ScheduleOutlined className="mr-1" />
                  )}
                  {mostCommonStatus.status === InterviewStatus.COMPLETED && (
                    <CheckCircleOutlined className="mr-1" />
                  )}
                  {mostCommonStatus.status === InterviewStatus.CANCELLED && (
                    <CloseCircleOutlined className="mr-1" />
                  )}
                  {mostCommonStatus.status.charAt(0).toUpperCase() +
                    mostCommonStatus.status.slice(1).replace("_", " ")}
                  <span className="text-xs text-gray-400 ml-2">
                    ({mostCommonStatus.count} interviews)
                  </span>
                </p>
              </div>
            </Flex>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default InterviewStatsComponent;
