"use client";
import { Interview } from "@/types/interview";
import React, { useState } from "react";
import BaseModal from "@/components/ui/modals/BaseModal";
import { message } from "antd";
import BaseButton from "@/components/ui/buttons/BaseButton";
import InterviewService from "@/services/interviewService";
import { useRouter } from "next/navigation";

interface DeleteInterviewModalProps {
  interview: Interview;
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const DeleteInterviewModal: React.FC<DeleteInterviewModalProps> = ({
  interview,
  visible,
  onClose,
  onSuccess,
}) => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  const handleDelete = async () => {
    try {
      setLoading(true);
      await InterviewService.deleteInterview(interview.id);
      message.success("Interview deleted successfully");
      onClose();
      router.push("/admin/interviews");
      onSuccess();
    } catch (error) {
      console.error("Failed to delete interview:", error);
      message.error("Failed to delete interview");
    } finally {
      setLoading(false);
    }
  };

  return (
    <BaseModal
      title="Confirm Deletion"
      isVisible={visible}
      onClose={onClose}
      footer={[
        <BaseButton key="cancel" onClick={onClose} label="Cancel" />,
        <BaseButton
          key="delete"
          type="primary"
          danger
          loading={loading}
          onClick={handleDelete}
          label="Delete"
        />,
      ]}
    >
      <p>
        Are you sure you want to delete this interview? This action cannot be
        undone.
      </p>
    </BaseModal>
  );
};

export default DeleteInterviewModal;
