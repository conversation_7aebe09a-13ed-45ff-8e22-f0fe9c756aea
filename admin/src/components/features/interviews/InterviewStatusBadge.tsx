// components/features/interviews/InterviewStatusBadge.tsx
"use client";
import React from "react";
import { Badge, Tag } from "antd";
import {
  VideoCameraOutlined,
  PhoneOutlined,
  EnvironmentOutlined,
} from "@ant-design/icons";
import { InterviewStatus, InterviewType } from "@/types/interview";
import {
  INTERVIEW_STATUS_OPTIONS,
  INTERVIEW_TYPE_OPTIONS,
} from "@/constants/interview";

interface InterviewStatusBadgeProps {
  status: InterviewStatus;
}

interface InterviewTypeBadgeProps {
  type: InterviewType;
}

/**
 * Component to display interview status with color badge
 */
export const InterviewStatusBadge: React.FC<InterviewStatusBadgeProps> = ({
  status,
}) => {
  const statusConfig = INTERVIEW_STATUS_OPTIONS.find(
    (opt) => opt.value === status
  ) || {
    value: status,
    label: status,
    color: "#d9d9d9",
  };

  return <Badge color={statusConfig.color} text={statusConfig.label} />;
};

/**
 * Component to display interview status as a colored tag
 */
export const InterviewStatusTag: React.FC<InterviewStatusBadgeProps> = ({
  status,
}) => {
  const statusConfig = INTERVIEW_STATUS_OPTIONS.find(
    (opt) => opt.value === status
  ) || {
    value: status,
    label: status,
    color: "#d9d9d9",
  };

  return <Tag color={statusConfig.color}>{statusConfig.label}</Tag>;
};

/**
 * Component to display interview type with icon badge
 */
export const InterviewTypeBadge: React.FC<InterviewTypeBadgeProps> = ({
  type,
}) => {
  const typeConfig = INTERVIEW_TYPE_OPTIONS.find(
    (opt) => opt.value === type
  ) || {
    value: type,
    label: type,
    icon: "api",
  };

  let icon;
  switch (typeConfig.icon) {
    case "video-camera":
      icon = <VideoCameraOutlined />;
      break;
    case "phone":
      icon = <PhoneOutlined />;
      break;
    case "environment":
      icon = <EnvironmentOutlined />;
      break;
    default:
      icon = null;
  }

  return (
    <Badge
      text={
        <span>
          {icon && <span className="mr-1">{icon}</span>}
          {typeConfig.label}
        </span>
      }
    />
  );
};

/**
 * Component to display interview type as a tag with icon
 */
export const InterviewTypeTag: React.FC<InterviewTypeBadgeProps> = ({
  type,
}) => {
  const typeConfig = INTERVIEW_TYPE_OPTIONS.find(
    (opt) => opt.value === type
  ) || {
    value: type,
    label: type,
    icon: "api",
  };

  let icon;
  switch (typeConfig.icon) {
    case "video-camera":
      icon = <VideoCameraOutlined />;
      break;
    case "phone":
      icon = <PhoneOutlined />;
      break;
    case "environment":
      icon = <EnvironmentOutlined />;
      break;
    default:
      icon = null;
  }

  return <Tag icon={icon}>{typeConfig.label}</Tag>;
};

export default InterviewStatusBadge;
