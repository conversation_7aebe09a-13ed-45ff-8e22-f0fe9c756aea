"use client";
import BaseSelect from "@/components/ui/selects/BaseSelect";
import {
  NOTIFICATION_RECIPIENT_OPTIONS,
  NOTIFICATION_TEMPLATE_OPTIONS,
} from "@/constants/interview";
import InterviewService from "@/services/interviewService";
import {
  BellOutlined,
  MailOutlined,
  MessageOutlined,
  SendOutlined,
} from "@ant-design/icons";
import { Button, Card, Form, Input, message, Radio } from "antd";
import React, { useState } from "react";

const { TextArea } = Input;

interface NotificationFormProps {
  interviewId: string;
  candidateName?: string;
  employerName?: string;
  onSuccess: () => void;
  onCancel: () => void;
}

const NotificationForm: React.FC<NotificationFormProps> = ({
  interviewId,
  candidateName = "Candidate",
  employerName = "Employer",
  onSuccess,
  onCancel,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<string>("");
  console.log("🚀 ~ selectedTemplate:", selectedTemplate);

  // Template mapping for quick content presets
  const templatePresets: Record<string, string> = {
    reminder: `This is a reminder that your interview is coming up soon. Please ensure you have a stable internet connection and a quiet environment.`,
    reschedule: `We need to reschedule the previously planned interview. Please check the new date and time and confirm if it works for you.`,
    cancellation: `We regret to inform you that the scheduled interview needs to be cancelled. We apologize for any inconvenience.`,
    confirmation: `This is to confirm that your interview has been scheduled. Please let us know if you have any questions or need any accommodations.`,
    feedback: `We'd like your feedback on the recent interview. Your input is valuable to us and helps improve our process.`,
    custom: ``,
  };

  const handleTemplateChange = (value: string) => {
    setSelectedTemplate(value);
    if (value !== "custom") {
      form.setFieldsValue({ message: templatePresets[value] });
    } else {
      form.setFieldsValue({ message: "" });
    }
  };

  interface NotificationFormValues {
    recipient: string;
    method: string;
    template: string;
    message: string;
  }

  const handleSubmit = async (values: NotificationFormValues) => {
    try {
      setLoading(true);

      // Prepare notification data
      const notificationData = {
        recipient: values.recipient as "candidate" | "employer" | "both",
        method: values.method as "email" | "sms" | "app",
        template: values.template,
        message: values.message,
      };

      // Send notification
      await InterviewService.sendNotification(interviewId, notificationData);
      message.success("Notification sent successfully");

      onSuccess();
    } catch (error) {
      console.error("Failed to send notification:", error);
      message.error("Failed to send notification");
    } finally {
      setLoading(false);
    }
  };

  // Get recipient label for preview
  const getRecipientLabel = (recipientValue: string) => {
    switch (recipientValue) {
      case "candidate":
        return candidateName;
      case "employer":
        return employerName;
      case "both":
        return `${candidateName} and ${employerName}`;
      default:
        return "Recipient";
    }
  };

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSubmit}
      initialValues={{
        recipient: "candidate",
        method: "email",
        template: "reminder",
      }}
    >
      <Card
        title={
          <div>
            <BellOutlined /> Notification Settings
          </div>
        }
        className="mb-4"
      >
        <Form.Item
          label="Recipient"
          name="recipient"
          rules={[{ required: true, message: "Please select a recipient" }]}
        >
          <Radio.Group>
            {NOTIFICATION_RECIPIENT_OPTIONS.map((option) => (
              <Radio value={option.value} key={option.value}>
                {option.value === "candidate"
                  ? candidateName
                  : option.value === "employer"
                  ? employerName
                  : `${candidateName} & ${employerName}`}
              </Radio>
            ))}
          </Radio.Group>
        </Form.Item>

        <Form.Item
          label="Notification Method"
          name="method"
          rules={[{ required: true, message: "Please select a method" }]}
        >
          <Radio.Group>
            <Radio.Button value="email">
              <MailOutlined /> Email
            </Radio.Button>
            <Radio.Button value="sms">
              <MessageOutlined /> SMS
            </Radio.Button>
            <Radio.Button value="app">
              <BellOutlined /> In-App
            </Radio.Button>
          </Radio.Group>
        </Form.Item>

        <Form.Item
          label="Template"
          name="template"
          rules={[{ required: true, message: "Please select a template" }]}
        >
          <BaseSelect
            options={NOTIFICATION_TEMPLATE_OPTIONS.map((option) => ({
              value: option.value,
              label: option.label,
            }))}
            placeholder="Select a template"
            onChange={handleTemplateChange}
          />
        </Form.Item>
      </Card>

      <Card
        title={
          <div>
            <SendOutlined /> Message Content
          </div>
        }
        className="mb-4"
      >
        <Form.Item
          label="Message"
          name="message"
          rules={[{ required: true, message: "Please enter a message" }]}
        >
          <TextArea
            rows={6}
            placeholder="Enter your notification message here"
          />
        </Form.Item>

        <div className="bg-gray-50 p-4 rounded-md border border-gray-200 mb-4">
          <h4 className="text-sm font-medium text-gray-700 mb-2">
            Message Preview
          </h4>
          <div className="text-sm text-gray-600">
            <p>
              To:{" "}
              {getRecipientLabel(
                form.getFieldValue("recipient") || "candidate"
              )}
            </p>
            <p>Method: {form.getFieldValue("method") || "email"}</p>
            <p>
              Template:{" "}
              {NOTIFICATION_TEMPLATE_OPTIONS.find(
                (t) => t.value === form.getFieldValue("template")
              )?.label || "Reminder"}
            </p>
            <div className="mt-2 p-2 bg-white border border-gray-100 rounded-sm">
              {form.getFieldValue("message") || templatePresets["reminder"]}
            </div>
          </div>
        </div>
      </Card>

      <div className="flex justify-end gap-2">
        <Button onClick={onCancel}>Cancel</Button>
        <Button
          type="primary"
          htmlType="submit"
          loading={loading}
          icon={<SendOutlined />}
        >
          Send Notification
        </Button>
      </div>
    </Form>
  );
};

export default NotificationForm;
