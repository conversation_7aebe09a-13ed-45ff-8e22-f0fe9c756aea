"use client";
import { Interview } from "@/types/interview";
import { <PERSON>lex, <PERSON>, Tabs, <PERSON>lt<PERSON>, Typography } from "antd";
import React, { useState } from "react";
import {
  DeleteOutlined,
  EditOutlined,
  SendOutlined,
  InfoCircleOutlined,
  TeamOutlined,
  FileTextOutlined,
  MessageOutlined,
} from "@ant-design/icons";
import BaseButton from "@/components/ui/buttons/BaseButton";
import DeleteInterviewModal from "@/components/features/interviews/modals/DeleteInterviewModal";
import EditInterviewModal from "@/components/features/interviews/modals/EditInterviewModal";
import FeedbackModal from "@/components/features/interviews/modals/FeedbackModal";
import NotificationModal from "@/components/features/interviews/modals/NotificationModal";
import FeedbackResultsTab from "@/components/features/interviews/tabs/FeedbackResultsTab";
import GeneralInfoTab from "@/components/features/interviews/tabs/GeneralInfoTab";
import NotesTab from "@/components/features/interviews/tabs/NotesTab";
import ParticipantsTab from "@/components/features/interviews/tabs/ParticipantsTab";

const { Title } = Typography;

interface InterviewDetailProps {
  interview: Interview;
  onRefresh: () => void;
}

const InterviewDetail: React.FC<InterviewDetailProps> = ({
  interview,
  onRefresh,
}) => {
  // Modal visibility states
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [feedbackModalVisible, setFeedbackModalVisible] = useState(false);
  const [notifyModalVisible, setNotifyModalVisible] = useState(false);

  const tabItems = [
    {
      key: "general",
      label: (
        <span>
          <InfoCircleOutlined /> General Information
        </span>
      ),
      children: <GeneralInfoTab interview={interview} />,
    },
    {
      key: "participants",
      label: (
        <span>
          <TeamOutlined /> Participants
        </span>
      ),
      children: <ParticipantsTab interview={interview} />,
    },
    {
      key: "feedback",
      label: (
        <span>
          <FileTextOutlined /> Feedback & Results
        </span>
      ),
      children: (
        <FeedbackResultsTab
          interview={interview}
          onAddFeedback={() => setFeedbackModalVisible(true)}
        />
      ),
    },
    {
      key: "notes",
      label: (
        <span>
          <MessageOutlined /> Notes
        </span>
      ),
      children: <NotesTab interview={interview} />,
    },
  ];

  return (
    <div>
      <Flex justify="space-between" className="mb-4">
        <Title level={4}>Interview Details</Title>
        <Space>
          <Tooltip title="Edit Interview">
            <BaseButton
              icon={<EditOutlined />}
              onClick={() => setEditModalVisible(true)}
              label="Edit"
            />
          </Tooltip>
          <Tooltip title="Send Notification">
            <BaseButton
              icon={<SendOutlined />}
              onClick={() => setNotifyModalVisible(true)}
              label="Notify"
            />
          </Tooltip>
          <Tooltip title="Delete Interview">
            <BaseButton
              danger
              icon={<DeleteOutlined />}
              onClick={() => setDeleteModalVisible(true)}
              label="Delete"
            />
          </Tooltip>
        </Space>
      </Flex>

      <Tabs defaultActiveKey="general" items={tabItems} />

      {/* Modals */}
      <EditInterviewModal
        interview={interview}
        visible={editModalVisible}
        onClose={() => setEditModalVisible(false)}
        onSuccess={() => {
          setEditModalVisible(false);
          onRefresh();
        }}
      />

      <DeleteInterviewModal
        interview={interview}
        visible={deleteModalVisible}
        onClose={() => setDeleteModalVisible(false)}
        onSuccess={onRefresh}
      />

      <FeedbackModal
        interview={interview}
        visible={feedbackModalVisible}
        onClose={() => setFeedbackModalVisible(false)}
        onSuccess={() => {
          setFeedbackModalVisible(false);
          onRefresh();
        }}
      />

      <NotificationModal
        interview={interview}
        visible={notifyModalVisible}
        onClose={() => setNotifyModalVisible(false)}
        onSuccess={() => {
          setNotifyModalVisible(false);
          onRefresh();
        }}
      />
    </div>
  );
};

export default InterviewDetail;
