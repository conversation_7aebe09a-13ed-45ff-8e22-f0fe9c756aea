"use client";
import BaseDatePicker from "@/components/ui/datepickers/BaseDatePicker";
import { REJECTION_REASON_OPTIONS } from "@/constants/interview";
import InterviewService from "@/services/interviewService";
import { InterviewFeedback } from "@/types/interview";
import {
  BulbOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  FieldTimeOutlined,
  StarOutlined,
} from "@ant-design/icons";
import { Button, Card, Form, Input, message, Radio, Rate, Select } from "antd";
import dayjs from "dayjs";
import React, { useState } from "react";
import { RadioChangeEvent } from "antd";

const { TextArea } = Input;
const { Option } = Select;

interface InterviewFeedbackFormProps {
  interviewId: string;
  onSuccess: () => void;
  onCancel: () => void;
}

const InterviewFeedbackForm: React.FC<InterviewFeedbackFormProps> = ({
  interviewId,
  onSuccess,
  onCancel,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [decision, setDecision] = useState<
    "Offer" | "Consider" | "Reject" | null
  >(null);

  const handleDecisionChange = (e: RadioChangeEvent) => {
    setDecision(e.target.value as "Offer" | "Consider" | "Reject" | null);
  };

  const handleSubmit = async (values: {
    rating: number;
    communication: number;
    technicalSkills: number;
    culturalFit: number;
    experience: number;
    strengths?: string;
    weaknesses?: string;
    notes?: string;
    decision: "Offer" | "Consider" | "Reject";
    rejectionReason?: string;
    salary?: string;
    startDate?: dayjs.Dayjs;
    offerNotes?: string;
  }) => {
    try {
      setLoading(true);

      // Format strengths and weaknesses arrays
      const strengths = values.strengths
        ? values.strengths.split(",").map((s: string) => s.trim())
        : [];
      const weaknesses = values.weaknesses
        ? values.weaknesses.split(",").map((w: string) => w.trim())
        : [];

      // Prepare offer details if decision is offer
      let offerDetails;
      if (values.decision === "Offer") {
        offerDetails = {
          salary: values.salary,
          startDate: values.startDate
            ? values.startDate.toISOString()
            : undefined,
          notes: values.offerNotes,
        };
      }

      // Create feedback object
      const feedback: InterviewFeedback = {
        rating: values.rating,
        communication: values.communication,
        technicalSkills: values.technicalSkills,
        culturalFit: values.culturalFit,
        experience: values.experience,
        strengths,
        weaknesses,
        notes: values.notes,
        decision: values.decision,
        rejectionReason:
          values.decision === "Reject" ? values.rejectionReason : undefined,
        offerDetails: values.decision === "Offer" ? offerDetails : undefined,
        submittedBy: "Admin", // This would come from current user context in a real app
        submittedAt: new Date().toISOString(),
      };

      // Submit feedback
      await InterviewService.submitFeedback(interviewId, feedback);
      message.success("Feedback submitted successfully");

      onSuccess();
    } catch (error) {
      console.error("Failed to submit feedback:", error);
      message.error("Failed to submit feedback");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSubmit}
      initialValues={{
        rating: 0,
        communication: 0,
        technicalSkills: 0,
        culturalFit: 0,
        experience: 0,
        decision: null,
      }}
    >
      <Card
        title={
          <div>
            <StarOutlined /> Candidate Ratings
          </div>
        }
        className="mb-4"
      >
        <Form.Item
          label="Overall Rating"
          name="rating"
          rules={[
            { required: true, message: "Please provide an overall rating" },
          ]}
        >
          <Rate />
        </Form.Item>

        <Form.Item label="Communication Skills" name="communication">
          <Rate />
        </Form.Item>

        <Form.Item label="Technical Skills" name="technicalSkills">
          <Rate />
        </Form.Item>

        <Form.Item label="Cultural Fit" name="culturalFit">
          <Rate />
        </Form.Item>

        <Form.Item label="Experience" name="experience">
          <Rate />
        </Form.Item>
      </Card>

      <Card
        title={
          <div>
            <BulbOutlined /> Evaluation Comments
          </div>
        }
        className="mb-4"
      >
        <Form.Item label="Strengths (comma separated)" name="strengths">
          <TextArea
            placeholder="Enter candidate strengths (e.g., Communication skills, Technical knowledge)"
            rows={2}
          />
        </Form.Item>

        <Form.Item
          label="Areas for Improvement (comma separated)"
          name="weaknesses"
        >
          <TextArea
            placeholder="Enter areas where candidate can improve"
            rows={2}
          />
        </Form.Item>

        <Form.Item label="Detailed Feedback Notes" name="notes">
          <TextArea
            placeholder="Enter detailed feedback about the candidate's performance"
            rows={4}
          />
        </Form.Item>
      </Card>

      <Card title={<div>Decision</div>} className="mb-4">
        <Form.Item
          name="decision"
          rules={[{ required: true, message: "Please select a decision" }]}
        >
          <Radio.Group onChange={handleDecisionChange}>
            <Radio.Button
              value="Offer"
              style={{ color: "white", backgroundColor: "#52c41a" }}
            >
              <CheckCircleOutlined /> Offer Position
            </Radio.Button>
            <Radio.Button
              value="Consider"
              style={{ color: "white", backgroundColor: "#1890ff" }}
            >
              <FieldTimeOutlined /> Consider Later
            </Radio.Button>
            <Radio.Button
              value="Reject"
              style={{ color: "white", backgroundColor: "#f5222d" }}
            >
              <CloseCircleOutlined /> Reject
            </Radio.Button>
          </Radio.Group>
        </Form.Item>

        {decision === "Reject" && (
          <Form.Item
            label="Rejection Reason"
            name="rejectionReason"
            rules={[
              { required: true, message: "Please select a rejection reason" },
            ]}
          >
            <Select placeholder="Select the main reason for rejection">
              {REJECTION_REASON_OPTIONS.map((reason) => (
                <Option key={reason} value={reason}>
                  {reason}
                </Option>
              ))}
            </Select>
          </Form.Item>
        )}

        {decision === "Offer" && (
          <>
            <Form.Item
              label="Salary Offer"
              name="salary"
              rules={[
                { required: true, message: "Please enter salary details" },
              ]}
            >
              <Input placeholder="e.g., 50,000 VND/hour" />
            </Form.Item>

            <Form.Item label="Proposed Start Date" name="startDate">
              <BaseDatePicker
                format="YYYY-MM-DD"
                disabledDate={(date) => date < dayjs().startOf("day")}
              />
            </Form.Item>

            <Form.Item label="Offer Notes" name="offerNotes">
              <TextArea
                placeholder="Additional notes about the offer"
                rows={3}
              />
            </Form.Item>
          </>
        )}
      </Card>

      <div className="flex justify-end gap-2">
        <Button onClick={onCancel}>Cancel</Button>
        <Button type="primary" htmlType="submit" loading={loading}>
          Submit Feedback
        </Button>
      </div>
    </Form>
  );
};

export default InterviewFeedbackForm;
