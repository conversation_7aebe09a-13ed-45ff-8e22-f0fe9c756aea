"use client";
import { useEffect } from "react";
import NProgress from "nprogress";
import "@/styles/nprogress-custom.css";

// Configure NProgress with simpler settings for smooth operation
NProgress.configure({
  showSpinner: false,
  trickle: true,
  trickleSpeed: 100,
  minimum: 0.15,
  easing: "linear",
  speed: 400,
});

export function LoadingBar() {
  useEffect(() => {
    // For initial load
    NProgress.start();

    // Complete after initial load
    setTimeout(() => {
      NProgress.done();
    }, 500);

    return () => {
      NProgress.done();
    };
  }, []);

  return null;
}
