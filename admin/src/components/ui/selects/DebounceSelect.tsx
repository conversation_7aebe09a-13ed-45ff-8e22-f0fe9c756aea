import type { SelectProps } from "antd";
import { Form, Select, Spin } from "antd";
import debounce from "lodash/debounce";
import { useTranslations } from "next-intl";
import { useEffect, useState } from "react";

interface DebounceSelectProps<ValueType = any>
  extends Omit<SelectProps<ValueType | ValueType[]>, "options" | "children"> {
  fetchOptions: (search: string) => any;
  debounceTimeout?: number;
  required?: boolean;
  rules?: any[];
  label?: string;
  helpText?: string;
  className?: string;
  name?: string;
  shouldUpdate?: (prevValues: any, curValues: any) => boolean;
  options?: any[];
}

const DebounceSelect = ({
  fetchOptions,
  debounceTimeout = 300,
  required = false,
  rules = [],
  label,
  className = "",
  name,
  shouldUpdate,
  options: externalOptions,
  ...props
}: DebounceSelectProps<any>) => {
  const tCommon = useTranslations("common");
  const tForm = useTranslations("form");
  const [fetching, setFetching] = useState(false);
  const [options, setOptions] = useState(externalOptions || []);
  const [isOpen, setIsOpen] = useState(false);

  const handleOpenChange = (open: boolean) => {
    setIsOpen(open);
  };

  const loadOptions = async (value: string) => {
    if (!isOpen) {
      return;
    }
    setOptions([]);
    setFetching(true);

    try {
      const newOptions = await fetchOptions(value);
      if (newOptions.data.length === 0) {
        setOptions([]);
        return;
      }
      const formattedOptions = newOptions.data.map((option: any) => ({
        label: option.name,
        value: option.id,
        key: option.id,
      }));
      setOptions(formattedOptions);
    } catch (error) {
      console.error("Failed to fetch options:", error);
    } finally {
      setFetching(false);
    }
  };

  if (required && !rules.some((rule) => "required" in rule)) {
    rules.push({
      required: true,
      message: `Please select ${label || "an option"}`,
    });
  }

  useEffect(() => {
    if (externalOptions) {
      setOptions(externalOptions);
    }
  }, [externalOptions]);

  useEffect(() => {
    if (fetching) {
      return;
    }
    if (isOpen && options.length === 0) {
      loadOptions(props.value);
    }
  }, [isOpen]);

  return (
    <Form.Item
      label={label}
      required={required}
      className={`w-full ${className}`}
      name={name}
      rules={rules}
      shouldUpdate={shouldUpdate}
    >
      <Select
        open={isOpen}
        showSearch
        onSearch={debounce(loadOptions, debounceTimeout)}
        placeholder={tForm("debounce_select.default_helper_text")}
        onOpenChange={handleOpenChange}
        filterOption={(input, option) => {
          if (!option || !option.label) return false;

          return option.label
            ?.toString()
            .toLowerCase()
            .includes(input.toLowerCase());
        }}
        notFoundContent={
          fetching ? <Spin size="small" /> : tCommon("responses.no_data")
        }
        {...props}
        options={options}
      />
    </Form.Item>
  );
};

export default DebounceSelect;
