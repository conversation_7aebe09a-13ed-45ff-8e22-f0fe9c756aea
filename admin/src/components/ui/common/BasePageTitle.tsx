import { Typography } from "antd";
import React from "react";

const { Title } = Typography;

interface BaseTitleProps {
  title: string;
  subtitle?: string;
}

const BasePageTitle: React.FC<BaseTitleProps> = ({ title, subtitle }) => {
  return (
    <div className="flex flex-col">
      <Title level={3} className="m-0 text-lg font-bold">
        {title}
      </Title>

      {subtitle && <p className="text-gray-500 mt-1">{subtitle}</p>}
    </div>
  );
};

export default BasePageTitle;
