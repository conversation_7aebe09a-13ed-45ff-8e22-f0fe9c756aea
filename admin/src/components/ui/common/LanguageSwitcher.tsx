// components/ui/common/LanguageSwitcher.tsx
"use client";

import { Select } from "antd";
import { useLanguage } from "@/contexts/LanguageContext";

const LanguageSwitcher = () => {
  const { locale, setLocale } = useLanguage();

  return (
    <Select
      value={locale}
      onChange={(value) => setLocale(value as "en" | "vi")}
      options={[
        { value: "en", label: "English" },
        { value: "vi", label: "Tiếng Việt" },
      ]}
      suffixIcon={null}
      popupMatchSelectWidth={false}
    />
  );
};

export default LanguageSwitcher;
