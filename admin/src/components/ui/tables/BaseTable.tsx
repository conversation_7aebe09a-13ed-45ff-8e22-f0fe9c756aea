"use client";
import { <PERSON>, But<PERSON>, Flex, Dropdown } from "antd";
import type { TableColumnsType } from "antd";
import type { MenuInfo } from "rc-menu/lib/interface";
import { forwardRef, useImperativeHandle, useState, useEffect } from "react";
import { useFetchData } from "@/hooks/useFetchData";
import SearchBar from "@/components/ui/tables/SearchBar";
import {
  EditOutlined,
  DeleteOutlined,
  MoreOutlined,
  PlusOutlined,
} from "@ant-design/icons";
import BaseModal from "@/components/ui/modals/BaseModal";
import TableFilter, {
  FilterConfig,
  FilterValue,
} from "@/components/ui/tables/TableFilter";
import { ApiListResponse } from "@/types/common";
import { useTranslations } from "next-intl";
import { useNotification } from "@/contexts/NotiContext";
import BasePageTitle from "@/components/ui/common/BasePageTitle";

interface BaseTableProps<T extends object> {
  api: (params?: Record<string, unknown>) => Promise<ApiListResponse<T>>;
  columns: TableColumnsType<T>;
  rowKey?: string | ((record: T) => React.Key);
  title?: string;
  createBtnText?: string;
  initialParams?: Record<string, number | string | undefined>;
  showSearch?: boolean;
  searchPlaceholder?: string;
  showActions?: boolean;
  filters?: FilterConfig[];
  onCreate?: () => void;
  onEdit?: (record: T) => void;
  onDelete?: (record: T) => Promise<void>;
  onRowClick?: (record: T) => void;
  disabledRowCheckbox?: boolean;
}

export interface BaseTableRef {
  refetch: () => void;
}

function BaseTableComponent<T extends object>(
  {
    api,
    columns,
    rowKey = "key",
    title,
    createBtnText,
    initialParams = {},
    showSearch = true,
    searchPlaceholder,
    onCreate,
    onEdit,
    onDelete,
    onRowClick,
    showActions = true,
    filters = [],
    disabledRowCheckbox,
  }: BaseTableProps<T>,
  ref: React.Ref<BaseTableRef>
) {
  const tCommon = useTranslations("common");
  const tTable = useTranslations("table");
  const notification = useNotification();

  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [pagination, setPagination] = useState({
    current: initialParams.page ? Number(initialParams.page) : 1,
    pageSize: initialParams.pageSize ? Number(initialParams.pageSize) : 10,
  });
  const [searchValue, setSearchValue] = useState(initialParams.search || "");
  const [deletingId, setDeletingId] = useState<React.Key | null>(null);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [recordToDelete, setRecordToDelete] = useState<T | null>(null);
  const [filterValues, setFilterValues] = useState<Record<string, FilterValue>>(
    {}
  );

  const defaultCreateBtnText = createBtnText || tCommon("actions.create");
  const defaultSearchPlaceholder =
    searchPlaceholder || tCommon("placeholders.search_placeholder");

  const { data, total, loading, refetch } = useFetchData<T>(api, {
    ...initialParams,
    ...filterValues,
    keyword: searchValue,
    page: pagination.current - 1,
    limit: pagination.pageSize,
  });

  useImperativeHandle(ref, () => ({
    refetch,
  }));

  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  const handlePaginationChange = (page: number, pageSize: number) => {
    setPagination({ current: page, pageSize });
  };

  const handleSearch = (value: string) => {
    setSearchValue(value);
    setPagination({
      ...pagination,
      current: 1,
    });
  };

  const handleEdit = (record: T, e: React.MouseEvent) => {
    // Stop event propagation to prevent row click
    e.stopPropagation();
    if (onEdit) {
      onEdit(record);
    }
  };

  const showDeleteConfirm = (record: T, e: React.MouseEvent) => {
    // Stop event propagation to prevent row click
    e.stopPropagation();
    setRecordToDelete(record);
    setDeleteModalVisible(true);
  };

  const handleDelete = async () => {
    if (onDelete && recordToDelete) {
      try {
        const recordKey =
          typeof rowKey === "function"
            ? rowKey(recordToDelete)
            : recordToDelete[rowKey as keyof T];
        setDeletingId(recordKey as React.Key);
        await onDelete(recordToDelete);
        refetch();
        setDeleteModalVisible(false);
        notification.notifySuccess(tTable("deleted_successfully"));
      } catch (error) {
        console.error("Delete error:", error);
        notification.notifyError(tTable("delete_failed"));
      } finally {
        setDeletingId(null);
        setRecordToDelete(null);
      }
    }
  };

  const handleFilterChange = (values: Record<string, FilterValue>) => {
    setFilterValues(values);
    // Reset to first page when filtering
    setPagination({
      ...pagination,
      current: 1,
    });
  };

  const hasSelected = selectedRowKeys.length > 0;

  const finalColumns = [...columns];

  if (showActions && (onEdit || onDelete)) {
    finalColumns.push({
      title: tCommon("actions.actions"),
      key: "actions",
      fixed: "right",
      width: 80,
      render: (_, record) => {
        const recordKey =
          typeof rowKey === "function"
            ? rowKey(record)
            : record[rowKey as keyof T];
        const isDeleting = deletingId === recordKey;

        const items = [
          ...(onEdit
            ? [
                {
                  key: "edit",
                  label: tCommon("actions.edit"),
                  icon: <EditOutlined />,
                  onClick: (info: MenuInfo) => {
                    info.domEvent.stopPropagation();
                    handleEdit(record, info.domEvent as React.MouseEvent);
                  },
                },
              ]
            : []),
          ...(onDelete
            ? [
                {
                  key: "delete",
                  label: tCommon("actions.delete"),
                  icon: <DeleteOutlined />,
                  danger: true,
                  onClick: (info: MenuInfo) => {
                    info.domEvent.stopPropagation();
                    showDeleteConfirm(
                      record,
                      info.domEvent as React.MouseEvent
                    );
                  },
                },
              ]
            : []),
        ];

        return (
          <div onClick={(e) => e.stopPropagation()}>
            <Dropdown
              menu={{ items }}
              trigger={["click"]}
              disabled={isDeleting}
            >
              <Button
                type="text"
                icon={<MoreOutlined />}
                size="small"
                loading={isDeleting}
                onClick={(e) => e.stopPropagation()}
              />
            </Dropdown>
          </div>
        );
      },
    });
  }

  useEffect(() => {
    refetch();
  }, [searchValue, pagination, filterValues]);

  return (
    <Flex vertical gap="middle">
      {title && <BasePageTitle title={title} />}

      <Flex align="center" gap="middle" justify="space-between" wrap>
        {showSearch && (
          <div className="w-1/3">
            <SearchBar
              onSearch={handleSearch}
              placeholder={defaultSearchPlaceholder}
            />
          </div>
        )}
        <div className="flex items-center space-x-2">
          {filters.length > 0 && (
            <TableFilter
              filters={filters}
              onFilterChange={handleFilterChange}
              initialValues={filterValues}
            />
          )}
          <div className="flex items-center">
            {hasSelected ? (
              <div className="mr-4">
                {tTable("selected_items", { count: selectedRowKeys.length })}
              </div>
            ) : null}
            {onCreate && (
              <Button type="primary" onClick={onCreate} loading={loading}>
                <PlusOutlined />
                {defaultCreateBtnText}
              </Button>
            )}
          </div>
        </div>
      </Flex>

      <Table<T>
        className="[&_.ant-table-row:hover]:cursor-pointer"
        rowSelection={!disabledRowCheckbox ? rowSelection : undefined}
        columns={finalColumns}
        dataSource={data}
        rowKey={rowKey}
        loading={loading}
        pagination={{
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: total,
          onChange: handlePaginationChange,
          showSizeChanger: true,
          onShowSizeChange: handlePaginationChange,
        }}
        scroll={{ x: "max-content" }}
        onRow={(record) => ({
          onClick: () => onRowClick && onRowClick(record),
        })}
      />

      {/* Delete Confirmation Modal */}
      <BaseModal
        isVisible={deleteModalVisible}
        title={tTable("confirm_delete")}
        onClose={() => setDeleteModalVisible(false)}
        onSubmit={handleDelete}
        submitText={tCommon("actions.delete")}
        confirmLoading={!!deletingId}
      >
        <div className="text-center">
          <p>{tTable("delete_confirmation_message")}</p>
          <p>{tTable("delete_confirmation_warning")}</p>
        </div>
      </BaseModal>
    </Flex>
  );
}

const BaseTable = forwardRef(BaseTableComponent) as <T extends object>(
  props: BaseTableProps<T> & { ref?: React.Ref<BaseTableRef> }
) => ReturnType<typeof BaseTableComponent>;

export default BaseTable;
