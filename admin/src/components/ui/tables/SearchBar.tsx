"use client";
import { Input, Flex } from "antd";
import { SearchOutlined } from "@ant-design/icons";
import { useState } from "react";

interface SearchBarProps {
  onSearch: (value: string) => void;
  placeholder?: string;
  allowClear?: boolean;
}

const SearchBar = ({
  onSearch,
  placeholder = "Search...",
  allowClear = true,
}: SearchBarProps) => {
  const [searchValue, setSearchValue] = useState("");

  const handleSearch = () => {
    onSearch(searchValue);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value);
  };

  const handleClear = () => {
    setSearchValue("");
    onSearch("");
  };

  return (
    <Flex gap="small" className="mb-4">
      <Input
        value={searchValue}
        onChange={handleInputChange}
        onPressEnter={handleSearch}
        placeholder={placeholder}
        allowClear={allowClear}
        onClear={handleClear}
        style={{ flexGrow: 1 }}
        suffix={<SearchOutlined />}
      />
    </Flex>
  );
};

export default SearchBar;
