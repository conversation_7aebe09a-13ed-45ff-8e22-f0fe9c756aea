"use client";
import { DatePicker, Flex, Form } from "antd";
import { RangePickerProps } from "antd/lib/date-picker";
import { Rule } from "antd/lib/form";
import React from "react";

const { RangePicker } = DatePicker;

interface BaseDateRangePickerProps extends RangePickerProps {
  label?: string;
  name?: string;
  required?: boolean;
  error?: string;
  helpText?: string;
  rules?: Rule[];
  className?: string;
}

const BaseDateRangePicker: React.FC<BaseDateRangePickerProps> = ({
  label,
  name,
  required = false,
  error,
  helpText,
  rules = [],
  className = "",
  placeholder = ["Start date", "End date"],
  ...props
}) => {
  const status = error ? "error" : "";

  // Add required rule if required prop is true
  if (required && !rules.some((rule) => "required" in rule)) {
    rules.push({
      required: true,
      message: `Please select ${label || "a date range"}`,
    });
  }

  return (
    <Form.Item
      label={label}
      required={required}
      help={error || helpText}
      validateStatus={status}
      className={`w-full ${className}`}
      name={name}
      rules={rules}
    >
      <Flex vertical gap="small">
        <RangePicker
          placeholder={placeholder}
          status={status}
          style={{ width: "100%" }}
          {...props}
        />
      </Flex>
    </Form.Item>
  );
};

export default BaseDateRangePicker;
