"use client";
import { Form, Input } from "antd";
import { Rule } from "antd/lib/form";
import { InputProps } from "antd/lib/input";
import React from "react";

interface BaseInputProps extends Omit<InputProps, "name"> {
  label?: string;
  required?: boolean;
  error?: string | null;
  helpText?: string;
  name?: string | (string | number)[];
  rules?: Rule[];
  className?: string;
}

const BaseInput: React.FC<BaseInputProps> = ({
  label,
  required = false,
  error,
  helpText,
  name,
  rules = [],
  className = "",
  ...props
}) => {
  if (required && !rules.some((rule) => "required" in rule)) {
    rules.push({
      required: true,
      message: `Please enter ${label || "this field"}`,
    });
  }

  return (
    <Form.Item
      label={label}
      required={required}
      help={error || helpText}
      className={`w-full ${className}`}
      name={name}
      rules={rules}
    >
      <Input
        {...props}
        id={typeof name === "string" ? name : name?.join("_")}
      />
    </Form.Item>
  );
};

export default BaseInput;
