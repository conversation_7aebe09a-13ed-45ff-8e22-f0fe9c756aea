"use client";
import { Form, Input } from "antd";
import { Rule } from "antd/lib/form";
import { TextAreaProps } from "antd/lib/input";
import React from "react";

const { TextArea } = Input;

interface BaseTextAreaProps extends TextAreaProps {
  label?: string;
  name?: string;
  required?: boolean;
  helpText?: string;
  rules?: Rule[];
  className?: string;
  maxLength?: number;
  showCount?: boolean;
  rows?: number;
  autoSize?: boolean | { minRows?: number; maxRows?: number };
  placeholder?: string;
}

const BaseTextArea: React.FC<BaseTextAreaProps> = ({
  label,
  name,
  required = false,
  helpText,
  rules = [],
  className = "",
  maxLength,
  showCount = false,
  rows = 4,
  autoSize = false,
  placeholder = "Enter text here...",
  ...props
}) => {
  if (required && !rules.some((rule) => "required" in rule)) {
    rules.push({
      required: true,
      message: `Please enter ${label || "this field"}`,
    });
  }

  return (
    <Form.Item
      label={label}
      required={required}
      help={helpText}
      className={`w-full ${className}`}
      name={name}
      rules={rules}
    >
      <TextArea
        placeholder={placeholder}
        maxLength={maxLength}
        showCount={showCount}
        rows={rows}
        autoSize={autoSize}
        id={name}
        {...props}
      />
    </Form.Item>
  );
};

export default BaseTextArea;
