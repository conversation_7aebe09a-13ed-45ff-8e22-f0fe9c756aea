export interface Branch {
  id: string;
  name: string;
  address: string;
  staffCount: number;
  activeJobPostings: number;
  isDefault: boolean;
  managerName?: string;
  phoneNumber?: string;
  operatingHours?: string;
  galleryImageUrls?: string[];
  additionalData?: Record<string, unknown>;
}

export interface BranchListParams {
  companyId: string;
  page?: number;
  pageSize?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

export interface BranchResponse {
  data: Branch[];
  total: number;
  page: number;
  pageSize: number;
}
