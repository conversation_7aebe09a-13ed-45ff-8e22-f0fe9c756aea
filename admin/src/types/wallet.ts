import { ApiResponseAddress } from "./address";

export interface WalletUser {
  id: number;
  ulid: string;
  name: string;
  email: string;
  phoneNumber: string;
  profilePicture: string;
  address: ApiResponseAddress;
}

export interface Wallet {
  id: number;
  user: WalletUser;
  point: number;
}

export interface WalletListResponse {
  data: Wallet[];
  errors?: Array<{
    code: number;
    message: string;
  }>;
  message: string;
  statusCode: number;
  success: boolean;
}
