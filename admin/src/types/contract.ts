// types/Contract.ts
export interface TimeEntry {
  id: string;
  contractId: string;
  date: Date;
  hoursWorked: number;
  description: string;
  status: "pending" | "approved" | "rejected";
  createdAt: Date;
  updatedAt: Date;
}

export interface PaymentRecord {
  id: string;
  contractId: string;
  amount: number;
  date: Date;
  status: "pending" | "completed" | "failed";
  description: string;
  paymentMethod: string;
  transactionId?: string;
  createdAt: Date;
}

export interface ContractMessage {
  id: string;
  contractId: string;
  senderId: string;
  senderName: string;
  message: string;
  createdAt: Date;
  isRead: boolean;
}

export interface Contract {
  id: number;
  title: string;
  employerId: number | string;
  employerName: string;
  jobSeekerId: number | string;
  jobSeekerName: string;
  startDate: Date;
  endDate: Date;
  hourlyRate: number;
  paymentFrequency: "weekly" | "biweekly" | "monthly";
  workingHoursPerWeek: number;
  workDays: string[];
  contractType: "part-time" | "freelance" | "project";
  status: "draft" | "offered" | "active" | "completed" | "terminated";
  createdAt: Date;
  activatedAt?: Date;
  terminatedAt?: Date;
  terminationReason?: string;
  isFeePaid: boolean;
  timeEntries: TimeEntry[];
  payments: PaymentRecord[];
  messages: ContractMessage[];
  additionalTerms: string;
}

export interface Payment {
  id: string;
  date: string;
  amount: number;
  description: string;
  status: "pending" | "completed" | "failed";
}

export const contractStatusOptions = [
  { value: "draft", label: "Draft" },
  { value: "offered", label: "Offered" },
  { value: "active", label: "Active" },
  { value: "completed", label: "Completed" },
  { value: "terminated", label: "Terminated" },
];

export const contractTypeOptions = [
  { value: "part-time", label: "Part-time" },
  { value: "freelance", label: "Freelance" },
  { value: "project", label: "Project-based" },
];

export const paymentFrequencyOptions = [
  { value: "weekly", label: "Weekly" },
  { value: "biweekly", label: "Bi-weekly" },
  { value: "monthly", label: "Monthly" },
];

export const workDayOptions = [
  { value: "monday", label: "Monday" },
  { value: "tuesday", label: "Tuesday" },
  { value: "wednesday", label: "Wednesday" },
  { value: "thursday", label: "Thursday" },
  { value: "friday", label: "Friday" },
  { value: "saturday", label: "Saturday" },
  { value: "sunday", label: "Sunday" },
];
