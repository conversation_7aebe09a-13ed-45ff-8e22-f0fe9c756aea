interface IPaginationMetadata {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  firstItemIndex: number;
  lastItemIndex: number;
  hasMorePages: boolean;
  hasPreviousPage: boolean;
}

export interface ApiListResponse<T> {
  data: T[];
  paginationMetadata?: IPaginationMetadata;
  status?: number;
  message?: string;
}

export interface ApiDetailResponse<T> {
  data: T;
  status?: number;
  message?: string;
}
