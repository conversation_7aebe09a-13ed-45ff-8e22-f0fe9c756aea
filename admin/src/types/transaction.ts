// types/transaction.ts
export interface Transaction {
  id: string;
  userId: string;
  userName: string;
  timestamp: string;
  points: number;
  amount: number;
  paymentMethod: "credit_card" | "bank_transfer" | "e_wallet" | "other";
  status: "completed" | "pending" | "failed" | "refunded";
}

export interface TransactionStats {
  totalRevenue: number;
  dailyRevenue: number;
  weeklyRevenue: number;
  monthlyRevenue: number;
  transactionsByMethod: {
    method: string;
    count: number;
  }[];
}

export interface PointPackage {
  id: string;
  name: string;
  points: number;
  price: number;
  description?: string;
  isPopular?: boolean;
  createdAt: string;
}

export interface TransactionTrend {
  date: string;
  revenue: number;
}
