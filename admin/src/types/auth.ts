export interface SignInFormValues {
  email: string;
  password: string;
  remember: boolean;
  [key: string]: unknown;
}

export interface User {
  ulid: string;
  email: string;
  name: string;
  profilePicture: string | null;
  role: string;
  permissions: string[] | null;
  createdAt: string;
  id: number;
}

export interface SignInResponse {
  accessToken: string;
  refreshToken: string;
  tokenType: string;
  user: User;
}

export interface RefreshTokenResponse {
  token: string;
  refreshToken: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export interface UpdateProfileRequest {
  name: string;
  phoneNumber: string;
  dateOfBirth: string;
  gender: "male" | "female" | "other";
  address: {
    provinceCode: number;
    districtCode: number;
    wardCode: number;
    detailAddress: string;
  };
}
