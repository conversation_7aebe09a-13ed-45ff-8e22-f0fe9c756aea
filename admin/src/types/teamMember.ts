// types/TeamMemberTypes.ts

import { Branch } from "./branch";

export interface TeamMember {
  id: string;
  companyId: string | number;
  name: string;
  photoUrl: string;
  position?: string;
  branchName?: string;
  branch: Branch;
  isManager: boolean;
  isActive: boolean;
  joinDate: string;
  isPending: boolean;
  additionalData?: Record<string, unknown>;
}

export interface TeamMemberInput {
  id?: string;
  companyId: string | number;
  userId: number;
  photoUrl?: string;
  position?: string;
  branchId?: string;
  isManager?: boolean;
}

export interface TeamMemberListResponse {
  data: TeamMember[];
  total: number;
}

export interface TeamMemberResponse {
  data: TeamMember;
}
