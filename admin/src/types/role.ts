import { Permission } from "@/types/permission";
export interface Role {
  id: string;
  name: string;
  permissions: Permission[];
  description?: string;
  isDefault?: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface RoleFormData {
  name: string;
  permissionIds?: string[];
  description?: string;
  isDefault?: boolean;
}

export interface RoleUser {
  id: string;
  name: string;
  username: string;
  joinedDate: string;
}
