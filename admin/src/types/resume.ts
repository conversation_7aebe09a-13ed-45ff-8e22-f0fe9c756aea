// types/resume.ts

import { Dayjs } from "dayjs";

export enum WorkShift {
  Morning = 0, // 6:00 - 12:00
  Afternoon = 1, // 12:00 - 18:00
  Evening = 2, // 18:00 - 22:00
  Night = 3, // 22:00 - 06:00
  Weekend = 4,
  Flexible = 5,
}

export interface ContactInfo {
  id?: number;
  phone?: string;
  phoneNumber?: string;
  email: string;
  address?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string;
  linkedin?: string;
  linkedInUrl?: string;
  github?: string;
  portfolioUrl?: string;
  resumeId?: number;
}

export interface Education {
  id?: number;
  school?: string;
  institution?: string;
  degree: string;
  fieldOfStudy?: string;
  major?: string;
  startDate: string;
  endDate?: string;
  description?: string;
  resumeId?: number;
}

export interface LanguageSkill {
  language: string;
  level: string;
}

export interface PartTimePreference {
  id?: number;
  minHourlyRate?: number;
  maxHoursPerWeek?: number;
  availableDays: string[];
  availableTimeSlots?: string[];
  preferredShifts?: WorkShift[];
  preferredJobTypes?: string[];
  preferredLocations?: string[];
  remoteOnly?: boolean;
  maxTravelDistance?: number;
  additionalNotes?: string;
  resumeId?: number;
  isStudent?: boolean;
  studyMajor?: string;
  currentStudyProgram?: string;
  transportationMode?: string;
  languageSkills?: string[];
  workLocationPreferences?: string[];
  willingToWorkRemotely?: boolean;
  willingToWorkOnsite?: boolean;
}

export interface WorkExperience {
  id?: number;
  company: string;
  position: string;
  startDate: string;
  endDate?: string;
  description: string;
  resumeId?: number;
}

export interface Resume {
  id: string | number;
  name: string;
  description: string;
  userId?: number;
  ulid?: string;
  lastUpdated?: string;
  lastUpdateAt?: string;
  skills: string[];
  workExperiences?: WorkExperience[];
  educations?: Education[];
  contactInfo?: ContactInfo;
  partTimePreference?: PartTimePreference;
  languages?: LanguageSkill[];
  isActive: boolean;
}

// Form value interface for ease of use with forms
export interface ResumeFormValues {
  name: string;
  description: string;
  skills: string;
  isActive: boolean;
  "contactInfo.phone": string;
  "contactInfo.email": string;
  "contactInfo.address"?: string;
  "contactInfo.linkedin"?: string;
  "contactInfo.github"?: string;
  "partTime.isPartTime": boolean;
  "partTime.minHoursPerWeek"?: number;
  "partTime.maxHoursPerWeek"?: number;
  "partTime.availableDays"?: string[];
  "partTime.preferredShifts"?: WorkShift[];
  "partTime.isStudent"?: boolean;
  "partTime.currentStudyProgram"?: string;
  "partTime.transportationMode"?: string;
  "partTime.languageSkills"?: string;
  "partTime.workLocationPreferences"?: string;
  "partTime.willingToWorkRemotely"?: boolean;
  "partTime.willingToWorkOnsite"?: boolean;
  "partTime.additionalNotes"?: string;
  workExperiences?: Array<{
    company: string;
    position: string;
    startDate: string | Dayjs;
    endDate?: string | Dayjs;
    description: string;
  }>;
  educations?: Array<{
    school: string;
    degree: string;
    startDate: string | Dayjs;
    endDate?: string | Dayjs;
    major?: string;
  }>;
}
