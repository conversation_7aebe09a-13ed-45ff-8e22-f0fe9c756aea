// types/application.ts

import { User } from "./auth";

export interface JobApplication {
  id: string;
  jobId: string;
  candidateName: string;
  photoUrl: string;
  status: ApplicationStatus;
  matchScore?: number;
  applicationDate?: string;
  experience?: string;
  skills?: string[];
  availability?: string;
  interviewDate?: string;
  notes?: string;
  resumeUrl?: string;
  distance?: string;
  salary?: string;
  isShortlisted?: boolean;
  userInfo: User;
  resumeId: number;
}

export type ApplicationStatus =
  | "Mới"
  | "Đã xem"
  | "Lịch phỏng vấn"
  | "Đã phỏng vấn"
  | "Đã mời làm việc"
  | "Đã từ chối";

export interface ApplicationFilters {
  status?: ApplicationStatus;
  matchScoreMin?: number;
  dateRange?: [string, string];
  isShortlisted?: boolean;
  search?: string;
  page?: number;
  pageSize?: number;
}

export interface ApplicationStatistics {
  total: number;
  new: number;
  viewed: number;
  scheduled: number;
  interviewed: number;
  hired: number;
  rejected: number;
  shortlisted: number;
}
