// types/interview/types.ts

/**
 * Interview status enum
 */
export enum InterviewStatus {
  PENDING = "pending",
  SCHEDULED = "scheduled",
  CONFIRMED = "confirmed",
  COMPLETED = "completed",
  CANCELLED = "cancelled",
  RESCHEDULED = "rescheduled",
  NO_SHOW = "no_show",
}

/**
 * Interview type enum
 */
export enum InterviewType {
  VIDEO_CALL = "Video Call",
  PHONE_CALL = "Phone Call",
  IN_PERSON = "In Person",
}

/**
 * Base interview interface
 */
export interface Interview {
  id: string;
  jobPostId: string;
  jobPostTitle: string;
  employerId: string;
  employerName: string;
  candidateId: string;
  candidateName: string;
  photoUrl: string;
  status: InterviewStatus;
  type: InterviewType;
  scheduledDate: string;
  duration: number; // Minutes
  location?: string;
  meetingLink?: string;
  notes?: string;
  feedbackProvided?: boolean;
  feedback?: InterviewFeedback;
  createdAt: string;
  updatedAt: string;
}

/**
 * Interview creation data interface
 */
export interface InterviewFormData {
  jobPostId: string;
  candidateId: string;
  employerId: string;
  status: InterviewStatus;
  type: InterviewType;
  scheduledDate: string;
  duration: number;
  location?: string;
  meetingLink?: string;
  notes?: string;
}

/**
 * Interview feedback interface
 */
export interface InterviewFeedback {
  rating?: number; // 1-5
  communication?: number; // 1-5
  technicalSkills?: number; // 1-5
  culturalFit?: number; // 1-5
  experience?: number; // 1-5
  strengths?: string[];
  weaknesses?: string[];
  notes?: string;
  decision?: "Offer" | "Consider" | "Reject";
  rejectionReason?: string;
  offerDetails?: {
    salary?: string;
    startDate?: string;
    notes?: string;
  };
  submittedBy?: string;
  submittedAt?: string;
}

/**
 * Interview statistics interface
 */
export interface InterviewStats {
  totalInterviews: number;
  scheduledInterviews: number;
  confirmedInterviews: number;
  completedInterviews: number;
  cancelledInterviews: number;
  noShowInterviews: number;
  completionRate: number;
  averageDuration: number;
  interviewsByType: Record<InterviewType, number>;
  interviewsByStatus: Record<InterviewStatus, number>;
}

/**
 * Interview filter interface
 */
export interface InterviewFilter {
  status?: InterviewStatus | InterviewStatus[];
  type?: InterviewType | InterviewType[];
  employerId?: string;
  candidateId?: string;
  jobPostId?: string;
  dateRange?: [string, string]; // ISO date strings for range
  search?: string;
}
