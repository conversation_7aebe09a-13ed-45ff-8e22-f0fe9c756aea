// types/CompanyTypes.ts

import { ApiResponseAddress } from "@/types/address";

export interface Company {
  id: string;
  name: string;
  industry: string;
  description: string;
  address: ApiResponseAddress;
  websiteUrl?: string;
  phoneNumber?: string;
  email?: string;
  foundedYear?: string;
  logoUrl?: string;
  totalEmployees: number;
  activeJobPostings: number;
  isVerified: boolean;
  isFavorite: boolean;
  isIndividual: boolean;
  createdAt: string;
  updatedAt: string;
  additionalData?: Record<string, unknown>;
}

export interface CompanyCreateInput {
  name: string;
  industry: string;
  description: string;
  address: string;
  websiteUrl?: string;
  phoneNumber?: string;
  email?: string;
  foundedYear?: string;
  logoUrl?: string;
  isIndividual: boolean;
  additionalData: Record<string, unknown>;
}

export interface CompanyUpdateInput extends Partial<CompanyCreateInput> {
  id: string;
}

export interface CompanyListResponse {
  data: Company[];
  total: number;
}

export interface CompanyResponse {
  data: Company;
}
