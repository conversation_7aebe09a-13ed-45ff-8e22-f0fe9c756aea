import { UserAddress } from "./address";
import { UserRole } from "@/constants/userRole";

export interface IUser {
  id: number | string;
  ulid: string; // Preserve ULID from existing user
  token: string;
  refreshToken: string;

  // Profile data
  name: string;
  phoneNumber: string;
  email: string;
  profilePicture: string;
  role: string;
  isEmailVerified: boolean;
  isPhoneVerified: boolean;
  isAdminPortal?: boolean;

  // Other fields
  active?: boolean;
  createdAt: string;
  lastLogin: string;
}

export interface UserFormData {
  name: string;
  phone: string;
  email: string;
  role: UserRole;
}

export type TabKey =
  | "info"
  | "profile"
  | "activities"
  | "transactions"
  | "logs";

// Extended User interface to include additional fields
export interface ExtendedUser extends IUser {
  address?: UserAddress;
  dateOfBirth?: string;
  gender?: "male" | "female" | "other";
  skills?: string[];
  education?: Array<{
    degree: string;
    institution: string;
    year: string;
  }>;
  experience?: Array<{
    position: string;
    company: string;
    duration: string;
    description: string;
  }>;
  interests?: string[];
  posts?: Array<{
    id: string;
    title: string;
    date: string;
    status: string;
  }>;
  applications?: Array<{
    id: string;
    jobTitle: string;
    company: string;
    date: string;
    status: string;
  }>;
  reviews?: Array<{
    id: string;
    from: string;
    rating: number;
    comment: string;
    date: string;
  }>;
  transactions?: Array<{
    id: string;
    type: "deposit" | "usage";
    points: number;
    date: string;
    details: string;
  }>;
}
