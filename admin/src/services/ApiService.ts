import { LOCAL_STORAGE_KEYS } from "@/constants/auth";
import { RefreshTokenResponse } from "@/types/auth";
import { utils } from "@/utils";
import { notification } from "antd";
import axios, {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  AxiosError,
} from "axios";
interface ApiResponse<T> {
  data: T;
  status: number;
  message?: string;
}

interface CustomAxiosRequestConfig extends AxiosRequestConfig {
  _retry?: boolean;
}

let isRefreshing = false;

const handleAuthError = (): void => {
  utils.auth.clearAuthData();
  isRefreshing = false;
  // Redirect to login page
  if (typeof window !== "undefined") {
    window.location.href = "/signin";
  }
};

const refreshToken = async (): Promise<void> => {
  const refreshTokenString = utils.auth.getRefreshToken();
  if (!refreshTokenString) {
    handleAuthError();
    return;
  }
  const res = (
    await ApiService.post(`/v1/admin/auth/refresh-token`, {
      refreshToken: refreshTokenString,
    })
  ).data as unknown as RefreshTokenResponse;

  if (res.token && res.refreshToken) {
    localStorage.setItem(LOCAL_STORAGE_KEYS.ACCESS_TOKEN, res.token);
    localStorage.setItem(LOCAL_STORAGE_KEYS.REFRESH_TOKEN, res.refreshToken);

    if (typeof window !== "undefined") {
      // window.location.reload();
    }
  } else {
    handleAuthError();
  }
};

const createApiService = () => {
  const api: AxiosInstance = axios.create({
    baseURL: process.env.NEXT_PUBLIC_API_URL,
    timeout: 15000,
    headers: {
      "Content-Type": "application/json",
      Accept: "application/json",
    },
  });

  api.interceptors.request.use(
    (config) => {
      if (typeof window !== "undefined") {
        const token = localStorage.getItem(LOCAL_STORAGE_KEYS.ACCESS_TOKEN);
        if (token && config.headers && !isRefreshing) {
          config.headers["Authorization"] = `Bearer ${token}`;
        }
      }

      return config;
    },
    (error: AxiosError) => Promise.reject(error)
  );

  api.interceptors.response.use(
    (response: AxiosResponse) => response,
    async (error: AxiosError) => {
      const originalRequest = error.config as CustomAxiosRequestConfig;

      // if (
      //   error.response?.status === 401 &&
      //   originalRequest &&
      //   !originalRequest._retry
      // ) {
      //   originalRequest._retry = true;
      //   notification.error({
      //     message: "Session expired",
      //     description: "Your session has expired.",
      //   });

      //   if (!isRefreshing) {
      //     isRefreshing = true;
      //     await refreshToken();
      //   } else {
      //     handleAuthError();
      //   }

      //   return new Promise(() => {});
      // }

      return Promise.reject(error);
    }
  );

  return {
    // Generic request method with explicit type parameter
    async request<T>(config: AxiosRequestConfig): Promise<ApiResponse<T>> {
      try {
        const response: AxiosResponse<ApiResponse<T>> = await api.request(
          config
        );
        return response.data;
      } catch (error) {
        throw error;
      }
    },

    async get<T>(
      url: string,
      params?: Record<string, unknown>,
      config?: AxiosRequestConfig
    ): Promise<ApiResponse<T>> {
      const requestConfig: AxiosRequestConfig = {
        ...config,
        method: "GET",
        url,
        params,
      };
      return this.request<T>(requestConfig);
    },

    async post<T, D extends object>(
      url: string,
      data?: D,
      config?: AxiosRequestConfig
    ): Promise<ApiResponse<T>> {
      const requestConfig: AxiosRequestConfig = {
        ...config,
        method: "POST",
        url,
        data,
      };
      return this.request<T>(requestConfig);
    },

    async put<T, D extends object>(
      url: string,
      data?: D,
      config?: AxiosRequestConfig
    ): Promise<ApiResponse<T>> {
      const requestConfig: AxiosRequestConfig = {
        ...config,
        method: "PUT",
        url,
        data,
      };
      return this.request<T>(requestConfig);
    },

    async patch<T, D extends object>(
      url: string,
      data?: D,
      config?: AxiosRequestConfig
    ): Promise<ApiResponse<T>> {
      const requestConfig: AxiosRequestConfig = {
        ...config,
        method: "PATCH",
        url,
        data,
      };
      return this.request<T>(requestConfig);
    },

    async delete<T>(
      url: string,
      config?: AxiosRequestConfig
    ): Promise<ApiResponse<T>> {
      const requestConfig: AxiosRequestConfig = {
        ...config,
        method: "DELETE",
        url,
      };
      return this.request<T>(requestConfig);
    },
  };
};

const ApiService = createApiService();

export default ApiService;
export { refreshToken };
