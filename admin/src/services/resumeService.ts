// services/resumeService.ts
import { Resume, WorkShift } from "@/types/resume";
import { ApiListResponse } from "@/types/common";
import { v4 as uuidv4 } from "uuid";
import ApiService from "./ApiService";

// Mock resume data
const mockResumes: Resume[] = [
  {
    id: "1",
    name: "Main Resume",
    lastUpdated: "2024-03-15T10:30:00Z",
    isActive: true,
    description:
      "Software developer with 5 years of experience in frontend development",
    skills: ["React", "TypeScript", "Node.js", "GraphQL", "Tailwind CSS"],
    workExperiences: [
      {
        company: "Tech Solutions Inc.",
        position: "Senior Frontend Developer",
        startDate: "2022-01-15",
        description: "Leading frontend development team on multiple projects",
      },
      {
        company: "WebDev Agency",
        position: "Frontend Developer",
        startDate: "2019-05-10",
        endDate: "2021-12-30",
        description: "Developed responsive web applications using React",
      },
    ],
    educations: [
      {
        school: "University of Technology",
        degree: "Bachelor of Computer Science",
        startDate: "2015-09-01",
        endDate: "2019-05-30",
        major: "Software Engineering",
      },
    ],
    contactInfo: {
      phone: "+84967890123",
      email: "<EMAIL>",
      address: "123 Main Street, District 1, HCMC",
      linkedin: "linkedin.com/in/username",
      github: "github.com/username",
    },
  },
  {
    id: "2",
    name: "Part-time Resume",
    lastUpdated: "2024-02-20T14:45:00Z",
    isActive: false,
    description: "Looking for part-time opportunities in web development",
    skills: ["JavaScript", "React", "HTML/CSS", "UI/UX"],
    partTimePreference: {
      minHoursPerWeek: 15,
      maxHoursPerWeek: 25,
      availableDays: ["Monday", "Wednesday", "Friday"],
      preferredShifts: [WorkShift.Afternoon, WorkShift.Evening],
      isStudent: true,
      currentStudyProgram: "Master's in Computer Science",
      transportationMode: "Public Transport",
      languageSkills: ["English - Fluent", "Vietnamese - Native"],
      workLocationPreferences: ["District 1", "District 3", "District 7"],
      willingToWorkRemotely: true,
      willingToWorkOnsite: true,
      additionalNotes: "Prefer project-based work",
    },
  },
];

/**
 * Get all resumes with optional filtering
 */
export const fetchResumes = async (
  params: Record<string, unknown> = {}
): Promise<{ data: Resume[]; total: number }> => {
  try {
    // Simulate API latency
    await new Promise((resolve) => setTimeout(resolve, 500));

    // Extract search term
    const searchTerm = ((params.search as string) || "").toLowerCase();

    // Filter by search term
    let filteredData = mockResumes;
    if (searchTerm) {
      filteredData = mockResumes.filter(
        (resume) =>
          resume.name.toLowerCase().includes(searchTerm) ||
          resume.description.toLowerCase().includes(searchTerm) ||
          resume.skills.some((skill) =>
            skill.toLowerCase().includes(searchTerm)
          )
      );
    }

    // Filter by status
    if (params.status === "active") {
      filteredData = filteredData.filter((resume) => resume.isActive);
    } else if (params.status === "inactive") {
      filteredData = filteredData.filter((resume) => !resume.isActive);
    }

    // Filter by type
    if (params.type === "fulltime") {
      filteredData = filteredData.filter(
        (resume) => !resume.partTimePreference
      );
    } else if (params.type === "parttime") {
      filteredData = filteredData.filter(
        (resume) => !!resume.partTimePreference
      );
    }

    // Return filtered data
    return {
      data: filteredData,
      total: filteredData.length,
    };
  } catch (error) {
    console.error("Error fetching resumes:", error);
    throw error;
  }
};

/**
 * Get a resume by ID from API
 */
export const getResumeById = async (
  id: string | number
): Promise<Resume | null> => {
  try {
    const response = await ApiService.get<Resume>(`/v1/admin/resumes/${id}`);
    return response.data || null;
  } catch (error) {
    console.error(`Error fetching resume with ID ${id}:`, error);
    throw error;
  }
};

/**
 * Create or update a resume
 */
export const saveResume = async (resume: Resume): Promise<Resume> => {
  try {
    // Simulate API latency
    await new Promise((resolve) => setTimeout(resolve, 800));

    // Find the resume index if it exists
    const index = mockResumes.findIndex((r) => r.id === resume.id);

    // If we're setting this resume as active, deactivate all others
    if (resume.isActive) {
      mockResumes.forEach((r) => {
        if (r.id !== resume.id) {
          r.isActive = false;
        }
      });
    }

    // Update or insert the resume
    if (index !== -1) {
      mockResumes[index] = resume;
    } else {
      // Ensure new resume has an ID
      if (!resume.id) {
        resume.id = uuidv4();
      }
      mockResumes.push(resume);
    }

    return resume;
  } catch (error) {
    console.error("Error saving resume:", error);
    throw error;
  }
};

/**
 * Delete a resume by ID
 */
export const deleteResume = async (id: string | number): Promise<boolean> => {
  try {
    await ApiService.delete(`/v1/admin/resumes/${id}`);
    return true;
  } catch (error) {
    console.error(`Error deleting resume with ID ${id}:`, error);
    throw error;
  }
};

/**
 * Set a resume as active
 */
export const setResumeActive = async (id: string): Promise<boolean> => {
  try {
    // Simulate API latency
    await new Promise((resolve) => setTimeout(resolve, 400));

    // Deactivate all resumes
    mockResumes.forEach((resume) => {
      resume.isActive = resume.id === id;
    });

    return true;
  } catch (error) {
    console.error(`Error setting resume ${id} as active:`, error);
    throw error;
  }
};

/**
 * Set resume active status via API
 */
export const setResumeIsActive = async (
  id: string | number,
  isActive: boolean
): Promise<boolean> => {
  try {
    const res = await ApiService.patch(
      `/v1/admin/resumes/${id}/is-active?isActive=${isActive}`
    );
    return true;
  } catch (error) {
    console.error(`Error setting resume ${id} isActive=${isActive}:`, error);
    throw error;
  }
};

/**
 * Get resumes by user ID
 */
export const getUserResumes = async (
  userId: string | number
): Promise<{ data: Resume[] }> => {
  try {
    const response = await ApiService.get(`/v1/admin/resumes/user/${userId}`);
    return { data: response.data as Resume[] };
  } catch (error) {
    console.error(`Error fetching resumes for user ${userId}:`, error);
    throw error;
  }
};

/**
 * Fetch all resumes from API for admin
 */
export const fetchAllResumes = async (
  params: Record<string, unknown> = {}
): Promise<{ data: Resume[]; total: number }> => {
  const res = await ApiService.get("/v1/admin/resumes", params);
  // Map API response to Resume[] if needed
  const data = ((res.data as Resume[]) || []).map((item: any) => ({
    id: item.id,
    name: item.name,
    description: item.description,
    skills: item.skills,
    lastUpdated: item.lastUpdateAt,
    userId: item.userId,
    isActive: item.isActive,
  }));
  return { data, total: data.length };
};

/**
 * Update a resume by ID
 */
export const updateResume = async (
  id: string | number,
  data: any
): Promise<Resume> => {
  try {
    const response = await ApiService.put(`/v1/admin/resumes/${id}`, data);
    return response.data as Resume;
  } catch (error) {
    console.error(`Error updating resume with ID ${id}:`, error);
    throw error;
  }
};

/**
 * Create a new resume
 */
export const createResume = async (data: any): Promise<Resume> => {
  try {
    const response = await ApiService.post(`/v1/admin/resumes`, data);
    return response.data as Resume;
  } catch (error) {
    console.error(`Error creating resume:`, error);
    throw error;
  }
};
