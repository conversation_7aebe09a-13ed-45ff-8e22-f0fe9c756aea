import ApiService from "@/services/ApiService";
import { ApiDetailResponse, ApiListResponse } from "@/types/common";
import { Role, RoleFormData, RoleUser } from "@/types/role";

interface IRoleService {
  getList: (params?: Record<string, unknown>) => Promise<ApiListResponse<Role>>;
  getDetail: (id: string) => Promise<ApiDetailResponse<Role>>;
  getRoleUsers: (
    roleId: string,
    params?: {
      page?: number;
      limit?: number;
      keyword?: string;
    }
  ) => Promise<ApiListResponse<RoleUser>>;
  getAvailableUsers: (roleId: string) => Promise<RoleUser[]>;
  assignUserToRole: (roleId: string, userId: string) => Promise<unknown>;
  removeUserFromRole: (roleId: string, userId: string) => Promise<unknown>;
  assignPermissionsToRole: (
    roleId: string,
    permissions: string[]
  ) => Promise<unknown>;
  removePermissionsFromRole: (roleId: string) => Promise<unknown>;
  create: (data: RoleFormData) => Promise<unknown>;
  update: (id: string, data: RoleFormData) => Promise<unknown>;
  delete: (id: string) => Promise<unknown>;
}

const basePath = "v1/admin/roles";

const RoleService: IRoleService = {
  getList: async (params?: Record<string, unknown>) => {
    return await ApiService.post(`${basePath}/search`, params);
  },

  getDetail: async (id: string) => {
    return await ApiService.get(`${basePath}/${id}`);
  },

  getRoleUsers: async (roleId, params) => {
    return await ApiService.get(`${basePath}/${roleId}/users`, params);
  },

  getAvailableUsers: async (roleId: string): Promise<RoleUser[]> => {
    const response = await ApiService.get(
      `${basePath}/${roleId}/available-users`
    );
    return response.data as RoleUser[];
  },

  assignUserToRole: async (roleId: string, userId: string) => {
    return await ApiService.post(`${basePath}/${roleId}/users`, { userId });
  },

  removeUserFromRole: async (roleId: string, userId: string) => {
    return await ApiService.delete(`${basePath}/${roleId}/users/${userId}`);
  },

  assignPermissionsToRole: async (roleId: string, permissions: string[]) => {
    return await ApiService.post(`${basePath}/${roleId}/permissions`, {
      permissions,
    });
  },

  removePermissionsFromRole: async (roleId: string) => {
    return await ApiService.delete(`${basePath}/${roleId}/permissions`);
  },

  create: async (data: RoleFormData) => {
    return await ApiService.post(`${basePath}`, data);
  },

  update: async (id: string, data: RoleFormData) => {
    return await ApiService.put(`${basePath}/${id}`, data);
  },

  delete: async (id: string) => {
    return await ApiService.delete(`${basePath}/${id}`);
  },
};

export default RoleService;
