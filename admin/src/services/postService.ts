// services/PostService.ts
import ApiService from "@/services/ApiService";
import { ApiDetailResponse, ApiListResponse } from "@/types/common";
import { Post } from "@/types/post";

interface IPostService {
  getList: (params?: Record<string, unknown>) => Promise<ApiListResponse<Post>>;
  getDetail: (id: string) => Promise<ApiDetailResponse<Post>>;
  create: (data: any) => Promise<unknown>;
  update: (id: string, data: any) => Promise<unknown>;
  delete: (id: string) => Promise<unknown>;
  createApplication: (data: any) => Promise<unknown>;
  getApplications: (postId: string) => Promise<unknown>;
  updateApplication: (applicationId: string, data: any) => Promise<unknown>;
  deleteApplication: (applicationId: string) => Promise<unknown>;
  activate: (id: string) => Promise<unknown>;
}

const basePath = "v1/admin/posts";

const PostService: IPostService = {
  getList: async (params?: Record<string, unknown>) => {
    return await ApiService.post(`${basePath}/search`, params);
  },
  getDetail: async (id: string) => {
    return await ApiService.get(`${basePath}/${id}`);
  },
  create: async (data) => {
    return await ApiService.post(`${basePath}`, data);
  },
  update: async (id: string, data: any) => {
    return await ApiService.put(`${basePath}/${id}`, data);
  },
  delete: async (id: string) => {
    return await ApiService.delete(`${basePath}/${id}`);
  },
  activate: async (id: string) => {
    return await ApiService.patch(`${basePath}/${id}/approve`);
  },
  getApplications: async (postId: string) => {
    return await ApiService.get(`${basePath}/${postId}/application`);
  },
  createApplication: async (data) => {
    return await ApiService.post(
      `${basePath}/${data.postId}/application`,
      data
    );
  },
  updateApplication: async (applicationId: string, data: any) => {
    return await ApiService.put(
      `${basePath}/application/${applicationId}`,
      data
    );
  },
  deleteApplication: async (applicationId: string) => {
    return await ApiService.delete(`${basePath}/application/${applicationId}`);
  },
};

export default PostService;
