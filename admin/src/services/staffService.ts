import ApiService from "@/services/ApiService";
import { ApiDetailResponse, ApiListResponse } from "@/types/common";
import { TeamMember, TeamMemberInput } from "@/types/teamMember";

interface IStaffService {
  getList: (
    params?: Record<string, unknown>
  ) => Promise<ApiListResponse<TeamMember>>;
  getDetail: (
    companyId: string,
    memberId: string
  ) => Promise<ApiDetailResponse<TeamMember>>;
  inviteTeamMember: (
    data: TeamMemberInput
  ) => Promise<ApiDetailResponse<TeamMember>>;
  update: (data: TeamMemberInput) => Promise<ApiDetailResponse<TeamMember>>;
  delete: (companyId: string, memberId: string) => Promise<unknown>;
  resendInvitation: (companyId: string, memberId: string) => Promise<unknown>;
  makeManager: (
    companyId: string,
    memberId: string,
    isManager: boolean
  ) => Promise<ApiDetailResponse<TeamMember>>;
}

const basePath = "v1/admin/companies";

const StaffService: IStaffService = {
  getList: async (params?: Record<string, unknown>) => {
    const companyId = params?.companyId as string;
    return await ApiService.post(
      `${basePath}/${companyId}/staffs/search`,
      params
    );
  },

  getDetail: async (companyId: string, memberId: string) => {
    return await ApiService.get(`${basePath}/${companyId}/staffs/${memberId}`);
  },

  inviteTeamMember: async (data) => {
    const { companyId } = data;
    return await ApiService.post(
      `${basePath}/${companyId}/staffs/invite`,
      data
    );
  },

  update: async (data) => {
    const { id, companyId } = data;
    return await ApiService.put(`${basePath}/${companyId}/staffs/${id}`, data);
  },

  delete: async (companyId: string, memberId: string) => {
    return await ApiService.delete(
      `${basePath}/${companyId}/staffs/${memberId}`
    );
  },

  resendInvitation: async (companyId: string, memberId: string) => {
    return await ApiService.post(
      `${basePath}/${companyId}/staffs/${memberId}/resend-invitation`
    );
  },

  makeManager: async (
    companyId: string,
    memberId: string,
    isManager: boolean
  ) => {
    return await ApiService.patch(
      `${basePath}/${companyId}/staffs/${memberId}/manager-status`,
      { isManager }
    );
  },
};

export default StaffService;
