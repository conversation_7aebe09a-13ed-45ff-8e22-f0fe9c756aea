// services/ContractService.ts
import ApiService from "@/services/ApiService";
import {
  Contract,
  TimeEntry,
  PaymentRecord,
  ContractMessage,
} from "@/types/contract";

interface ContractFilter {
  search?: string;
  status?: string;
  contractType?: string;
  employerId?: string;
  jobSeekerId?: string;
  page?: number;
  limit?: number;
}

interface PaginationResponse {
  limit: number;
  page: number;
  totalElements: number;
}

interface ContractSearchResponse {
  data: Contract[];
  pagination: PaginationResponse;
}

interface ContractDetailResponse {
  id: number | string;
  title: string;
  employerId: number | string;
  employerName: string;
  jobSeekerId: number | string;
  jobSeekerName: string;
  startDate: Date;
  endDate: Date;
  hourlyRate: number;
  paymentFrequency: "weekly" | "biweekly" | "monthly";
  workingHoursPerWeek: number;
  workDays: string[];
  contractType: "part-time" | "freelance" | "project";
  status: "draft" | "offered" | "active" | "completed" | "terminated";
  createdAt: Date;
  activatedAt?: Date;
  terminatedAt?: Date;
  terminationReason?: string;
  isFeePaid: boolean;
  additionalTerms: Record<string, unknown>;
  timeEntries?: TimeEntry[];
  payments?: PaymentRecord[];
  messages?: ContractMessage[];
}

const ContractService = {
  async getContracts(params?: Record<string, unknown>) {
    try {
      const filter: ContractFilter = {
        search: params?.search as string,
        status: params?.status as string,
        contractType: params?.contractType as string,
        employerId: params?.employerId as string,
        jobSeekerId: params?.jobSeekerId as string,
        page: Number(params?.page) || 0,
        limit: Number(params?.pageSize) || 10,
      };

      const response = await ApiService.post(
        "/v1/admin/contracts/search",
        filter
      );

      return {
        data: response.data,
      };
    } catch (error) {
      console.error("Error fetching contracts:", error);
      throw error;
    }
  },

  async getContract(id: string) {
    try {
      const response = await ApiService.get<ContractDetailResponse>(
        `/v1/admin/contracts/${id}`
      );
      return response.data;
    } catch (error) {
      console.error("Error fetching contract:", error);
      throw error;
    }
  },

  async createContract(contractData: Partial<Contract>) {
    try {
      const response = await ApiService.post<
        ContractDetailResponse,
        typeof contractData
      >("/v1/admin/contracts", contractData);
      return response.data;
    } catch (error) {
      console.error("Error creating contract:", error);
      throw error;
    }
  },

  async updateContract(id: string, contractData: Partial<Contract>) {
    try {
      const response = await ApiService.put<
        ContractDetailResponse,
        typeof contractData
      >(`/v1/admin/contracts/${id}`, contractData);
      return response.data;
    } catch (error) {
      console.error("Error updating contract:", error);
      throw error;
    }
  },

  async deleteContract(id: string) {
    try {
      await ApiService.delete(`/v1/admin/contracts/${id}`);
      return { success: true };
    } catch (error) {
      console.error("Error deleting contract:", error);
      throw error;
    }
  },

  async changeContractStatus(
    id: string,
    status: Contract["status"],
    reason?: string
  ) {
    try {
      const requestData = { status, notes: reason };
      const response = await ApiService.put<
        ContractDetailResponse,
        typeof requestData
      >(`/v1/admin/contracts/${id}/status`, requestData);
      return response.data;
    } catch (error) {
      console.error("Error changing contract status:", error);
      throw error;
    }
  },

  async addTimeEntry(contractId: string, data: Partial<TimeEntry>) {
    try {
      const requestData = {
        contractId: Number(contractId),
        ...data,
      };
      const response = await ApiService.post<TimeEntry, typeof requestData>(
        "/v1/admin/contracts/time-entries",
        requestData
      );
      return response.data;
    } catch (error) {
      console.error("Error adding time entry:", error);
      throw error;
    }
  },

  async getTimeEntries(contractId: string, params?: Record<string, unknown>) {
    try {
      const filter = {
        contractId: Number(contractId),
        page: Number(params?.page) || 1,
        limit: Number(params?.pageSize) || 10,
      };

      const response = await ApiService.post<
        {
          data: TimeEntry[];
          pagination: PaginationResponse;
        },
        typeof filter
      >(`/v1/admin/contracts/${contractId}/time-entries/search`, filter);

      return {
        data: response.data.data,
        total: response.data.pagination.totalElements,
      };
    } catch (error) {
      console.error("Error fetching time entries:", error);
      throw error;
    }
  },

  async addPayment(contractId: string, data: Partial<PaymentRecord>) {
    try {
      const requestData = {
        contractId: Number(contractId),
        ...data,
      };
      const response = await ApiService.post<PaymentRecord, typeof requestData>(
        "/v1/admin/contracts/payment-records",
        requestData
      );
      return response.data;
    } catch (error) {
      console.error("Error adding payment:", error);
      throw error;
    }
  },

  async getPayments(params?: Record<string, unknown>) {
    try {
      const filter = {
        page: Number(params?.page) || 1,
        limit: Number(params?.pageSize) || 10,
      };

      const response = await ApiService.post<
        {
          data: PaymentRecord[];
          pagination: PaginationResponse;
        },
        typeof filter
      >("/v1/admin/contracts/payment-records/search", filter);

      return {
        data: response.data.data,
        total: response.data.pagination.totalElements,
      };
    } catch (error) {
      console.error("Error fetching payments:", error);
      throw error;
    }
  },

  async sendMessage(contractId: string, message: string) {
    try {
      const response = await ApiService.post<
        ContractMessage,
        { contractId: number; message: string }
      >("/v1/admin/contracts/messages", {
        contractId: Number(contractId),
        message,
      });
      return response.data;
    } catch (error) {
      console.error("Error sending message:", error);
      throw error;
    }
  },

  async getMessages(contractId: string, params?: Record<string, unknown>) {
    try {
      const filter = {
        contractId: Number(contractId),
        page: Number(params?.page) || 1,
        limit: Number(params?.pageSize) || 10,
      };

      const response = await ApiService.post<
        {
          data: ContractMessage[];
          pagination: PaginationResponse;
        },
        typeof filter
      >(`/v1/admin/contracts/${contractId}/messages/search`, filter);

      return {
        data: response.data.data,
        total: response.data.pagination.totalElements,
      };
    } catch (error) {
      console.error("Error fetching messages:", error);
      throw error;
    }
  },
};

export default ContractService;
