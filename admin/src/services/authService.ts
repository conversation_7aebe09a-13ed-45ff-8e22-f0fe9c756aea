import {
  SignInResponse,
  ChangePasswordRequest,
  UpdateProfileRequest,
} from "@/types/auth";
import ApiService from "@/services/ApiService";

interface IAuthService {
  signin: (data: Record<string, unknown>) => Promise<SignInResponse>;
  changePassword: (data: ChangePasswordRequest) => Promise<{ message: string }>;
  updateProfile: (data: UpdateProfileRequest) => Promise<{ message: string }>;
}

const basePath = "v1/admin/auth";

const AuthService: IAuthService = {
  signin: async (data: Record<string, unknown>) => {
    const res = await ApiService.post(`${basePath}/login`, data);
    return res.data as SignInResponse;
  },

  changePassword: async (data: ChangePasswordRequest) => {
    const res = await ApiService.post(`${basePath}/change-password`, data);
    return res.data as { message: string };
  },

  updateProfile: async (data: UpdateProfileRequest) => {
    const res = await ApiService.post(`${basePath}/update-profile`, data);
    return res.data as { message: string };
  },
};

export default AuthService;
