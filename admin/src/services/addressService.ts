// services/addressService.ts
import { District, Province, Ward } from "@/types/address";
import ApiService from "@/services/ApiService";

interface IAddressService {
  getProvinces: () => Promise<Province[]>;
  getDistricts: (provinceCode: number) => Promise<District[]>;
  getWards: (districtCode: number) => Promise<Ward[]>;
  getProvinceByCode: (code: number) => Promise<Province | null>;
  getDistrictByCode: (
    provinceCode: number,
    districtCode: number
  ) => Promise<District | null>;
  getWardByCode: (
    districtCode: number,
    wardCode: number
  ) => Promise<Ward | null>;
}

const baseURL = "v1/addresses";

const AddressService: IAddressService = {
  getProvinces: async () => {
    const res = await ApiService.get(`${baseURL}/provinces`);
    return (res.data as unknown as Province[]) || [];
  },

  getDistricts: async (provinceCode: number) => {
    const res = await ApiService.get(
      `${baseURL}/provinces/${provinceCode}/districts`
    );
    return (res.data as unknown as District[]) || [];
  },

  getWards: async (districtCode: number) => {
    const res = await ApiService.get(
      `${baseURL}/districts/${districtCode}/wards`
    );
    return (res.data as unknown as Ward[]) || [];
  },

  getProvinceByCode: async (code: number) => {
    const res = await ApiService.get(`${baseURL}/provinces/${code}`);
    return (res.data as unknown as Province) || null;
  },

  getDistrictByCode: async (provinceCode: number, districtCode: number) => {
    const res = await ApiService.get(
      `${baseURL}/provinces/${provinceCode}/districts/${districtCode}`
    );
    return (res.data as unknown as District) || null;
  },

  getWardByCode: async (districtCode: number, wardCode: number) => {
    const res = await ApiService.get(
      `${baseURL}/districts/${districtCode}/wards/${wardCode}`
    );
    return (res.data as unknown as Ward) || null;
  },
};

export default AddressService;
