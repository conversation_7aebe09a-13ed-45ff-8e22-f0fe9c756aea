// services/permissionService.ts
import ApiService from "@/services/ApiService";
import { ApiListResponse } from "@/types/common";
import { Permission, PermissionFormData } from "@/types/permission";

interface IPermissionService {
  getList: (
    params?: Record<string, unknown>
  ) => Promise<ApiListResponse<Permission>>;
  create: (data: PermissionFormData) => Promise<unknown>;
  update: (id: string, data: PermissionFormData) => Promise<unknown>;
  delete: (id: string) => Promise<unknown>;
}

const basePath = "v1/admin/permissions";

const PermissionService: IPermissionService = {
  getList: async (params?: Record<string, unknown>) => {
    return await ApiService.post(`${basePath}/search`, params);
  },
  create: async (data) => {
    return await ApiService.post(`${basePath}`, data);
  },
  update: async (id: string, data: PermissionFormData) => {
    return await ApiService.put(`${basePath}/${id}`, data);
  },
  delete: async (id: string) => {
    return await ApiService.delete(`${basePath}/${id}`);
  },
};

export default PermissionService;
