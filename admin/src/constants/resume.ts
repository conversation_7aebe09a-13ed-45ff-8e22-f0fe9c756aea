// components/features/resumes/resumeUtils.ts
import {
  Resume,
  ResumeFormValues,
  ContactInfo,
  PartTimePreference,
  WorkShift,
} from "@/types/resume";
import { v4 as uuidv4 } from "uuid";

/**
 * Constants for working with resumes
 */
export const DAYS_OF_WEEK = [
  "Monday",
  "Tuesday",
  "Wednesday",
  "Thursday",
  "Friday",
  "Saturday",
  "Sunday",
];

export const WORK_SHIFT_OPTIONS = [
  { label: "Morning (6:00 - 12:00)", value: WorkShift.Morning },
  { label: "Afternoon (12:00 - 18:00)", value: WorkShift.Afternoon },
  { label: "Evening (18:00 - 22:00)", value: WorkShift.Evening },
  { label: "Night (22:00 - 6:00)", value: WorkShift.Night },
  { label: "Weekend", value: WorkShift.Weekend },
  { label: "Flexible", value: WorkShift.Flexible },
];

/**
 * Convert work shift enum value to readable string
 */
export const getWorkShiftLabel = (shift: WorkShift): string => {
  return (
    WORK_SHIFT_OPTIONS.find((option) => option.value === shift)?.label ||
    "Unknown"
  );
};

/**
 * Format a date string to local format
 */
export const formatDate = (dateString: string): string => {
  try {
    return new Date(dateString).toLocaleDateString();
  } catch (error) {
    console.log("🚀 ~ formatDate ~ error:", error);
    return dateString;
  }
};

/**
 * Convert form values to a Resume object
 */
export const formValuesToResume = (
  values: ResumeFormValues,
  existingId?: string
): Resume => {
  // Process skills - convert from comma-separated string to array
  const skills = values.skills
    .split(",")
    .map((skill: string) => skill.trim())
    .filter((skill: string) => skill);

  // Process language skills
  const languageSkills = values["partTime.languageSkills"]
    ? values["partTime.languageSkills"]
        .split(",")
        .map((skill: string) => skill.trim())
        .filter((skill: string) => skill)
    : [];

  // Process work locations
  const workLocationPreferences = values["partTime.workLocationPreferences"]
    ? values["partTime.workLocationPreferences"]
        .split(",")
        .map((location: string) => location.trim())
        .filter((location: string) => location)
    : [];

  // Prepare contact info
  const contactInfo: ContactInfo = {
    phone: values["contactInfo.phone"],
    email: values["contactInfo.email"],
    address: values["contactInfo.address"],
    linkedin: values["contactInfo.linkedin"],
    github: values["contactInfo.github"],
  };

  // Prepare part-time preferences if enabled
  let partTimePreference: PartTimePreference | undefined;
  if (values["partTime.isPartTime"]) {
    partTimePreference = {
      minHoursPerWeek: values["partTime.minHoursPerWeek"],
      maxHoursPerWeek: values["partTime.maxHoursPerWeek"],
      availableDays: values["partTime.availableDays"] || [],
      preferredShifts: values["partTime.preferredShifts"] || [],
      isStudent: values["partTime.isStudent"] || false,
      currentStudyProgram: values["partTime.currentStudyProgram"],
      transportationMode: values["partTime.transportationMode"],
      languageSkills,
      workLocationPreferences,
      willingToWorkRemotely: values["partTime.willingToWorkRemotely"] || false,
      willingToWorkOnsite: values["partTime.willingToWorkOnsite"] || false,
      additionalNotes: values["partTime.additionalNotes"],
    };
  }

  // Prepare resume data
  const resumeData: Resume = {
    id: existingId || uuidv4(),
    name: values.name,
    description: values.description,
    lastUpdated: new Date().toISOString(),
    isActive: values.isActive,
    skills,
    workExperiences: values.workExperiences || [],
    educations: values.educations || [],
    contactInfo,
    partTimePreference,
  };

  return resumeData;
};

/**
 * Convert Resume object to form values
 */
export const resumeToFormValues = (resume: Resume): ResumeFormValues => {
  const formValues: ResumeFormValues = {
    name: resume.name,
    description: resume.description,
    skills: resume.skills.join(", "),
    isActive: resume.isActive,

    // Contact Info
    "contactInfo.phone": resume.contactInfo?.phone || "",
    "contactInfo.email": resume.contactInfo?.email || "",
    "contactInfo.address": resume.contactInfo?.address || "",
    "contactInfo.linkedin": resume.contactInfo?.linkedin || "",
    "contactInfo.github": resume.contactInfo?.github || "",

    // Part-time preferences
    "partTime.isPartTime": !!resume.partTimePreference,
    "partTime.minHoursPerWeek":
      resume.partTimePreference?.minHoursPerWeek ?? 10,
    "partTime.maxHoursPerWeek":
      resume.partTimePreference?.maxHoursPerWeek ?? 20,
    "partTime.availableDays": resume.partTimePreference?.availableDays ?? [],
    "partTime.preferredShifts":
      resume.partTimePreference?.preferredShifts ?? [],
    "partTime.isStudent": resume.partTimePreference?.isStudent ?? false,
    "partTime.currentStudyProgram":
      resume.partTimePreference?.currentStudyProgram || "",
    "partTime.transportationMode":
      resume.partTimePreference?.transportationMode || "",
    "partTime.languageSkills": Array.isArray((resume as any).languages)
      ? ((resume as any).languages as Array<{ language: string }>)
          .map((l) => l.language)
          .join(", ")
      : Array.isArray(resume.partTimePreference?.languageSkills)
      ? resume.partTimePreference.languageSkills.join(", ")
      : "",
    "partTime.workLocationPreferences": Array.isArray(
      resume.partTimePreference?.workLocationPreferences
    )
      ? resume.partTimePreference.workLocationPreferences.join(", ")
      : "",
    "partTime.willingToWorkRemotely":
      resume.partTimePreference?.willingToWorkRemotely ?? false,
    "partTime.willingToWorkOnsite":
      resume.partTimePreference?.willingToWorkOnsite ?? false,
    "partTime.additionalNotes":
      resume.partTimePreference?.additionalNotes || "",

    // Experiences and education
    workExperiences: resume.workExperiences || [],
    educations: resume.educations || [],
  };

  return formValues;
};
