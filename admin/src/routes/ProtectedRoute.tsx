// components/ProtectedRoute.tsx
"use client";

import { LoadingBar } from "@/components/ui/loadings/LoadingBar";
import { useAuth } from "@/contexts/AuthContext";
import { utils } from "@/utils";
import { usePathname, useRouter } from "next/navigation";
import { useEffect } from "react";

export default function ProtectedRoute({
  children,
  allowedRoles = [],
}: {
  children: React.ReactNode;
  allowedRoles?: string[];
}) {
  const { user, loading } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    if (!loading) {
      if (!utils.auth.isAuthenticated()) {
        // Save the current URL to redirect back after login
        sessionStorage.setItem("redirectAfterLogin", pathname);
        router.push("/signin");
      }
    }
  }, [loading, router, pathname, user, allowedRoles]);

  if (loading) {
    return <LoadingBar />;
  }

  if (!utils.auth.isAuthenticated()) {
    return <LoadingBar />;
  }

  return <>{children}</>;
}
