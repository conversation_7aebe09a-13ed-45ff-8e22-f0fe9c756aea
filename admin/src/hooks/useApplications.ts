// hooks/useApplications.ts
"use client";
import ApiService from "@/services/ApiService";
import {
  ApplicationFilters,
  ApplicationStatus,
  JobApplication,
} from "@/types/application";
import dayjs from "dayjs";
import { useEffect, useState } from "react";

export const useApplications = (jobId: string) => {
  const [applications, setApplications] = useState<JobApplication[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [total, setTotal] = useState(0);
  const [filters, setFilters] = useState<ApplicationFilters>({
    page: 1,
    pageSize: 10,
  });

  const fetchApplications = async (params: ApplicationFilters = {}) => {
    try {
      setLoading(true);
      const response = await ApiService.get<{
        data: JobApplication[];
        total: number;
      }>(`/api/admin/jobs/${jobId}/applications`, { ...filters, ...params });

      // Format dates
      const formattedData = response.data.data.map((app) => ({
        ...app,
        applicationDate: dayjs(app.applicationDate).format("YYYY-MM-DD"),
        interviewDate: app.interviewDate
          ? dayjs(app.interviewDate).format("YYYY-MM-DD")
          : undefined,
      }));

      setApplications(formattedData);
      setTotal(response.data.total);
      setError(null);
      return { data: formattedData, total: response.data.total };
    } catch (err) {
      console.error("Error fetching applications:", err);
      setError("Failed to load applications");
      return { data: [], total: 0 };
    } finally {
      setLoading(false);
    }
  };

  const updateApplicationStatus = async (
    applicationId: string,
    newStatus: string
  ) => {
    try {
      setLoading(true);
      await ApiService.patch(`/api/admin/applications/${applicationId}`, {
        status: newStatus,
      });

      // Update local state
      setApplications((prev) =>
        prev.map((app) =>
          app.id === applicationId
            ? { ...app, status: newStatus as ApplicationStatus }
            : app
        )
      );

      return true;
    } catch (err) {
      console.error("Error updating application status:", err);
      setError("Failed to update application status");
      return false;
    } finally {
      setLoading(false);
    }
  };

  const scheduleInterview = async (
    applicationId: string,
    interviewDate: string,
    notes?: string
  ) => {
    try {
      setLoading(true);
      await ApiService.patch(`/api/admin/applications/${applicationId}`, {
        status: "Lịch phỏng vấn",
        interviewDate,
        notes,
      });

      // Update local state
      setApplications((prev) =>
        prev.map((app) =>
          app.id === applicationId
            ? {
                ...app,
                status: "Lịch phỏng vấn",
                interviewDate,
                notes: notes || app.notes,
              }
            : app
        )
      );

      return true;
    } catch (err) {
      console.error("Error scheduling interview:", err);
      setError("Failed to schedule interview");
      return false;
    } finally {
      setLoading(false);
    }
  };

  const toggleShortlist = async (applicationId: string) => {
    try {
      const application = applications.find((app) => app.id === applicationId);
      if (!application) return false;

      const newShortlistStatus = !application.isShortlisted;

      setLoading(true);
      await ApiService.patch(`/api/admin/applications/${applicationId}`, {
        isShortlisted: newShortlistStatus,
      });

      // Update local state
      setApplications((prev) =>
        prev.map((app) =>
          app.id === applicationId
            ? { ...app, isShortlisted: newShortlistStatus }
            : app
        )
      );

      return true;
    } catch (err) {
      console.error("Error toggling shortlist status:", err);
      setError("Failed to update shortlist status");
      return false;
    } finally {
      setLoading(false);
    }
  };

  // Update filters and refetch data
  const updateFilters = (newFilters: ApplicationFilters) => {
    setFilters((prev) => ({ ...prev, ...newFilters }));
  };

  // Initial fetch
  useEffect(() => {
    fetchApplications();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [jobId, filters]);

  return {
    applications,
    loading,
    error,
    total,
    filters,
    updateFilters,
    fetchApplications,
    updateApplicationStatus,
    scheduleInterview,
    toggleShortlist,
  };
};
