/* eslint-disable react-hooks/exhaustive-deps */
// hooks/useAddressForm.ts
"use client";
import { useState, useCallback, useEffect } from "react";
import { Province, District, Ward, AddressFormData } from "@/types/address";
import AddressService from "@/services/addressService";

interface UseAddressFormOptions {
  initialValue?: AddressFormData;
  loadOnMount?: boolean;
  onAddressChange?: (data: AddressFormData & { detailAddress: string }) => void;
}

export const useAddressForm = (options: UseAddressFormOptions = {}) => {
  const { initialValue, loadOnMount = true, onAddressChange } = options;

  // Data states
  const [provinces, setProvinces] = useState<Province[]>([]);
  const [districts, setDistricts] = useState<District[]>([]);
  const [wards, setWards] = useState<Ward[]>([]);

  // Loading states
  const [loading, setLoading] = useState({
    provinces: false,
    districts: false,
    wards: false,
  });

  // Selected values
  const [selectedProvince, setSelectedProvince] = useState<
    Province | undefined | null
  >(null);
  const [selectedDistrict, setSelectedDistrict] = useState<
    District | undefined | null
  >(null);
  const [selectedWard, setSelectedWard] = useState<Ward | undefined | null>(
    null
  );
  const [detailAddress, setDetailAddress] = useState<string>(
    initialValue?.detailAddress || ""
  );

  const setLoadingState = useCallback(
    (key: keyof typeof loading, value: boolean) => {
      setLoading((prev) => ({ ...prev, [key]: value }));
    },
    []
  );

  // Load provinces
  const loadProvinces = useCallback(async () => {
    setLoadingState("provinces", true);

    try {
      const data = await AddressService.getProvinces();
      setProvinces(data);

      // Auto-select initial province if provided
      if (initialValue?.provinceCode) {
        const province = data.find((p) => p.code === initialValue.provinceCode);
        console.log("🚀 ~ loadProvinces ~ province:", province);
        if (province) {
          setSelectedProvince(province);
        }
      }
    } catch (error) {
      console.error("Failed to load provinces:", error);
    } finally {
      setLoadingState("provinces", false);
    }
  }, [initialValue?.provinceCode, setLoadingState]);

  // Load districts
  const loadDistricts = useCallback(
    async (provinceCode: number) => {
      setLoadingState("districts", true);

      try {
        const data = await AddressService.getDistricts(provinceCode);
        setDistricts(data);

        // Auto-select initial district if provided
        if (initialValue?.districtCode) {
          const district = data.find(
            (d) => d.code === initialValue.districtCode
          );
          if (district) {
            setSelectedDistrict(district);
          }
        }
      } catch (error) {
        console.error("Failed to load districts:", error);
      } finally {
        setLoadingState("districts", false);
      }
    },
    [initialValue?.districtCode, setLoadingState]
  );

  // Load wards
  const loadWards = useCallback(
    async (districtCode: number) => {
      setLoadingState("wards", true);

      try {
        const data = await AddressService.getWards(districtCode);
        setWards(data);

        // Auto-select initial ward if provided
        if (initialValue?.wardCode) {
          const ward = data.find((w) => w.code === initialValue.wardCode);
          if (ward) {
            setSelectedWard(ward);
          }
        }
      } catch (error) {
        console.error("Failed to load wards:", error);
      } finally {
        setLoadingState("wards", false);
      }
    },
    [initialValue?.wardCode, setLoadingState]
  );

  // Handle province change
  const handleProvinceChange = useCallback(
    async (provinceCode: number | null) => {
      const province = provinceCode
        ? provinces.find((p) => p.code === provinceCode)
        : null;

      setSelectedProvince(province);
      setSelectedDistrict(null);
      setSelectedWard(null);
      setDistricts([]);
      setWards([]);

      if (provinceCode && province) {
        await loadDistricts(provinceCode);
      }
    },
    [provinces, loadDistricts]
  );

  // Handle district change
  const handleDistrictChange = useCallback(
    async (districtCode: number | null) => {
      const district = districtCode
        ? districts.find((d) => d.code === districtCode)
        : null;

      setSelectedDistrict(district);
      setSelectedWard(null);
      setWards([]);

      if (districtCode && district) {
        await loadWards(districtCode);
      }
    },
    [districts, loadWards]
  );

  // Handle ward change
  const handleWardChange = useCallback(
    (wardCode: number | null) => {
      const ward = wardCode ? wards.find((w) => w.code === wardCode) : null;
      setSelectedWard(ward);
    },
    [wards]
  );

  // Get current form data
  const getFormData = useCallback(
    (): AddressFormData => ({
      provinceCode: selectedProvince?.code || null,
      districtCode: selectedDistrict?.code || null,
      wardCode: selectedWard?.code || null,
      detailAddress: detailAddress,
    }),
    [selectedProvince, selectedDistrict, selectedWard, detailAddress]
  );

  // Get full address text
  const getFullAddressText = useCallback((): string => {
    const parts = [];

    if (detailAddress.trim()) {
      parts.push(detailAddress.trim());
    }
    if (selectedWard) parts.push(selectedWard.name);
    if (selectedDistrict) parts.push(selectedDistrict.name);
    if (selectedProvince) parts.push(selectedProvince.name);

    return parts.join(", ");
  }, [selectedProvince, selectedDistrict, selectedWard, detailAddress]);

  // Reset form
  const resetForm = useCallback(() => {
    setSelectedProvince(null);
    setSelectedDistrict(null);
    setSelectedWard(null);
    setDetailAddress("");
    setDistricts([]);
    setWards([]);
  }, []);

  // Effect to trigger onChange callback
  useEffect(() => {
    if (onAddressChange) {
      const formData = getFormData();
      const detailAddress = getFullAddressText();
      onAddressChange({ ...formData, detailAddress });
    }
  }, [selectedProvince, selectedDistrict, selectedWard, detailAddress]);

  // Effect to load initial data
  useEffect(() => {
    if (loadOnMount) {
      loadProvinces();
    }
  }, [loadOnMount, loadProvinces]);

  // Effect to load districts and wards for initial values
  useEffect(() => {
    if (selectedProvince && initialValue?.districtCode) {
      loadDistricts(selectedProvince.code);
    }
  }, [selectedProvince, initialValue?.districtCode, loadDistricts]);

  useEffect(() => {
    if (selectedDistrict && initialValue?.wardCode) {
      loadWards(selectedDistrict.code);
    }
  }, [selectedDistrict, initialValue?.wardCode, loadWards]);

  return {
    // Data
    provinces,
    districts,
    wards,

    // Loading states
    loading,

    // Selected values
    selectedProvince,
    selectedDistrict,
    selectedWard,

    // Actions
    loadProvinces,
    handleProvinceChange,
    handleDistrictChange,
    handleWardChange,

    // Utilities
    getFormData,
    getFullAddressText,
    resetForm,

    // Computed values
    isComplete: !!(
      selectedProvince &&
      selectedDistrict &&
      selectedWard &&
      detailAddress.trim()
    ),
  };
};
