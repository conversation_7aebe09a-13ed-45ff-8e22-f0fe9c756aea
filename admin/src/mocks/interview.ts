// mocks/interview/mockData.ts
import {
  Interview,
  InterviewStats,
  InterviewStatus,
  InterviewType,
} from "@/types/interview";

// Generate random date within range
const randomDate = (start: Date, end: Date) => {
  return new Date(
    start.getTime() + Math.random() * (end.getTime() - start.getTime())
  );
};

// Format date for display
export const formatDate = (date: Date): string => {
  return date.toISOString();
};

// Mock interview data
export const mockInterviews: Interview[] = [
  {
    id: "INT-1001",
    jobPostId: "JP-2024-001",
    jobPostTitle: "Front-end Developer",
    employerId: "EMP-101",
    employerName: "Tech Innovations",
    candidateId: "CAND-201",
    candidateName: "Nguyễn Văn A",
    photoUrl: "https://randomuser.me/api/portraits/men/32.jpg",
    status: InterviewStatus.SCHEDULED,
    type: InterviewType.VIDEO_CALL,
    scheduledDate: formatDate(
      randomDate(new Date(), new Date(Date.now() + 7 * 24 * 60 * 60 * 1000))
    ),
    duration: 30,
    meetingLink: "https://meet.google.com/abc-defg-hij",
    notes: "Discuss experience with React and TypeScript",
    feedbackProvided: false,
    createdAt: formatDate(new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)),
    updatedAt: formatDate(new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)),
  },
  {
    id: "INT-1002",
    jobPostId: "JP-2024-002",
    jobPostTitle: "UX/UI Designer",
    employerId: "EMP-102",
    employerName: "Creative Solutions",
    candidateId: "CAND-202",
    candidateName: "Trần Thị B",
    photoUrl: "https://randomuser.me/api/portraits/women/44.jpg",
    status: InterviewStatus.CONFIRMED,
    type: InterviewType.VIDEO_CALL,
    scheduledDate: formatDate(
      randomDate(new Date(), new Date(Date.now() + 2 * 24 * 60 * 60 * 1000))
    ),
    duration: 45,
    meetingLink: "https://meet.google.com/klm-nopq-rst",
    notes: "Review portfolio and discuss design process",
    feedbackProvided: false,
    createdAt: formatDate(new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)),
    updatedAt: formatDate(new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)),
  },
  {
    id: "INT-1003",
    jobPostId: "JP-2024-003",
    jobPostTitle: "Marketing Assistant",
    employerId: "EMP-103",
    employerName: "Marketing Group",
    candidateId: "CAND-203",
    candidateName: "Lê Văn C",
    photoUrl: "https://randomuser.me/api/portraits/men/67.jpg",
    status: InterviewStatus.COMPLETED,
    type: InterviewType.IN_PERSON,
    scheduledDate: formatDate(new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)),
    duration: 60,
    location: "Tầng 5, 123 Nguyễn Huệ, Quận 1, TP.HCM",
    notes: "Discuss marketing campaign experience",
    feedbackProvided: true,
    feedback: {
      rating: 4,
      communication: 4,
      technicalSkills: 3,
      culturalFit: 5,
      experience: 4,
      strengths: ["Communication skills", "Creative thinking"],
      weaknesses: ["Limited technical knowledge"],
      notes: "Strong candidate with good communication skills",
      decision: "Offer",
      offerDetails: {
        salary: "50,000 VND/hour",
        startDate: formatDate(new Date(Date.now() + 14 * 24 * 60 * 60 * 1000)),
        notes: "We are pleased to offer you the position",
      },
      submittedBy: "Admin",
      submittedAt: formatDate(new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)),
    },
    createdAt: formatDate(new Date(Date.now() - 10 * 24 * 60 * 60 * 1000)),
    updatedAt: formatDate(new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)),
  },
  {
    id: "INT-1004",
    jobPostId: "JP-2024-004",
    jobPostTitle: "Data Analyst",
    employerId: "EMP-104",
    employerName: "Data Insights",
    candidateId: "CAND-204",
    candidateName: "Phạm Thị D",
    photoUrl: "https://randomuser.me/api/portraits/women/67.jpg",
    status: InterviewStatus.COMPLETED,
    type: InterviewType.PHONE_CALL,
    scheduledDate: formatDate(new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)),
    duration: 30,
    notes: "Discuss SQL and data visualization experience",
    feedbackProvided: true,
    feedback: {
      rating: 2,
      communication: 3,
      technicalSkills: 2,
      culturalFit: 2,
      experience: 2,
      strengths: ["Eager to learn"],
      weaknesses: ["Limited SQL knowledge", "Poor problem-solving skills"],
      notes: "Not a good fit for the position",
      decision: "Reject",
      rejectionReason: "Thiếu kỹ năng phù hợp",
      submittedBy: "Admin",
      submittedAt: formatDate(new Date(Date.now() - 4 * 24 * 60 * 60 * 1000)),
    },
    createdAt: formatDate(new Date(Date.now() - 12 * 24 * 60 * 60 * 1000)),
    updatedAt: formatDate(new Date(Date.now() - 4 * 24 * 60 * 60 * 1000)),
  },
  {
    id: "INT-1005",
    jobPostId: "JP-2024-005",
    jobPostTitle: "Customer Service Representative",
    employerId: "EMP-105",
    employerName: "Service Solutions",
    candidateId: "CAND-205",
    candidateName: "Hoàng Văn E",
    photoUrl: "https://randomuser.me/api/portraits/men/42.jpg",
    status: InterviewStatus.CANCELLED,
    type: InterviewType.VIDEO_CALL,
    scheduledDate: formatDate(new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)),
    duration: 30,
    meetingLink: "https://meet.google.com/uvw-xyz-123",
    notes: "Discuss customer service experience",
    feedbackProvided: false,
    createdAt: formatDate(new Date(Date.now() - 8 * 24 * 60 * 60 * 1000)),
    updatedAt: formatDate(new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)),
  },
  {
    id: "INT-1006",
    jobPostId: "JP-2024-006",
    jobPostTitle: "Content Writer",
    employerId: "EMP-106",
    employerName: "Content Creators",
    candidateId: "CAND-206",
    candidateName: "Vũ Minh F",
    photoUrl: "https://randomuser.me/api/portraits/men/55.jpg",
    status: InterviewStatus.RESCHEDULED,
    type: InterviewType.VIDEO_CALL,
    scheduledDate: formatDate(
      randomDate(new Date(), new Date(Date.now() + 10 * 24 * 60 * 60 * 1000))
    ),
    duration: 45,
    meetingLink: "https://meet.google.com/456-789-abc",
    notes: "Discuss writing portfolio and experience",
    feedbackProvided: false,
    createdAt: formatDate(new Date(Date.now() - 6 * 24 * 60 * 60 * 1000)),
    updatedAt: formatDate(new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)),
  },
  {
    id: "INT-1007",
    jobPostId: "JP-2024-007",
    jobPostTitle: "Barista",
    employerId: "EMP-107",
    employerName: "Coffee House",
    candidateId: "CAND-207",
    candidateName: "Đặng Thị G",
    photoUrl: "https://randomuser.me/api/portraits/women/23.jpg",
    status: InterviewStatus.PENDING,
    type: InterviewType.IN_PERSON,
    scheduledDate: formatDate(
      randomDate(new Date(), new Date(Date.now() + 14 * 24 * 60 * 60 * 1000))
    ),
    duration: 30,
    location: "Coffee House, 456 Lê Lợi, Quận 1, TP.HCM",
    notes: "Discuss coffee making experience",
    feedbackProvided: false,
    createdAt: formatDate(new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)),
    updatedAt: formatDate(new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)),
  },
  {
    id: "INT-1008",
    jobPostId: "JP-2024-008",
    jobPostTitle: "Sales Associate",
    employerId: "EMP-108",
    employerName: "Retail Group",
    candidateId: "CAND-208",
    candidateName: "Phan Văn H",
    photoUrl: "https://randomuser.me/api/portraits/men/28.jpg",
    status: InterviewStatus.NO_SHOW,
    type: InterviewType.VIDEO_CALL,
    scheduledDate: formatDate(new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)),
    duration: 30,
    meetingLink: "https://meet.google.com/def-ghi-jkl",
    notes: "Discuss sales experience and techniques",
    feedbackProvided: false,
    createdAt: formatDate(new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)),
    updatedAt: formatDate(new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)),
  },
];

// Mock interview statistics
export const mockInterviewStats: InterviewStats = {
  totalInterviews: mockInterviews.length,
  scheduledInterviews: mockInterviews.filter(
    (i) => i.status === InterviewStatus.SCHEDULED
  ).length,
  confirmedInterviews: mockInterviews.filter(
    (i) => i.status === InterviewStatus.CONFIRMED
  ).length,
  completedInterviews: mockInterviews.filter(
    (i) => i.status === InterviewStatus.COMPLETED
  ).length,
  cancelledInterviews: mockInterviews.filter(
    (i) => i.status === InterviewStatus.CANCELLED
  ).length,
  noShowInterviews: mockInterviews.filter(
    (i) => i.status === InterviewStatus.NO_SHOW
  ).length,
  completionRate:
    mockInterviews.filter((i) => i.status === InterviewStatus.COMPLETED)
      .length / mockInterviews.length,
  averageDuration:
    mockInterviews.reduce((sum, interview) => sum + interview.duration, 0) /
    mockInterviews.length,
  interviewsByType: {
    [InterviewType.VIDEO_CALL]: mockInterviews.filter(
      (i) => i.type === InterviewType.VIDEO_CALL
    ).length,
    [InterviewType.PHONE_CALL]: mockInterviews.filter(
      (i) => i.type === InterviewType.PHONE_CALL
    ).length,
    [InterviewType.IN_PERSON]: mockInterviews.filter(
      (i) => i.type === InterviewType.IN_PERSON
    ).length,
  },
  interviewsByStatus: {
    [InterviewStatus.PENDING]: mockInterviews.filter(
      (i) => i.status === InterviewStatus.PENDING
    ).length,
    [InterviewStatus.SCHEDULED]: mockInterviews.filter(
      (i) => i.status === InterviewStatus.SCHEDULED
    ).length,
    [InterviewStatus.CONFIRMED]: mockInterviews.filter(
      (i) => i.status === InterviewStatus.CONFIRMED
    ).length,
    [InterviewStatus.COMPLETED]: mockInterviews.filter(
      (i) => i.status === InterviewStatus.COMPLETED
    ).length,
    [InterviewStatus.CANCELLED]: mockInterviews.filter(
      (i) => i.status === InterviewStatus.CANCELLED
    ).length,
    [InterviewStatus.RESCHEDULED]: mockInterviews.filter(
      (i) => i.status === InterviewStatus.RESCHEDULED
    ).length,
    [InterviewStatus.NO_SHOW]: mockInterviews.filter(
      (i) => i.status === InterviewStatus.NO_SHOW
    ).length,
  },
};

// Mock API implementation based on the mock data
export const getMockInterviews = (params: Record<string, unknown> = {}) => {
  let filteredInterviews = [...mockInterviews];
  const { status, type, search, page = 1, pageSize = 10 } = params;

  // Apply filters
  if (status) {
    filteredInterviews = filteredInterviews.filter((i) =>
      Array.isArray(status) ? status.includes(i.status) : i.status === status
    );
  }

  if (type) {
    filteredInterviews = filteredInterviews.filter((i) =>
      Array.isArray(type) ? type.includes(i.type) : i.type === type
    );
  }

  if (search) {
    const searchStr = String(search).toLowerCase();
    filteredInterviews = filteredInterviews.filter(
      (i) =>
        i.candidateName.toLowerCase().includes(searchStr) ||
        i.employerName.toLowerCase().includes(searchStr) ||
        i.jobPostTitle.toLowerCase().includes(searchStr)
    );
  }

  // Sort by date (most recent first)
  filteredInterviews.sort(
    (a, b) =>
      new Date(b.scheduledDate).getTime() - new Date(a.scheduledDate).getTime()
  );

  // Paginate
  const startIndex = (Number(page) - 1) * Number(pageSize);
  const paginatedInterviews = filteredInterviews.slice(
    startIndex,
    startIndex + Number(pageSize)
  );

  return {
    data: paginatedInterviews,
    total: filteredInterviews.length,
  };
};

// Get single interview
export const getMockInterview = (id: string) => {
  return mockInterviews.find((interview) => interview.id === id);
};
