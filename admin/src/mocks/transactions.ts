// mock/simpleTransactionData.ts
import dayjs from "dayjs";
import {
  Transaction,
  TransactionStats,
  PointPackage,
  TransactionTrend,
} from "@/types/transaction";

// Basic mock transactions
export const mockTransactions: Transaction[] = [
  {
    id: "TX000001",
    userId: "U1001",
    userName: "<PERSON><PERSON><PERSON>",
    timestamp: dayjs().subtract(1, "day").toISOString(),
    points: 500,
    amount: 5.0,
    paymentMethod: "credit_card",
    status: "completed",
  },
  {
    id: "TX000002",
    userId: "U1002",
    userName: "Tran Thi B",
    timestamp: dayjs().subtract(2, "day").toISOString(),
    points: 1000,
    amount: 9.99,
    paymentMethod: "bank_transfer",
    status: "completed",
  },
  {
    id: "TX000003",
    userId: "U1003",
    userName: "Le Van C",
    timestamp: dayjs().subtract(3, "day").toISOString(),
    points: 2000,
    amount: 19.99,
    paymentMethod: "e_wallet",
    status: "completed",
  },
  {
    id: "TX000004",
    userId: "U1004",
    userName: "Pham Thi D",
    timestamp: dayjs().subtract(4, "day").toISOString(),
    points: 5000,
    amount: 49.99,
    paymentMethod: "credit_card",
    status: "pending",
  },
  {
    id: "TX000005",
    userId: "U1005",
    userName: "Hoang Van E",
    timestamp: dayjs().subtract(5, "day").toISOString(),
    points: 1000,
    amount: 9.99,
    paymentMethod: "bank_transfer",
    status: "failed",
  },
];

// Simple transaction stats
export const mockTransactionStats: TransactionStats = {
  totalRevenue: 94.96,
  dailyRevenue: 5.0,
  weeklyRevenue: 94.96,
  monthlyRevenue: 94.96,
  transactionsByMethod: [
    { method: "Credit Card", count: 2 },
    { method: "Bank Transfer", count: 2 },
    { method: "E-Wallet", count: 1 },
  ],
};

// Simple transaction trends (last 7 days)
export const mockTransactionTrends: TransactionTrend[] = [
  { date: dayjs().subtract(6, "day").format("YYYY-MM-DD"), revenue: 10 },
  { date: dayjs().subtract(5, "day").format("YYYY-MM-DD"), revenue: 10 },
  { date: dayjs().subtract(4, "day").format("YYYY-MM-DD"), revenue: 50 },
  { date: dayjs().subtract(3, "day").format("YYYY-MM-DD"), revenue: 20 },
  { date: dayjs().subtract(2, "day").format("YYYY-MM-DD"), revenue: 10 },
  { date: dayjs().subtract(1, "day").format("YYYY-MM-DD"), revenue: 5 },
  { date: dayjs().format("YYYY-MM-DD"), revenue: 0 },
];

// Basic point packages
export const mockPointPackages: PointPackage[] = [
  {
    id: "pkg001",
    name: "Basic Package",
    points: 500,
    price: 5.0,
    description: "Starter package for new users",
    isPopular: false,
    createdAt: dayjs().subtract(30, "day").toISOString(),
  },
  {
    id: "pkg002",
    name: "Standard Package",
    points: 1000,
    price: 9.99,
    description: "Most popular package",
    isPopular: true,
    createdAt: dayjs().subtract(30, "day").toISOString(),
  },
  {
    id: "pkg003",
    name: "Premium Package",
    points: 5000,
    price: 49.99,
    description: "Best value package",
    isPopular: true,
    createdAt: dayjs().subtract(30, "day").toISOString(),
  },
];

// Simple mock service
export const transactionServiceMock = {
  getTransactions: async () => {
    return { data: mockTransactions, total: mockTransactions.length };
  },

  getTransactionStats: async () => {
    return mockTransactionStats;
  },

  getTransactionTrends: async () => {
    return mockTransactionTrends;
  },

  exportTransactions: async () => {
    const csvContent =
      "ID,User,Date,Points,Amount,PaymentMethod,Status\n" +
      mockTransactions
        .map(
          (t) =>
            `${t.id},${t.userName},${dayjs(t.timestamp).format("YYYY-MM-DD")},${
              t.points
            },${t.amount},${t.paymentMethod},${t.status}`
        )
        .join("\n");

    return new Blob([csvContent], { type: "text/csv" });
  },

  getPointPackages: async () => {
    return { data: mockPointPackages, total: mockPointPackages.length };
  },

  createPointPackage: async (data: Omit<PointPackage, "id" | "createdAt">) => {
    return {
      ...data,
      id: `pkg${Math.floor(1000 + Math.random() * 9000)}`,
      createdAt: dayjs().toISOString(),
    };
  },

  updatePointPackage: async (id: string, data: Partial<PointPackage>) => {
    const pkg = mockPointPackages.find((p) => p.id === id);
    return { ...pkg, ...data } as PointPackage;
  },

  deletePointPackage: async () => {
    // Mock delete operation
  },
};

// Use this to override the real service
export const setupMockService = (service: Record<string, unknown>) => {
  Object.keys(transactionServiceMock).forEach((key) => {
    service[key] =
      transactionServiceMock[key as keyof typeof transactionServiceMock];
  });
};
