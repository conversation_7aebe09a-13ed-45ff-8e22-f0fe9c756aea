import { Contract } from "@/types/contract";

export const mockContracts: Contract[] = [
  {
    id: "CON-001",
    title: "Web Development Contract",
    employerId: "EMP-001",
    employerName: "Acme Corporation",
    jobSeekerId: "JS-001",
    jobSeekerName: "<PERSON> Do<PERSON>",
    startDate: new Date("2023-11-01"),
    endDate: new Date("2024-04-30"),
    hourlyRate: 45.0,
    paymentFrequency: "biweekly",
    workingHoursPerWeek: 40,
    workDays: ["monday", "tuesday", "wednesday", "thursday", "friday"],
    contractType: "freelance",
    status: "active",
    createdAt: new Date("2023-10-15"),
    activatedAt: new Date("2023-11-01"),
    isFeePaid: true,
    timeEntries: [
      {
        id: "TE-001",
        contractId: "CON-001",
        date: new Date("2023-11-05"),
        hoursWorked: 8,
        description: "Frontend development",
        status: "approved",
        createdAt: new Date("2023-11-05"),
        updatedAt: new Date("2023-11-06"),
      },
      {
        id: "TE-002",
        contractId: "CON-001",
        date: new Date("2023-11-06"),
        hoursWorked: 7.5,
        description: "API integration",
        status: "approved",
        createdAt: new Date("2023-11-06"),
        updatedAt: new Date("2023-11-07"),
      },
    ],
    payments: [
      {
        id: "PAY-001",
        contractId: "CON-001",
        amount: 720.0,
        date: new Date("2023-11-15"),
        status: "completed",
        description: "Payment for first two weeks",
        paymentMethod: "bank_transfer",
        createdAt: new Date("2023-11-15"),
      },
    ],
    messages: [
      {
        id: "MSG-001",
        contractId: "CON-001",
        senderId: "EMP-001",
        senderName: "Acme Corporation",
        message: "Welcome to the project! Let's discuss the timeline.",
        createdAt: new Date("2023-11-01"),
        isRead: true,
      },
      {
        id: "MSG-002",
        contractId: "CON-001",
        senderId: "JS-001",
        senderName: "John Doe",
        message: "Thanks! I'll start working on the frontend components.",
        createdAt: new Date("2023-11-01"),
        isRead: true,
      },
    ],
    additionalTerms: {
      description:
        "Project includes 2 rounds of revisions for each deliverable.",
    },
  },
  {
    id: "CON-002",
    title: "Mobile App Development",
    employerId: "EMP-002",
    employerName: "TechStart Inc.",
    jobSeekerId: "JS-002",
    jobSeekerName: "Jane Smith",
    startDate: new Date("2023-12-01"),
    endDate: new Date("2024-05-31"),
    hourlyRate: 55.0,
    paymentFrequency: "monthly",
    workingHoursPerWeek: 30,
    workDays: ["monday", "wednesday", "friday"],
    contractType: "project",
    status: "offered",
    createdAt: new Date("2023-11-20"),
    isFeePaid: false,
    timeEntries: [],
    payments: [],
    messages: [
      {
        id: "MSG-003",
        contractId: "CON-002",
        senderId: "EMP-002",
        senderName: "TechStart Inc.",
        message:
          "We're excited to have you on board for our mobile app development project!",
        createdAt: new Date("2023-11-20"),
        isRead: true,
      },
    ],
    additionalTerms: {
      description:
        "Developer retains rights to generic code libraries created during the project.",
    },
  },
  {
    id: "CON-003",
    title: "Content Writing Services",
    employerId: "EMP-003",
    employerName: "Global Media Ltd.",
    jobSeekerId: "JS-003",
    jobSeekerName: "Alex Johnson",
    startDate: new Date("2023-10-01"),
    endDate: new Date("2023-12-31"),
    hourlyRate: 35.0,
    paymentFrequency: "weekly",
    workingHoursPerWeek: 20,
    workDays: ["tuesday", "thursday", "saturday"],
    contractType: "part-time",
    status: "completed",
    createdAt: new Date("2023-09-15"),
    activatedAt: new Date("2023-10-01"),
    terminatedAt: new Date("2023-12-31"),
    isFeePaid: true,
    timeEntries: [
      {
        id: "TE-003",
        contractId: "CON-003",
        date: new Date("2023-10-03"),
        hoursWorked: 6,
        description: "Blog article writing",
        status: "approved",
        createdAt: new Date("2023-10-03"),
        updatedAt: new Date("2023-10-04"),
      },
    ],
    payments: [
      {
        id: "PAY-002",
        contractId: "CON-003",
        amount: 210.0,
        date: new Date("2023-10-08"),
        status: "completed",
        description: "Payment for first week",
        paymentMethod: "paypal",
        createdAt: new Date("2023-10-08"),
      },
    ],
    messages: [],
    additionalTerms: {
      description: "All content must be original and pass plagiarism checks.",
    },
  },
  {
    id: "CON-004",
    title: "Marketing Consultant",
    employerId: "EMP-001",
    employerName: "Acme Corporation",
    jobSeekerId: "JS-004",
    jobSeekerName: "Sam Wilson",
    startDate: new Date("2023-09-15"),
    endDate: new Date("2024-03-15"),
    hourlyRate: 60.0,
    paymentFrequency: "biweekly",
    workingHoursPerWeek: 15,
    workDays: ["monday", "wednesday", "friday"],
    contractType: "part-time",
    status: "terminated",
    createdAt: new Date("2023-09-01"),
    activatedAt: new Date("2023-09-15"),
    terminatedAt: new Date("2023-11-30"),
    terminationReason: "Budget constraints",
    isFeePaid: true,
    timeEntries: [],
    payments: [],
    messages: [],
    additionalTerms: {
      description: "Consultant will provide monthly performance reports.",
    },
  },
  {
    id: "CON-005",
    title: "UX/UI Design Contract",
    employerId: "EMP-002",
    employerName: "TechStart Inc.",
    jobSeekerId: "JS-005",
    jobSeekerName: "Emily Chen",
    startDate: new Date("2024-01-15"),
    endDate: new Date("2024-07-15"),
    hourlyRate: 50.0,
    paymentFrequency: "monthly",
    workingHoursPerWeek: 25,
    workDays: ["tuesday", "wednesday", "thursday", "friday"],
    contractType: "freelance",
    status: "draft",
    createdAt: new Date("2023-12-10"),
    isFeePaid: false,
    timeEntries: [],
    payments: [],
    messages: [],
    additionalTerms: {
      description: "Designer will provide source files for all deliverables.",
    },
  },
];
