import { PermissionEnum } from "./constants/_permissionEnum";
import { AuthContextType } from "./contexts/AuthContext";
import _ from "lodash";

export default function access(
  initialState: { currentUser?: AuthContextType["user"] } | undefined
) {
  const { currentUser } = initialState ?? {};
  const finalAccess = currentUser?.permissions?.reduce((acc: any, current) => {
    acc[current] = true;
    return acc;
  }, {});

  if (!finalAccess) return {};

  finalAccess["viewAccountManagement"] =
    finalAccess[PermissionEnum.VIEW_ACCOUNT_MANAGEMENT];

  finalAccess["viewRole"] =
    finalAccess[PermissionEnum.ROLE_READ] ||
    finalAccess[PermissionEnum.ROLE_CREATE] ||
    finalAccess[PermissionEnum.ROLE_UPDATE] ||
    finalAccess[PermissionEnum.ROLE_DELETE] ||
    finalAccess[PermissionEnum.ROLE_ASSIGN_PERMISSION] ||
    finalAccess[PermissionEnum.ROLE_UNASSIGN_PERMISSION];

  finalAccess["viewUser"] =
    finalAccess[PermissionEnum.USER_READ] ||
    finalAccess[PermissionEnum.USER_CREATE] ||
    finalAccess[PermissionEnum.USER_UPDATE] ||
    finalAccess[PermissionEnum.USER_DELETE] ||
    finalAccess[PermissionEnum.USER_ASSIGN_ROLE] ||
    finalAccess[PermissionEnum.USER_REMOVE_ROLE];

  finalAccess["viewPermission"] = finalAccess[PermissionEnum.PERMISSION_READ];

  finalAccess["viewCompany"] =
    finalAccess[PermissionEnum.COMPANY_READ] ||
    finalAccess[PermissionEnum.COMPANY_CREATE] ||
    finalAccess[PermissionEnum.COMPANY_UPDATE] ||
    finalAccess[PermissionEnum.COMPANY_DELETE] ||
    finalAccess[PermissionEnum.COMPANY_INVITE_STAFF] ||
    finalAccess[PermissionEnum.COMPANY_REMOVE_STAFF] ||
    finalAccess[PermissionEnum.COMPANY_UPDATE_STAFF] ||
    finalAccess[PermissionEnum.COMPANY_VERIFY] ||
    finalAccess[PermissionEnum.COMPANY_CREATE_BRANCH] ||
    finalAccess[PermissionEnum.COMPANY_UPDATE_BRANCH] ||
    finalAccess[PermissionEnum.COMPANY_DELETE_BRANCH];

  finalAccess["viewPost"] =
    finalAccess[PermissionEnum.POST_READ] ||
    finalAccess[PermissionEnum.POST_CREATE] ||
    finalAccess[PermissionEnum.POST_UPDATE] ||
    finalAccess[PermissionEnum.POST_DELETE] ||
    finalAccess[PermissionEnum.POST_APPROVE] ||
    finalAccess[PermissionEnum.POST_REJECT];

  return finalAccess;
}
