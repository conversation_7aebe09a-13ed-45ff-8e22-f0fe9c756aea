"use client";
import ResumeTable from "@/components/features/users/job-seeker-resumes/ResumeTable";
import ResumeFormModal from "@/components/features/users/job-seeker-resumes/ResumeFormModal";
import {
  fetchAllResumes,
  saveResume,
  deleteResume,
} from "@/services/resumeService";
import { Card, Typography, message } from "antd";
import { useTranslations } from "next-intl";
import { useContext, useState } from "react";
import { useRouter } from "next/navigation";
import { AuthContext } from "@/contexts/AuthContext";
import { Resume } from "@/types/resume";
import { ExtendedUser } from "@/types/user";
import { PermissionEnum } from "@/constants/_permissionEnum";

const ResumeManagementPage = () => {
  const t = useTranslations("route");
  const tCommon = useTranslations("common");
  const router = useRouter();
  const authContext = useContext(AuthContext);
  const access = authContext?.access;

  if (!access?.viewUser) {
    return (
      <Card>
        <Typography.Text type="danger">
          {tCommon("status.no_permission")}
        </Typography.Text>
      </Card>
    );
  }

  // Fake userInfo for ResumeFormModal (id, name, email)
  const getUserInfo = (resume?: Resume): ExtendedUser => ({
    id: resume?.userId || "",
    ulid: "",
    token: "",
    refreshToken: "",
    name: "",
    phoneNumber: "",
    email: "",
    profilePicture: "",
    role: "",
    isEmailVerified: false,
    isPhoneVerified: false,
    createdAt: "",
    lastLogin: "",
  });

  const handleCreate = () => {
    router.push(`/job-seekers-management/resumes/new`);
  };

  const handleView = (resume: Resume) => {
    router.push(`/job-seekers-management/resumes/${resume.id}`);
  };

  const handleEdit = (resume: Resume) => {
    router.push(`/job-seekers-management/resumes/${resume.id}/edit`);
  };

  const handleDelete = async (resume: Resume) => {
    try {
      await deleteResume(String(resume.id));
      message.success("Deleted successfully");
    } catch (err) {
      message.error("Delete failed");
    }
  };

  return (
    <Card>
      <Typography.Title level={4}>{t("resumes")}</Typography.Title>
      <ResumeTable
        api={fetchAllResumes}
        onCreate={
          access?.[PermissionEnum.RESUME_CREATE] ? handleCreate : undefined
        }
        onEdit={access?.[PermissionEnum.RESUME_UPDATE] ? handleEdit : undefined}
        onDelete={
          access?.[PermissionEnum.RESUME_DELETE] ? handleDelete : undefined
        }
        access={access}
        onView={handleView}
      />
    </Card>
  );
};

export default ResumeManagementPage;
