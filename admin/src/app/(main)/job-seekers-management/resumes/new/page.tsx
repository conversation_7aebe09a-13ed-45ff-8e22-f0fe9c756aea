"use client";
import { useRouter } from "next/navigation";
import { useState, useContext } from "react";
import {
  Card,
  Form,
  Button,
  message,
  Spin,
  Row,
  Col,
  Typography,
  Space,
  Input,
  Switch,
  DatePicker,
  Select,
  Checkbox,
  InputNumber,
} from "antd";
import {
  SaveOutlined,
  PlusOutlined,
  DeleteOutlined,
  UserOutlined,
  PhoneOutlined,
  MailOutlined,
  EnvironmentOutlined,
  LinkedinOutlined,
  GlobalOutlined,
  BankOutlined,
  BookOutlined,
  TrophyOutlined,
  ClockCircleOutlined,
} from "@ant-design/icons";
import { AuthContext } from "@/contexts/AuthContext";
import { Resume } from "@/types/resume";
import { createResume } from "@/services/resumeService";
import dayjs from "dayjs";
import DebounceSelect from "@/components/ui/selects/DebounceSelect";
import UserService from "@/services/userService";
import { UserRole } from "@/constants/userRole";

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

const languageLevels = [
  { value: "beginner", label: "Beginner" },
  { value: "intermediate", label: "Intermediate" },
  { value: "advanced", label: "Advanced" },
  { value: "fluent", label: "Fluent" },
  { value: "native", label: "Native" },
];

const availableDays = [
  "Monday",
  "Tuesday",
  "Wednesday",
  "Thursday",
  "Friday",
  "Saturday",
  "Sunday",
];

const timeSlots = ["Morning", "Afternoon", "Evening", "Night"];

const fetchUserOptions = async (search: string) => {
  const res = await UserService.getList({
    role: UserRole.JOB_SEEKER,
    search,
  });
  return { data: res.data };
};

const ResumeCreatePage = () => {
  const router = useRouter();
  const authContext = useContext(AuthContext);
  const access = authContext?.access;

  const [form] = Form.useForm();
  const [saving, setSaving] = useState(false);

  const handleSave = async () => {
    try {
      setSaving(true);
      const values = await form.validateFields();
      const formData = {
        ...values,
        userId: parseInt(values.userId, 10),
        skills:
          values.skills
            ?.split(",")
            .map((skill: string) => skill.trim())
            .filter(Boolean) || [],
        workExperiences: values.workExperiences?.map((exp: any) => ({
          ...exp,
          startDate: exp.startDate ? exp.startDate.format("YYYY-MM-DD") : null,
          endDate: exp.endDate ? exp.endDate.format("YYYY-MM-DD") : null,
        })),
        educations: values.educations?.map((edu: any) => ({
          ...edu,
          startDate: edu.startDate ? edu.startDate.format("YYYY-MM-DD") : null,
          endDate: edu.endDate ? edu.endDate.format("YYYY-MM-DD") : null,
        })),
      };
      const newResume = await createResume(formData);
      message.success("Resume created successfully!");
      router.push(`/job-seekers-management/resumes/${newResume.id}`);
    } catch (error) {
      message.error("Failed to create resume");
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    router.push("/job-seekers-management/resumes");
  };

  if (!access?.viewUser) {
    return (
      <Card>
        <Typography.Text type="danger">
          You do not have permission to create resumes.
        </Typography.Text>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <Row justify="space-between" align="middle">
          <Col>
            <Title level={3} style={{ margin: 0 }}>
              <UserOutlined /> Create New Resume
            </Title>
          </Col>
        </Row>
      </Card>
      <Form form={form} layout="vertical" requiredMark={false}>
        <Row gutter={[24, 0]}>
          {/* Left Column */}
          <Col xs={24} lg={12}>
            {/* Basic Information */}
            <Card
              title={
                <Title level={4} style={{ margin: 0 }}>
                  <UserOutlined /> Basic Information
                </Title>
              }
              style={{ marginBottom: 24 }}
            >
              <DebounceSelect
                name="userId"
                label="User"
                required
                fetchOptions={fetchUserOptions}
                placeholder="Search and select a user..."
                showSearch
                optionLabelProp="label"
                filterOption={false}
              />
              <Form.Item
                label="Resume Name"
                name="name"
                rules={[
                  { required: true, message: "Please enter resume name" },
                ]}
              >
                <Input placeholder="Enter resume name" size="large" />
              </Form.Item>

              <Form.Item label="Description" name="description">
                <TextArea
                  placeholder="Enter resume description"
                  rows={4}
                  showCount
                  maxLength={500}
                />
              </Form.Item>

              <Form.Item
                label="Skills"
                name="skills"
                rules={[{ required: true, message: "Please enter skills" }]}
              >
                <Input
                  placeholder="Enter skills separated by commas (e.g., React, Node.js, TypeScript)"
                  size="large"
                />
              </Form.Item>

              <Form.Item name="isActive" valuePropName="checked">
                <Space>
                  <Switch />
                  <Text>Set as Active Resume</Text>
                </Space>
              </Form.Item>
            </Card>

            {/* Contact Information */}
            <Card
              title={
                <Title level={4} style={{ margin: 0 }}>
                  <PhoneOutlined /> Contact Information
                </Title>
              }
              style={{ marginBottom: 24 }}
            >
              <Form.Item
                label="Phone Number"
                name={["contactInfo", "phoneNumber"]}
                rules={[
                  { required: true, message: "Please enter phone number" },
                ]}
              >
                <Input
                  prefix={<PhoneOutlined />}
                  placeholder="Enter phone number"
                  size="large"
                />
              </Form.Item>

              <Form.Item
                label="Email"
                name={["contactInfo", "email"]}
                rules={[
                  { required: true, message: "Please enter email" },
                  { type: "email", message: "Please enter valid email" },
                ]}
              >
                <Input
                  prefix={<MailOutlined />}
                  placeholder="Enter email address"
                  size="large"
                />
              </Form.Item>

              <Form.Item label="Address" name={["contactInfo", "address"]}>
                <TextArea placeholder="Enter address" rows={2} />
              </Form.Item>

              <Form.Item
                label="LinkedIn URL"
                name={["contactInfo", "linkedInUrl"]}
              >
                <Input
                  prefix={<LinkedinOutlined />}
                  placeholder="Enter LinkedIn profile URL"
                  size="large"
                />
              </Form.Item>

              <Form.Item
                label="Portfolio URL"
                name={["contactInfo", "portfolioUrl"]}
              >
                <Input
                  prefix={<GlobalOutlined />}
                  placeholder="Enter portfolio website URL"
                  size="large"
                />
              </Form.Item>
            </Card>

            {/* Languages */}
            <Card
              title={
                <Title level={4} style={{ margin: 0 }}>
                  <GlobalOutlined /> Languages
                </Title>
              }
              style={{ marginBottom: 24 }}
            >
              <Form.List name="languages">
                {(fields, { add, remove }) => (
                  <>
                    {fields.map(({ key, name, ...restField }) => (
                      <Card key={key} type="inner" style={{ marginBottom: 16 }}>
                        <Row gutter={16}>
                          <Col span={12}>
                            <Form.Item
                              {...restField}
                              name={[name, "language"]}
                              label="Language"
                              rules={[
                                {
                                  required: true,
                                  message: "Please enter language",
                                },
                              ]}
                            >
                              <Input placeholder="e.g., English" />
                            </Form.Item>
                          </Col>
                          <Col span={10}>
                            <Form.Item
                              {...restField}
                              name={[name, "level"]}
                              label="Level"
                              rules={[
                                {
                                  required: true,
                                  message: "Please select level",
                                },
                              ]}
                            >
                              <Select placeholder="Select level">
                                {languageLevels.map((level) => (
                                  <Option key={level.value} value={level.value}>
                                    {level.label}
                                  </Option>
                                ))}
                              </Select>
                            </Form.Item>
                          </Col>
                          <Col span={2}>
                            <Form.Item label=" ">
                              <Button
                                type="text"
                                danger
                                icon={<DeleteOutlined />}
                                onClick={() => remove(name)}
                              />
                            </Form.Item>
                          </Col>
                        </Row>
                      </Card>
                    ))}
                    <Button
                      type="dashed"
                      onClick={() => add()}
                      icon={<PlusOutlined />}
                      block
                    >
                      Add Language
                    </Button>
                  </>
                )}
              </Form.List>
            </Card>
          </Col>

          {/* Right Column */}
          <Col xs={24} lg={12}>
            {/* Work Experience */}
            <Card
              title={
                <Title level={4} style={{ margin: 0 }}>
                  <BankOutlined /> Work Experience
                </Title>
              }
              style={{ marginBottom: 24 }}
            >
              <Form.List name="workExperiences">
                {(fields, { add, remove }) => (
                  <>
                    {fields.map(({ key, name, ...restField }) => (
                      <Card key={key} type="inner" style={{ marginBottom: 16 }}>
                        <Row gutter={16}>
                          <Col span={12}>
                            <Form.Item
                              {...restField}
                              name={[name, "position"]}
                              label="Position"
                              rules={[
                                {
                                  required: true,
                                  message: "Please enter position",
                                },
                              ]}
                            >
                              <Input placeholder="e.g., Software Developer" />
                            </Form.Item>
                          </Col>
                          <Col span={12}>
                            <Form.Item
                              {...restField}
                              name={[name, "company"]}
                              label="Company"
                              rules={[
                                {
                                  required: true,
                                  message: "Please enter company",
                                },
                              ]}
                            >
                              <Input placeholder="e.g., Tech Company Inc." />
                            </Form.Item>
                          </Col>
                          <Col span={12}>
                            <Form.Item
                              {...restField}
                              name={[name, "startDate"]}
                              label="Start Date"
                              rules={[
                                {
                                  required: true,
                                  message: "Please select start date",
                                },
                              ]}
                            >
                              <DatePicker style={{ width: "100%" }} />
                            </Form.Item>
                          </Col>
                          <Col span={12}>
                            <Form.Item
                              {...restField}
                              name={[name, "endDate"]}
                              label="End Date"
                            >
                              <DatePicker
                                style={{ width: "100%" }}
                                placeholder="Current"
                              />
                            </Form.Item>
                          </Col>
                          <Col span={22}>
                            <Form.Item
                              {...restField}
                              name={[name, "description"]}
                              label="Description"
                            >
                              <TextArea
                                rows={3}
                                placeholder="Describe your responsibilities and achievements"
                              />
                            </Form.Item>
                          </Col>
                          <Col span={2}>
                            <Form.Item label=" ">
                              <Button
                                type="text"
                                danger
                                icon={<DeleteOutlined />}
                                onClick={() => remove(name)}
                              />
                            </Form.Item>
                          </Col>
                        </Row>
                      </Card>
                    ))}
                    <Button
                      type="dashed"
                      onClick={() => add()}
                      icon={<PlusOutlined />}
                      block
                    >
                      Add Work Experience
                    </Button>
                  </>
                )}
              </Form.List>
            </Card>

            {/* Education */}
            <Card
              title={
                <Title level={4} style={{ margin: 0 }}>
                  <BookOutlined /> Education
                </Title>
              }
              style={{ marginBottom: 24 }}
            >
              <Form.List name="educations">
                {(fields, { add, remove }) => (
                  <>
                    {fields.map(({ key, name, ...restField }) => (
                      <Card key={key} type="inner" style={{ marginBottom: 16 }}>
                        <Row gutter={16}>
                          <Col span={12}>
                            <Form.Item
                              {...restField}
                              name={[name, "degree"]}
                              label="Degree"
                              rules={[
                                {
                                  required: true,
                                  message: "Please enter degree",
                                },
                              ]}
                            >
                              <Input placeholder="e.g., Bachelor of Science" />
                            </Form.Item>
                          </Col>
                          <Col span={12}>
                            <Form.Item
                              {...restField}
                              name={[name, "institution"]}
                              label="Institution"
                              rules={[
                                {
                                  required: true,
                                  message: "Please enter institution",
                                },
                              ]}
                            >
                              <Input placeholder="e.g., University of Technology" />
                            </Form.Item>
                          </Col>
                          <Col span={12}>
                            <Form.Item
                              {...restField}
                              name={[name, "fieldOfStudy"]}
                              label="Field of Study"
                            >
                              <Input placeholder="e.g., Computer Science" />
                            </Form.Item>
                          </Col>
                          <Col span={12}>
                            <Form.Item
                              {...restField}
                              name={[name, "startDate"]}
                              label="Start Date"
                              rules={[
                                {
                                  required: true,
                                  message: "Please select start date",
                                },
                              ]}
                            >
                              <DatePicker style={{ width: "100%" }} />
                            </Form.Item>
                          </Col>
                          <Col span={12}>
                            <Form.Item
                              {...restField}
                              name={[name, "endDate"]}
                              label="End Date"
                            >
                              <DatePicker
                                style={{ width: "100%" }}
                                placeholder="Current"
                              />
                            </Form.Item>
                          </Col>
                          <Col span={10}>
                            <Form.Item
                              {...restField}
                              name={[name, "description"]}
                              label="Description"
                            >
                              <TextArea
                                rows={2}
                                placeholder="Additional details"
                              />
                            </Form.Item>
                          </Col>
                          <Col span={2}>
                            <Form.Item label=" ">
                              <Button
                                type="text"
                                danger
                                icon={<DeleteOutlined />}
                                onClick={() => remove(name)}
                              />
                            </Form.Item>
                          </Col>
                        </Row>
                      </Card>
                    ))}
                    <Button
                      type="dashed"
                      onClick={() => add()}
                      icon={<PlusOutlined />}
                      block
                    >
                      Add Education
                    </Button>
                  </>
                )}
              </Form.List>
            </Card>
          </Col>
        </Row>

        {/* Part-time Preferences (Full Width) */}
        <Card
          title={
            <Title level={4} style={{ margin: 0 }}>
              <ClockCircleOutlined /> Part-time Preferences
            </Title>
          }
          style={{ marginBottom: 24 }}
        >
          <Row gutter={[24, 16]}>
            <Col xs={24} sm={12} md={6}>
              <Form.Item
                label="Min Hourly Rate"
                name={["partTimePreference", "minHourlyRate"]}
              >
                <InputNumber
                  style={{ width: "100%" }}
                  placeholder="Min rate"
                  min={0}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item
                label="Max Hours Per Week"
                name={["partTimePreference", "maxHoursPerWeek"]}
              >
                <InputNumber
                  style={{ width: "100%" }}
                  placeholder="Max hours"
                  min={1}
                  max={168}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item
                label="Max Travel Distance"
                name={["partTimePreference", "maxTravelDistance"]}
              >
                <InputNumber
                  style={{ width: "100%" }}
                  placeholder="Distance (km)"
                  min={0}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item
                name={["partTimePreference", "remoteOnly"]}
                valuePropName="checked"
              >
                <Space>
                  <Switch />
                  <Text>Remote Only</Text>
                </Space>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                label="Available Days"
                name={["partTimePreference", "availableDays"]}
              >
                <Checkbox.Group>
                  <Row>
                    {availableDays.map((day) => (
                      <Col span={8} key={day}>
                        <Checkbox value={day}>{day}</Checkbox>
                      </Col>
                    ))}
                  </Row>
                </Checkbox.Group>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                label="Available Time Slots"
                name={["partTimePreference", "availableTimeSlots"]}
              >
                <Checkbox.Group>
                  <Row>
                    {timeSlots.map((slot) => (
                      <Col span={12} key={slot}>
                        <Checkbox value={slot}>{slot}</Checkbox>
                      </Col>
                    ))}
                  </Row>
                </Checkbox.Group>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                label="Preferred Job Types"
                name={["partTimePreference", "preferredJobTypes"]}
              >
                <Select
                  mode="tags"
                  placeholder="Enter job types"
                  style={{ width: "100%" }}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                label="Preferred Locations"
                name={["partTimePreference", "preferredLocations"]}
              >
                <Select
                  mode="tags"
                  placeholder="Enter preferred locations"
                  style={{ width: "100%" }}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                name={["partTimePreference", "isStudent"]}
                valuePropName="checked"
                noStyle
              >
                <Switch />
              </Form.Item>
              <Text style={{ marginLeft: 8 }}>Currently a Student</Text>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                label="Study Major"
                name={["partTimePreference", "studyMajor"]}
              >
                <Input placeholder="Enter study major" />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                label="Additional Notes"
                name={["partTimePreference", "additionalNotes"]}
              >
                <TextArea
                  rows={3}
                  placeholder="Any additional information or requirements"
                  showCount
                  maxLength={1000}
                />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* Save Button */}
        <Card>
          <Row justify="center">
            <Space size="large">
              <Button size="large" onClick={handleCancel}>
                Cancel
              </Button>
              <Button
                type="primary"
                size="large"
                icon={<SaveOutlined />}
                onClick={handleSave}
                loading={saving}
              >
                Create Resume
              </Button>
            </Space>
          </Row>
        </Card>
      </Form>
    </div>
  );
};

export default ResumeCreatePage;
