"use client";

import WalletTable from "@/components/features/wallet/WalletTable";
import BasePageTitle from "@/components/ui/common/BasePageTitle";
import WalletService from "@/services/walletService";
import { Card } from "antd";
import { useTranslations } from "next-intl";

export default function JobSeekerWalletPage() {
  const tWallet = useTranslations("wallet");
  return (
    <div>
      <Card>
        <WalletTable
          api={WalletService.getJobSeekerWallets}
          tTitle={tWallet("job_seeker_wallets")}
        />
      </Card>
    </div>
  );
}
