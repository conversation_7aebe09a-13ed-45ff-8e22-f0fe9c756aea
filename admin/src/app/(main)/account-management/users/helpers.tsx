import { Tag } from "antd";
import { ManOutlined, WomanOutlined } from "@ant-design/icons";
import { getRoleColor } from "@/constants/userRole";

export const getRoleTag = (role: string) => {
  return <Tag color={getRoleColor(role)}>{role}</Tag>;
};

export const getStatusTag = (status: boolean) => {
  const colorMap: Record<string, string> = {
    active: "green",
    locked: "red",
    pending: "orange",
  };

  return (
    <Tag color={colorMap[status ? "active" : "locked"] || "default"}>
      {status ? "Active" : "Inactivated"}
    </Tag>
  );
};

export const getTransactionTypeTag = (type: "deposit" | "usage") => {
  return type === "deposit" ? (
    <Tag color="green">DEPOSIT</Tag>
  ) : (
    <Tag color="blue">USAGE</Tag>
  );
};

export const getGenderIcon = (gender?: string) => {
  if (gender === "male") return <ManOutlined />;
  if (gender === "female") return <WomanOutlined />;
  return null;
};

export const getPostStatusTag = (status: string) => {
  const colorMap: Record<string, string> = {
    active: "green",
    expired: "orange",
    pending: "blue",
    rejected: "red",
  };

  return (
    <Tag color={colorMap[status] || "default"}>{status.toUpperCase()}</Tag>
  );
};

export const getApplicationStatusTag = (status: string) => {
  const colorMap: Record<string, string> = {
    applied: "blue",
    viewed: "purple",
    interviewed: "orange",
    accepted: "green",
    rejected: "red",
  };

  return (
    <Tag color={colorMap[status] || "default"}>{status.toUpperCase()}</Tag>
  );
};
