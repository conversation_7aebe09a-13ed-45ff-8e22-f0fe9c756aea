"use client";
import AddRoleCard from "@/components/features/roles/AddRoleCard";
import RoleCard from "@/components/features/roles/RoleCard";
import RoleFormModal from "@/components/features/roles/RoleFormModal";
import { PermissionEnum } from "@/constants/_permissionEnum";
import { AuthContext } from "@/contexts/AuthContext";
import RoleService from "@/services/roleService";
import { Role } from "@/types/role";
import { Col, notification, Row, Spin } from "antd";
import { useContext, useEffect, useState } from "react";

export default function RolePage() {
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(true);
  const [formModalVisible, setFormModalVisible] = useState(false);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const authContext = useContext(AuthContext);
  const access = authContext?.access;

  useEffect(() => {
    fetchRoles();
  }, []);

  const fetchRoles = async () => {
    setLoading(true);
    try {
      const response = await RoleService.getList({ page: 0, limit: 100 });
      setRoles(response.data);
    } catch (error) {
      console.log("🚀 ~ fetchRoles ~ error:", error);
      notification.error({
        message: "Error",
        description: "Failed to load roles.",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleEditRole = (role: Role) => {
    setSelectedRole(role);
    setFormModalVisible(true);
  };

  const handleDeleteRole = async (role: Role) => {
    try {
      await RoleService.delete(role.id);
      notification.success({
        message: "Success",
        description: `Role "${role.name}" deleted successfully.`,
      });
      fetchRoles(); // Refresh the list
    } catch (error) {
      console.log("🚀 ~ handleDeleteRole ~ error:", error);
      notification.error({
        message: "Error",
        description: "Failed to delete role. Please try again.",
      });
    }
  };

  return (
    <>
      {loading ? (
        <div className="flex justify-center items-center p-10">
          <Spin size="large" />
        </div>
      ) : (
        <div className="overflow-hidden">
          <Row gutter={[16, 16]}>
            {roles.length === 0
              ? null
              : roles.map((role) => (
                  <Col xs={24} sm={8} md={6} lg={6} xl={6} key={role.id}>
                    <RoleCard
                      role={role}
                      onEdit={
                        access &&
                        access[PermissionEnum.ROLE_UPDATE] &&
                        handleEditRole
                      }
                      onDelete={
                        access &&
                        access[PermissionEnum.ROLE_DELETE] &&
                        handleDeleteRole
                      }
                    />
                  </Col>
                ))}
            <Col span={6}>
              {access && access[PermissionEnum.ROLE_CREATE] && (
                <AddRoleCard onSuccess={fetchRoles} />
              )}
            </Col>
          </Row>
        </div>
      )}

      {/* Role Form Modal */}
      <RoleFormModal
        isVisible={formModalVisible}
        onClose={() => setFormModalVisible(false)}
        selectedRole={selectedRole}
        onSuccess={fetchRoles}
      />
    </>
  );
}
