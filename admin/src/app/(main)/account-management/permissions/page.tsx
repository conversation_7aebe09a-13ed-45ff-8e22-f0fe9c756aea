"use client";
import PermissionFormModal from "@/components/features/permissions/PermissionModal";
import BaseTable from "@/components/ui/tables/BaseTable";
import PermissionService from "@/services/permissionService";
import { Permission } from "@/types/permission";
import { formatTableDate } from "@/utils/date";
import type { TableColumnsType } from "antd";
import { Card } from "antd";
import { useTranslations } from "next-intl";
import { useRef, useState } from "react";

export default function PermissionsPage() {
  const t = useTranslations("permissions");
  const tCommon = useTranslations("common");
  const [selectedPermission, setSelectedPermission] =
    useState<Permission | null>(null);
  const [formModalVisible, setFormModalVisible] = useState(false);
  const tableRef = useRef<{ refetch: () => void }>(null);

  const columns: TableColumnsType<Permission> = [
    {
      title: tCommon("fields.name"),
      dataIndex: "name",
      key: "name",
    },
    {
      title: tCommon("fields.description"),
      dataIndex: "description",
      key: "description",
    },
    {
      title: tCommon("fields.created_at"),
      dataIndex: "createdAt",
      key: "createdAt",
      render: formatTableDate,
    },
    {
      title: tCommon("fields.updated_at"),
      dataIndex: "updatedAt",
      key: "updatedAt",
      render: formatTableDate,
    },
  ];

  const handleEditPermission = (permission: Permission) => {
    setSelectedPermission(permission);
    setFormModalVisible(true);
  };

  const filters = [
    {
      key: "module",
      label: "Module",
      type: "select" as const,
      options: [
        { value: "user", label: "User" },
        { value: "post", label: "Post" },
        { value: "transaction", label: "Transaction" },
        { value: "content", label: "Content" },
        { value: "system", label: "System" },
      ],
    },
    {
      key: "action",
      label: "Action",
      type: "select" as const,
      options: [
        { value: "read", label: "Read" },
        { value: "create", label: "Create" },
        { value: "update", label: "Update" },
        { value: "delete", label: "Delete" },
        { value: "manage", label: "Manage" },
        { value: "configure", label: "Configure" },
      ],
    },
  ];

  return (
    <Card>
      <BaseTable<Permission>
        api={PermissionService.getList}
        ref={tableRef}
        columns={columns}
        rowKey="id"
        title={t("title")}
        showSearch={true}
        searchPlaceholder={t("search_placeholder")}
        onRowClick={handleEditPermission}
        filters={filters}
        showActions={false}
        disabledRowCheckbox
      />

      <PermissionFormModal
        isVisible={formModalVisible}
        onClose={() => setFormModalVisible(false)}
        selectedPermission={selectedPermission}
        onSuccess={() => {}}
      />
    </Card>
  );
}
