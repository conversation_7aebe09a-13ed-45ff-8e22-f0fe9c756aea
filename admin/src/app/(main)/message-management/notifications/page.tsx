// app/(main)/notifications/page.tsx
"use client";

import NotificationDashboard from "@/components/features/notifications/NotificationDashboard";
import NotificationDetailModal from "@/components/features/notifications/NotificationDetailModal";
import NotificationListing from "@/components/features/notifications/NotificationListing";
import { Notification } from "@/types/notifications";
import {
  DashboardOutlined,
  SendOutlined,
  UnorderedListOutlined,
} from "@ant-design/icons";
import { Button, Card, Flex, Tabs, Typography } from "antd";
import { useRouter } from "next/navigation";
import React, { useState } from "react";

const NotificationManagement: React.FC = () => {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("dashboard");
  const [selectedNotification, setSelectedNotification] =
    useState<Notification | null>(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);

  const handleViewDetails = (notification: Notification) => {
    setSelectedNotification(notification);
    setDetailModalVisible(true);
  };

  const handleCloseDetails = () => {
    setDetailModalVisible(false);
    setSelectedNotification(null);
  };

  const handleCreateNew = () => {
    router.push("/message-management/notifications/sent-notification");
  };

  const tabItems = [
    {
      key: "dashboard",
      label: (
        <span>
          <DashboardOutlined className="!pr-2" />
          Dashboard
        </span>
      ),
      children: <NotificationDashboard />,
    },
    {
      key: "listing",
      label: (
        <span>
          <UnorderedListOutlined className="!pr-2" />
          All Notifications
        </span>
      ),
      children: (
        <NotificationListing
          onCreateNew={handleCreateNew}
          onViewDetails={handleViewDetails}
        />
      ),
    },
  ];

  return (
    <Card>
      <Flex justify="space-between" align="center" className="py-4">
        <Typography.Title level={4}>Notification Management</Typography.Title>
        <Button
          type="primary"
          icon={<SendOutlined />}
          onClick={handleCreateNew}
          size="large"
        >
          Send Notification
        </Button>
      </Flex>

      <Tabs activeKey={activeTab} onChange={setActiveTab} items={tabItems} />

      {/* Notification Detail Modal */}
      <NotificationDetailModal
        notification={selectedNotification}
        visible={detailModalVisible}
        onClose={handleCloseDetails}
        onUpdate={() => {
          // Trigger refetch if needed
          // This could be improved with a context or state management
        }}
      />
    </Card>
  );
};

export default NotificationManagement;
