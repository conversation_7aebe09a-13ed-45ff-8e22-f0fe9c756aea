// app/contracts/[id]/edit/page.tsx
"use client";

import ContractForm from "@/components/features/contracts/ContractForm";
import { UserRole } from "@/constants/userRole";
import ContractService from "@/services/contractService";
import { Contract } from "@/types/contract";
import { Spin, Typography, message } from "antd";
import { useParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";

const { Title } = Typography;

export default function EditContractPage() {
  const router = useRouter();
  const { id } = useParams();
  const [contract, setContract] = useState<Contract | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchContract = async () => {
      try {
        setLoading(true);
        const data = await ContractService.getContract(id as string);
        setContract(data as any);
      } catch (error) {
        console.error("Error fetching contract:", error);
        message.error("Failed to load contract");
        router.push("/contracts");
      } finally {
        setLoading(false);
      }
    };

    fetchContract();
  }, [id, router]);

  if (loading) {
    return (
      <div className="p-6 flex justify-center items-center min-h-[300px]">
        <Spin size="large" />
      </div>
    );
  }

  if (!contract) {
    return (
      <div className="p-6">
        <Title level={4}>Contract not found</Title>
      </div>
    );
  }

  return (
    <div className="p-6">
      <Title level={2} className="mb-6">
        Edit Contract
      </Title>
      <ContractForm contract={contract} userRole={UserRole.EMPLOYER} />
    </div>
  );
}
