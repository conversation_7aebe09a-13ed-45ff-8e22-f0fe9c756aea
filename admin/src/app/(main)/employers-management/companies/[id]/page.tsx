"use client";

import AddressDisplay from "@/components/features/address/AddressDisplay";
import BranchForm from "@/components/features/companies/branches/BranchForm";
import BranchList from "@/components/features/companies/branches/BranchList";
import CompanyForm from "@/components/features/companies/CompanyForm";
import JobPostingList from "@/components/features/companies/JobPostingList";
import TeamMemberForm from "@/components/features/companies/staffs/StaffForm";
import TeamMemberList from "@/components/features/companies/staffs/StaffList";
import BaseButton from "@/components/ui/buttons/BaseButton";
import BaseModal from "@/components/ui/modals/BaseModal";
import { PermissionEnum } from "@/constants/_permissionEnum";
import { AuthContext } from "@/contexts/AuthContext";
import CompanyService from "@/services/companyService";
import { Branch } from "@/types/branch";
import { Company } from "@/types/company";
import { TeamMember } from "@/types/teamMember";
import {
  BranchesOutlined,
  CheckCircleOutlined,
  DeleteOutlined,
  EditOutlined,
  ExclamationCircleOutlined,
  FileTextOutlined,
  TeamOutlined,
} from "@ant-design/icons";
import {
  Avatar,
  Card,
  Descriptions,
  Flex,
  Space,
  Spin,
  Tabs,
  Tag,
  Typography,
  message,
} from "antd";
import { useParams, useRouter } from "next/navigation";
import { useContext, useEffect, useState } from "react";

const { Title, Text } = Typography;

const CompanyDetailsPage = () => {
  const params = useParams();
  const router = useRouter();
  const companyId = params.id as string;

  const [loading, setLoading] = useState(true);
  const [company, setCompany] = useState<Company | null>(null);
  const [isCompanyModalOpen, setIsCompanyModalOpen] = useState(false);
  const [isBranchModalOpen, setIsBranchModalOpen] = useState(false);
  const [isTeamMemberModalOpen, setIsTeamMemberModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isVerifyModalOpen, setIsVerifyModalOpen] = useState(false);
  const [currentBranch, setCurrentBranch] = useState<Branch | null>(null);
  const [currentTeamMember, setCurrentTeamMember] = useState<TeamMember | null>(
    null
  );
  const [refreshKey, setRefreshKey] = useState(0);

  const authContext = useContext(AuthContext);
  const access = authContext?.access;

  useEffect(() => {
    fetchCompanyDetails();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [companyId, refreshKey]);

  const fetchCompanyDetails = async () => {
    setLoading(true);
    try {
      const res = await CompanyService.getDetail(companyId);
      setCompany(res.data);
    } catch (error) {
      console.error("Failed to fetch company details:", error);
      message.error("Failed to load company details");
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    setRefreshKey((prev) => prev + 1);
  };

  const handleEditCompany = () => {
    setIsCompanyModalOpen(true);
  };

  const handleDeleteCompany = async () => {
    try {
      await CompanyService.delete(companyId);
      message.success("Company deleted successfully");
      router.push("/admin/companies");
    } catch (error) {
      console.error("Failed to delete company:", error);
      message.error("Failed to delete company");
    }
  };

  const handleVerifyCompany = async () => {
    try {
      await CompanyService.verifyCompany(companyId);
      message.success("Company verified successfully");
      handleRefresh();
    } catch (error) {
      console.error("Failed to verify company:", error);
      message.error("Failed to verify company");
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Spin size="large" />
      </div>
    );
  }

  if (!company) {
    return (
      <div className="text-center p-8">
        <Title level={3}>Company not found</Title>
        <BaseButton
          label="Back to Companies"
          onClick={() => router.push("/admin/companies")}
        />
      </div>
    );
  }

  // Define tab items
  const tabItems = [
    {
      key: "branches",
      label: (
        <span>
          <BranchesOutlined /> Branches
        </span>
      ),
      children: <BranchList companyId={companyId} />,
    },
    {
      key: "team-members",
      label: (
        <span>
          <TeamOutlined /> Team Members
        </span>
      ),
      children: <TeamMemberList companyId={companyId} />,
    },
    {
      key: "job-postings",
      label: (
        <span>
          <FileTextOutlined /> Job Postings
        </span>
      ),
      children: <JobPostingList />,
    },
  ];

  return (
    <div>
      <Card className="mb-6">
        <Flex justify="space-between" align="center">
          <Flex align="center" gap="middle">
            <Avatar
              src={company.logoUrl}
              alt={company.name}
              size={64}
              className="mr-4"
            >
              {company.name[0]}
            </Avatar>
            <div>
              <Title level={3} className="mb-0">
                {company.name}
              </Title>
              <Text type="secondary">{company.industry}</Text>
            </div>
            {company.isVerified ? (
              <Tag color="success" icon={<CheckCircleOutlined />}>
                Verified
              </Tag>
            ) : (
              <Tag color="warning">Pending Verification</Tag>
            )}
            {company.isIndividual && <Tag color="blue">Individual</Tag>}
          </Flex>
          <Space>
            {!company.isVerified &&
              access &&
              access[PermissionEnum.COMPANY_VERIFY] && (
                <BaseButton
                  label="Verify"
                  icon={<CheckCircleOutlined />}
                  type="primary"
                  onClick={() => setIsVerifyModalOpen(true)}
                />
              )}
            {access && access[PermissionEnum.COMPANY_UPDATE] && (
              <BaseButton
                label="Edit"
                icon={<EditOutlined />}
                onClick={handleEditCompany}
              />
            )}
            {access && access[PermissionEnum.COMPANY_DELETE] && (
              <BaseButton
                label="Delete"
                icon={<DeleteOutlined />}
                danger
                onClick={() => setIsDeleteModalOpen(true)}
              />
            )}
          </Space>
        </Flex>
      </Card>

      <Card className="mb-6">
        <Descriptions title="Company Details" bordered>
          <Descriptions.Item label="Address" span={3}>
            <AddressDisplay addressData={company.address} />
          </Descriptions.Item>
          <Descriptions.Item label="Contact Email">
            {company.email || "-"}
          </Descriptions.Item>
          <Descriptions.Item label="Phone Number">
            {company.phoneNumber || "-"}
          </Descriptions.Item>
          <Descriptions.Item label="Website">
            {company.websiteUrl || "-"}
          </Descriptions.Item>
          <Descriptions.Item label="Founded Year">
            {company.foundedYear || "-"}
          </Descriptions.Item>
          <Descriptions.Item label="Team Members">
            {company.totalEmployees}
          </Descriptions.Item>
          <Descriptions.Item label="Active Job Postings">
            {company.activeJobPostings}
          </Descriptions.Item>
          <Descriptions.Item label="Description" span={3}>
            {company.description}
          </Descriptions.Item>
        </Descriptions>
      </Card>

      <Card>
        <Tabs defaultActiveKey="branches" items={tabItems} />
      </Card>

      {/* Modals */}
      <BaseModal
        isVisible={isCompanyModalOpen}
        title="Edit Company"
        onClose={() => setIsCompanyModalOpen(false)}
        onSubmit={() => {}}
        footer={null}
        width={800}
      >
        <CompanyForm
          initialData={company}
          onSuccess={() => {
            setIsCompanyModalOpen(false);
            handleRefresh();
          }}
        />
      </BaseModal>

      <BaseModal
        isVisible={isBranchModalOpen}
        title={currentBranch ? "Edit Branch" : "Add Branch"}
        onClose={() => setIsBranchModalOpen(false)}
        onSubmit={() => {}}
        footer={null}
      >
        <BranchForm
          companyId={companyId}
          initialData={currentBranch}
          onSuccess={() => {
            setIsBranchModalOpen(false);
            setCurrentBranch(null);
            handleRefresh();
          }}
        />
      </BaseModal>

      <BaseModal
        isVisible={isTeamMemberModalOpen}
        title={currentTeamMember ? "Edit Team Member" : "Invite Team Member"}
        onClose={() => setIsTeamMemberModalOpen(false)}
        onSubmit={() => {}}
        footer={null}
      >
        <TeamMemberForm
          companyId={companyId}
          initialData={currentTeamMember}
          onSuccess={() => {
            setIsTeamMemberModalOpen(false);
            setCurrentTeamMember(null);
            handleRefresh();
          }}
        />
      </BaseModal>

      <BaseModal
        isVisible={isDeleteModalOpen}
        title="Confirm Delete"
        onClose={() => setIsDeleteModalOpen(false)}
        onSubmit={handleDeleteCompany}
        submitText="Delete"
        confirmLoading={false}
      >
        <div className="text-center">
          <ExclamationCircleOutlined className="text-5xl text-red-500 mb-4" />
          <p>Are you sure you want to delete this company?</p>
          <p>
            This action cannot be undone. All branches and team members will
            also be deleted.
          </p>
        </div>
      </BaseModal>

      <BaseModal
        isVisible={isVerifyModalOpen}
        title="Confirm Verification"
        onClose={() => setIsVerifyModalOpen(false)}
        onSubmit={async () => {
          await handleVerifyCompany();
          setIsVerifyModalOpen(false);
        }}
        submitText="Verify"
        confirmLoading={false}
      >
        <div className="text-center">
          <ExclamationCircleOutlined className="text-5xl text-blue-500 mb-4" />
          <p>Are you sure you want to verify this company?</p>
          <p>This action will mark the company as verified.</p>
        </div>
      </BaseModal>
    </div>
  );
};

export default CompanyDetailsPage;
