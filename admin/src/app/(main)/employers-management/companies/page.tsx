"use client";

import BaseButton from "@/components/ui/buttons/BaseButton";
import BasePageTitle from "@/components/ui/common/BasePageTitle";
import BaseTable from "@/components/ui/tables/BaseTable";
import CompanyModal from "@/components/features/companies/CompanyModal";
import { FilterConfig } from "@/components/ui/tables/TableFilter";
import CompanyService from "@/services/companyService";
import { Company } from "@/types/company";
import {
  CheckCircleOutlined,
  ExclamationCircleOutlined,
} from "@ant-design/icons";
import { Avatar, Card, Modal, Space, Tag, Typography, message } from "antd";
import { useRouter } from "next/navigation";
import { useContext, useRef, useState } from "react";
import { useTranslations } from "next-intl";
import { AuthContext } from "@/contexts/AuthContext";
import { PermissionEnum } from "@/constants/_permissionEnum";

const { confirm } = Modal;
const { Text } = Typography;

const CompanyPage = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentCompany, setCurrentCompany] = useState<Company | null>(null);
  const router = useRouter();
  const tableRef = useRef<{ refetch: () => void }>(null);

  const tCompanies = useTranslations("companies");
  const tCommon = useTranslations("common");

  const authContext = useContext(AuthContext);
  const access = authContext?.access;

  const filters: FilterConfig[] = [
    {
      key: "industry",
      label: tCommon("fields.industry"),
      type: "select",
      options: [
        { value: "technology", label: tCompanies("industries.technology") },
        { value: "healthcare", label: tCompanies("industries.healthcare") },
        { value: "education", label: tCompanies("industries.education") },
        { value: "finance", label: tCompanies("industries.finance") },
        { value: "retail", label: tCompanies("industries.retail") },
        {
          value: "manufacturing",
          label: tCompanies("industries.manufacturing"),
        },
        { value: "hospitality", label: tCompanies("industries.hospitality") },
        { value: "other", label: tCompanies("industries.other") },
      ],
    },
    {
      key: "isVerified",
      label: tCompanies("verification_status"),
      type: "select",
      options: [
        { value: "true", label: tCompanies("verified") },
        { value: "false", label: tCompanies("not_verified") },
      ],
    },
    {
      key: "isIndividual",
      label: tCompanies("company_type"),
      type: "select",
      options: [
        { value: "true", label: tCompanies("individual") },
        { value: "false", label: tCompanies("organization") },
      ],
    },
    // {
    //   key: "createdAt",
    //   label: tCommon("fields.created_at"),
    //   type: "dateRange",
    // },
  ];

  const handleModalSuccess = () => {
    setIsModalOpen(false);
    if (tableRef.current) {
      tableRef.current.refetch();
    }
  };

  const handleVerifyCompany = async (company: Company) => {
    confirm({
      title: tCompanies("verify_company_title", { name: company.name }),
      icon: <ExclamationCircleOutlined />,
      content: tCompanies("verify_company_message"),
      onOk: async () => {
        try {
          await CompanyService.verifyCompany(company.id);
          message.success(
            tCompanies("company_verified_successfully", { name: company.name })
          );
          handleModalSuccess();
        } catch (error) {
          message.error(tCompanies("failed_to_verify_company"));
          console.error(error);
        }
      },
      okButtonProps: {
        children: tCommon("actions.verify"),
      },
      cancelButtonProps: {
        children: tCommon("actions.cancel"),
      },
    });
  };

  const handleDeleteCompany = async (company: Company) => {
    try {
      await CompanyService.delete(company.id);
      message.success(
        tCompanies("company_deleted_successfully", { name: company.name })
      );
      return Promise.resolve();
    } catch (error) {
      message.error(tCompanies("failed_to_delete_company"));
      console.error(error);
      return Promise.reject(error);
    }
  };

  const viewCompanyDetails = (company: Company) => {
    router.push(`/employers-management/companies/${company.id}`);
  };

  const columns = [
    {
      title: tCommon("fields.id"),
      dataIndex: "id",
    },
    {
      title: tCompanies("company_name"),
      dataIndex: "name",
      key: "name",
      render: (_: unknown, record: Company) => (
        <Space>
          <Avatar src={record.logoUrl} alt={record.name} size="large">
            {record.name ? record.name[0] : "C"}
          </Avatar>
          <div>
            <Text strong>{record.name}</Text>
            <div>
              <Text type="secondary">{record.industry}</Text>
            </div>
          </div>
        </Space>
      ),
    },
    {
      title: tCommon("fields.contact"),
      dataIndex: "contact",
      key: "contact",
      render: (_: unknown, record: Company) => (
        <div>
          <div>{record.email || "-"}</div>
          <div>{record.phoneNumber || "-"}</div>
        </div>
      ),
    },
    {
      title: tCompanies("teams_jobs"),
      key: "metrics",
      render: (_: unknown, record: Company) => (
        <div>
          <div>
            {record.totalEmployees} {tCompanies("team_members")}
          </div>
          <div>
            {record.activeJobPostings} {tCompanies("active_job_postings")}
          </div>
        </div>
      ),
    },
    {
      title: tCommon("fields.address"),
      dataIndex: "address",
      key: "address",
      ellipsis: true,
    },
    {
      title: tCommon("fields.status"),
      key: "status",
      render: (_: unknown, record: Company) => (
        <Space>
          {record.isVerified ? (
            <Tag color="success" icon={<CheckCircleOutlined />}>
              {tCompanies("verified")}
            </Tag>
          ) : (
            <Tag color="warning">{tCompanies("not_verified")}</Tag>
          )}
          {record.isIndividual && (
            <Tag color="blue">{tCompanies("individual")}</Tag>
          )}
        </Space>
      ),
    },
  ];

  if (access && access[PermissionEnum.COMPANY_VERIFY]) {
    columns.push({
      title: tCommon("actions.verify"),
      key: "verify",
      render: (_: unknown, record: Company) => {
        if (record.isVerified) return <></>;
        return (
          <BaseButton
            type="primary"
            size="small"
            label={tCommon("actions.verify")}
            onClick={(e) => {
              e.stopPropagation();
              handleVerifyCompany(record);
            }}
          />
        );
      },
    });
  }

  const handleEditCompany = (company: Company | null) => {
    setCurrentCompany(company);
    setIsModalOpen(true);
  };

  const handleCreateCompany = () => handleEditCompany(null);

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setCurrentCompany(null);
  };

  return (
    <>
      <Card>
        <BasePageTitle title={tCompanies("company_management")} />

        <BaseTable
          ref={tableRef}
          api={CompanyService.getList}
          columns={columns}
          rowKey="id"
          title=""
          createBtnText={tCompanies("add_company")}
          showSearch={true}
          searchPlaceholder={tCompanies("search_companies_placeholder")}
          onCreate={
            access &&
            access[PermissionEnum.COMPANY_CREATE] &&
            handleCreateCompany
          }
          onEdit={
            access && access[PermissionEnum.COMPANY_UPDATE] && handleEditCompany
          }
          onDelete={
            access &&
            access[PermissionEnum.COMPANY_DELETE] &&
            handleDeleteCompany
          }
          onRowClick={viewCompanyDetails}
          filters={filters}
          showActions={true}
        />
      </Card>

      <CompanyModal
        isVisible={isModalOpen}
        onClose={handleCloseModal}
        onSuccess={handleModalSuccess}
        company={currentCompany}
      />
    </>
  );
};

export default CompanyPage;
