"use client";

import EmployerTable from "@/components/features/posts/tables/employer/EmployerTable";
import { Card, Typography } from "antd";
import { useTranslations } from "next-intl";
import { useContext } from "react";
import { AuthContext } from "@/contexts/AuthContext";

const EmployerPostsPage = () => {
  const t = useTranslations("posts");
  const tCommon = useTranslations("common");
  const authContext = useContext(AuthContext);
  const access = authContext?.access;

  if (!access?.viewPost) {
    return (
      <Card>
        <Typography.Text type="danger">
          {tCommon("status.no_permission")}
        </Typography.Text>
      </Card>
    );
  }

  return (
    <Card>
      <Typography.Title level={4}>
        {t("post_management") + " - " + tCommon("roles.employer")}
      </Typography.Title>
      <EmployerTable />
    </Card>
  );
};

export default EmployerPostsPage;
