"use client";
// app/admin/transactions/page.tsx
import PointPackageList from "@/components/features/transactions/PointPackageList";
import TransactionList from "@/components/features/transactions/TransactionList";
import TransactionService from "@/services/transactionService";
import { TransactionStats, TransactionTrend } from "@/types/transaction";
import { DownloadOutlined } from "@ant-design/icons";
import { Button, Card, Col, Row, Statistic, Tabs, Typography } from "antd";
import { useEffect, useState } from "react";
import {
  CartesianGrid,
  Cell,
  Legend,
  Line,
  LineChart,
  Pie,
  <PERSON>hart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";

const { Title } = Typography;

const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042"];

const TransactionDashboard = () => {
  const [stats, setStats] = useState<TransactionStats | null>(null);
  const [trends, setTrends] = useState<TransactionTrend[]>([]);
  const [period, setPeriod] = useState<"day" | "week" | "month" | "year">(
    "month"
  );
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const [statsData, trendsData] = await Promise.all([
          TransactionService.getTransactionStats(),
          TransactionService.getTransactionTrends(period),
        ]);
        setStats(statsData);
        setTrends(trendsData);
      } catch (error) {
        console.error("Error fetching transaction data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [period]);

  const handleExportTransactions = async () => {
    try {
      const blob = await TransactionService.exportTransactions();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute(
        "download",
        `transactions-${new Date().toISOString().split("T")[0]}.csv`
      );
      document.body.appendChild(link);
      link.click();
      link.remove();
    } catch (error) {
      console.error("Error exporting transactions:", error);
    }
  };

  const handlePeriodChange = (value: string) => {
    setPeriod(value as "day" | "week" | "month" | "year");
  };

  const tabItems = [
    {
      key: "transactions",
      label: "Transaction History",
      children: <TransactionList />,
    },
    {
      key: "point-packages",
      label: "Point Packages",
      children: <PointPackageList />,
    },
  ];

  return (
    <Card>
      <div className="flex justify-between items-center mb-6">
        <Title level={2}>Transaction Management</Title>
        <Button
          type="primary"
          icon={<DownloadOutlined />}
          onClick={handleExportTransactions}
        >
          Export Transactions
        </Button>
      </div>

      {/* Stats Cards */}
      <Row gutter={16} className="mb-6">
        <Col span={6}>
          <Card loading={loading}>
            <Statistic
              title="Total Revenue"
              value={stats?.totalRevenue || 0}
              precision={2}
              prefix="$"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card loading={loading}>
            <Statistic
              title="Daily Revenue"
              value={stats?.dailyRevenue || 0}
              precision={2}
              prefix="$"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card loading={loading}>
            <Statistic
              title="Weekly Revenue"
              value={stats?.weeklyRevenue || 0}
              precision={2}
              prefix="$"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card loading={loading}>
            <Statistic
              title="Monthly Revenue"
              value={stats?.monthlyRevenue || 0}
              precision={2}
              prefix="$"
            />
          </Card>
        </Col>
      </Row>

      {/* Charts */}
      <Row gutter={16} className="mb-6">
        <Col span={16}>
          <Card
            title="Revenue Trends"
            extra={
              <div className="flex items-center">
                <span className="mr-2">Period:</span>
                <select
                  value={period}
                  onChange={(e) => handlePeriodChange(e.target.value)}
                  className="border rounded-sm px-2 py-1"
                >
                  <option value="day">Daily</option>
                  <option value="week">Weekly</option>
                  <option value="month">Monthly</option>
                  <option value="year">Yearly</option>
                </select>
              </div>
            }
            loading={loading}
          >
            <div style={{ width: "100%", height: 300 }}>
              <ResponsiveContainer>
                <LineChart
                  data={trends}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip formatter={(value) => [`$${value}`, "Revenue"]} />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="revenue"
                    stroke="#8884d8"
                    activeDot={{ r: 8 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </Card>
        </Col>
        <Col span={8}>
          <Card title="Payment Methods" loading={loading}>
            <div style={{ width: "100%", height: 300 }}>
              <ResponsiveContainer>
                <PieChart>
                  <Pie
                    data={stats?.transactionsByMethod || []}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) =>
                      `${name}: ${(percent * 100).toFixed(0)}%`
                    }
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="count"
                    nameKey="method"
                  >
                    {stats?.transactionsByMethod.map((entry, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={COLORS[index % COLORS.length]}
                      />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value, name) => [value, name]} />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Transaction List & Point Packages */}
      <Tabs items={tabItems} defaultActiveKey="transactions" className="mb-6" />
    </Card>
  );
};

export default TransactionDashboard;
