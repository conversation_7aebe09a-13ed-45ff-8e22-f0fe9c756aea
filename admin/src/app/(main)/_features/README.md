# Legacy Features Folder

⚠️ **This folder is not accessible via URL routing**

## Purpose

This `_features` folder contains the original feature structure before the menu reorganization. It's kept for:

- **Reference purposes** - backup of original implementation
- **Code comparison** - when debugging or understanding legacy logic
- **Component extraction** - source for shared components if needed

## Current Structure (Not Routed)

```
_features/
├── posts/          # Original posts structure
├── contracts/      # Original contracts page
├── interviews/     # Original interviews page
├── companies/      # Original companies structure
├── transactions/   # Original transactions page
├── user-management/ # Original user management structure
└── wallet-management/ # Original wallet structure
```

## New Active Structure

All active routes now use the new structure:

- `/job-seekers-management/`
- `/employers-management/`
- `/transactions-management/`

## Notes

- Folder starts with `_` so Next.js ignores it in routing
- Can be safely removed when no longer needed
- Contains original components that may still be referenced by new structure
