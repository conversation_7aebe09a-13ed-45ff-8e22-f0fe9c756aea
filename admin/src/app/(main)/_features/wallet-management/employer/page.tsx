"use client";

import WalletTable from "@/components/features/wallet/WalletTable";
import WalletService from "@/services/walletService";
import { Card } from "antd";
import { useTranslations } from "next-intl";

export default function EmployerWalletPage() {
  const tWallet = useTranslations("wallet");
  return (
    <div>
      <Card>
        <WalletTable
          api={WalletService.getEmployerWallets}
          tTitle={tWallet("employer_wallets")}
        />
      </Card>
    </div>
  );
}
