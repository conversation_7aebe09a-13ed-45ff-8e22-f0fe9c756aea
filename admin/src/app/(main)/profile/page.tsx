"use client";

import { useEffect, useState } from "react";
import { <PERSON>, <PERSON><PERSON>, Spin, message, Avatar, Descriptions } from "antd";
import { useAuth } from "@/contexts/AuthContext";
import UserService from "@/services/userService";
import {
  LockOutlined,
  EditOutlined,
  MailOutlined,
  PhoneOutlined,
  UserOutlined,
} from "@ant-design/icons";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";
import { utils } from "@/utils";

const ProfilePage = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [userDetail, setUserDetail] = useState<any>(null);
  const router = useRouter();
  const tProfile = useTranslations("profile");

  useEffect(() => {
    const fetchUserDetail = async () => {
      try {
        // Lấy user từ localStorage
        const localUser =
          typeof window !== "undefined" ? localStorage.getItem("user") : null;
        if (!localUser) return;

        const parsedUser = JSON.parse(localUser);
        const userId = parsedUser.id;

        if (!userId) return;

        // Call API để lấy thông tin chi tiết user
        const response = await UserService.getDetail(userId);
        setUserDetail(response.data);
      } catch (error) {
        console.error("Error fetching user detail:", error);
        message.error(tProfile("load_error"));
      } finally {
        setLoading(false);
      }
    };

    fetchUserDetail();
  }, [tProfile]);

  if (loading)
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Spin size="large" />
      </div>
    );
  if (!userDetail)
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div>User not found</div>
      </div>
    );

  return (
    <div className="max-w-2xl mx-auto py-8">
      <Card
        title={tProfile("title")}
        className="mb-6"
        extra={
          <Button
            icon={<EditOutlined />}
            onClick={() => router.push("/profile/edit")}
          >
            {tProfile("edit")}
          </Button>
        }
      >
        <div className="flex items-center gap-6 mb-6">
          <Avatar
            size={96}
            src={userDetail.profilePicture || "/user/default-avatar.svg"}
            icon={<UserOutlined />}
          />
          <div>
            <div className="font-bold text-xl mb-1">
              {userDetail.name || "-"}
            </div>
            <div className="flex items-center gap-2 text-gray-500">
              <MailOutlined />
              {userDetail.email || "-"}
            </div>
            <div className="flex items-center gap-2 text-gray-500">
              <PhoneOutlined />
              {userDetail.phoneNumber || "-"}
            </div>
          </div>
        </div>
        <Descriptions column={1} bordered size="small">
          <Descriptions.Item label={tProfile("role")}>
            {userDetail.role
              ? tProfile(`role_${userDetail.role.toLowerCase()}`)
              : tProfile("na")}
          </Descriptions.Item>
          <Descriptions.Item label={tProfile("address")}>
            {utils.formatAddress(userDetail.address) || tProfile("na")}
          </Descriptions.Item>
          <Descriptions.Item label={tProfile("gender")}>
            {userDetail.gender
              ? tProfile(`gender_${userDetail.gender}`)
              : tProfile("na")}
          </Descriptions.Item>
          <Descriptions.Item label={tProfile("dob")}>
            {userDetail.dateOfBirth
              ? utils.formatBirthDate(userDetail.dateOfBirth)
              : tProfile("na")}
          </Descriptions.Item>
          <Descriptions.Item label={tProfile("created_at")}>
            {userDetail.createdAt
              ? utils.formatTableDate(userDetail.createdAt)
              : tProfile("na")}
          </Descriptions.Item>
        </Descriptions>
      </Card>
      <Card>
        <Button
          type="primary"
          icon={<LockOutlined />}
          onClick={() => router.push("/profile/change-password")}
        >
          {tProfile("change_password")}
        </Button>
      </Card>
    </div>
  );
};

export default ProfilePage;
