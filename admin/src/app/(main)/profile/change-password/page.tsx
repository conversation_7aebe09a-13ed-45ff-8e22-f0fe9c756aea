"use client";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { Form, Input, Button, Card, message } from "antd";
import { useTranslations } from "next-intl";
import AuthService from "@/services/authService";
import { useNotification } from "@/contexts/NotiContext";
import { ChangePasswordRequest } from "@/types/auth";

const ChangePasswordPage = () => {
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const router = useRouter();
  const tProfile = useTranslations("profile");
  const notification = useNotification();

  const onFinish = async (values: ChangePasswordRequest) => {
    setLoading(true);
    try {
      await AuthService.changePassword({
        currentPassword: values.currentPassword,
        newPassword: values.newPassword,
        confirmPassword: values.confirmPassword,
      });

      notification.notifySuccess(tProfile("change_success"));
      form.resetFields();
      router.push("/profile");
    } catch (error: any) {
      const errorMessage =
        error?.response?.data?.message || tProfile("change_error");
      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-xl mx-auto py-8">
      <Card title={tProfile("change_password_title")}>
        <Form form={form} layout="vertical" onFinish={onFinish}>
          <Form.Item
            name="currentPassword"
            label={tProfile("old_password")}
            rules={[{ required: true, message: tProfile("password_required") }]}
          >
            <Input.Password />
          </Form.Item>
          <Form.Item
            name="newPassword"
            label={tProfile("new_password")}
            rules={[
              { required: true, message: tProfile("password_required") },
              {
                pattern:
                  /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
                message: tProfile("password_format_error"),
              },
            ]}
          >
            <Input.Password />
          </Form.Item>
          <Form.Item
            name="confirmPassword"
            label={tProfile("confirm_password")}
            dependencies={["newPassword"]}
            rules={[
              { required: true, message: tProfile("password_required") },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue("newPassword") === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(
                    new Error(tProfile("password_mismatch"))
                  );
                },
              }),
            ]}
          >
            <Input.Password />
          </Form.Item>
          <Form.Item>
            <div className="flex justify-end gap-2">
              <Button onClick={() => router.push("/profile")}>
                {tProfile("cancel")}
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                {tProfile("save")}
              </Button>
            </div>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default ChangePasswordPage;
