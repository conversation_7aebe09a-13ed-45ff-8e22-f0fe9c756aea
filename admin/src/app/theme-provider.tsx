// app/theme-provider.tsx
"use client";

import React, { useEffect, useState } from "react";
import { ConfigProvider, theme as antdTheme } from "antd";
import { useTheme } from "@/contexts/ThemeContext";

// Define light theme colors
const lightTheme = {
  colorPrimary: "#1677ff",
  colorSuccess: "#52c41a",
  colorWarning: "#faad14",
  colorError: "#ff4d4f",
  colorInfo: "#1677ff",
  borderRadius: 6,
};

// Define dark theme colors
const darkTheme = {
  colorPrimary: "#177ddc",
  colorSuccess: "#49aa19",
  colorWarning: "#d89614",
  colorError: "#a61d24",
  colorInfo: "#177ddc",
  borderRadius: 6,
};

export const AntdThemeProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const { theme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // Prevent hydration mismatch by waiting for client-side mount
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return <>{children}</>;
  }

  return (
    <ConfigProvider
      theme={{
        token: theme === "dark" ? darkTheme : lightTheme,
        algorithm:
          theme === "dark"
            ? antdTheme.darkAlgorithm
            : antdTheme.defaultAlgorithm,
      }}
    >
      {children}
    </ConfigProvider>
  );
};
