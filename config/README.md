# Flexin Kubernetes Configurations

This repository contains Kubernetes configurations for all Flexin environments.

## Structure

- `environments/`: Environment-specific configurations
  - `dev/`: Development environment
  - `staging/`: Staging environment  
  - `prod/`: Production environment

## Guidelines

1. **All changes require PR review** - See CODEOWNERS
2. **Test changes in dev first** - Never directly modify staging/prod
3. **Use descriptive commit messages** - Include ticket numbers
4. **Validate YAML syntax** - Use the validation script before pushing

## Making Changes

1. Create a feature branch
2. Make your changes in the appropriate environment directory
3. Run `scripts/validate.sh` to validate configurations
4. Create a PR with detailed description
5. Wait for reviews and approvals
6. Merge after successful validation

## Validation

Run the validation script before pushing:
```bash
./scripts/validate.sh dev backend