#!/bin/bash

ENVIRONMENT=${1:-dev}
SERVICE=${2:-all}

echo "🔍 Validating configurations for environment: $ENVIRONMENT, service: $SERVICE"

if [ ! -d "environments/$ENVIRONMENT" ]; then
    echo "❌ Environment $ENVIRONMENT not found!"
    exit 1
fi

# Validate YAML syntax
echo "📋 Validating YAML syntax..."
if [ "$SERVICE" = "all" ]; then
    find "environments/$ENVIRONMENT" -name "*.yaml" -o -name "*.yml"
else
    find "environments/$ENVIRONMENT/$SERVICE" -name "*.yaml" -o -name "*.yml" 2>/dev/null
fi | while read file; do
    if [ -f "$file" ]; then
        echo "Checking $file..."
        python3 -c "import yaml; yaml.safe_load(open('$file'))" || {
            echo "❌ Invalid YAML syntax in: $file"
            exit 1
        }
    fi
done

# Validate Kubernetes manifests
echo "⚙️ Validating Kubernetes manifests..."
if [ "$SERVICE" = "all" ]; then
    find "environments/$ENVIRONMENT" -name "*.yaml" -o -name "*.yml"
else
    find "environments/$ENVIRONMENT/$SERVICE" -name "*.yaml" -o -name "*.yml" 2>/dev/null
fi | while read file; do
    if [ -f "$file" ]; then
        echo "Validating $file..."
        kubectl apply --dry-run=client -f "$file" || {
            echo "❌ Invalid Kubernetes manifest: $file"
            exit 1
        }
    fi
done

echo "✅ All validations passed!"