apiVersion: v1
kind: ConfigMap
metadata:
  name: landing-config
  namespace: frontend
data:
  next.config.js: |
    /** @type {import('next').NextConfig} */
    const nextConfig = {
      output: 'standalone',
      experimental: {
        outputFileTracingRoot: undefined,
      },
      env: {
        NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
        NEXT_PUBLIC_ENV: process.env.NEXT_PUBLIC_ENV,
      },
    }
    
    module.exports = nextConfig