apiVersion: v1
kind: ConfigMap
metadata:
  name: backend-app-config
  namespace: backend
data:
  application-dev.yml: |
    spring:
      profiles:
        active: dev
      datasource:
        url: ***************************************************************************************************
        username: flexin_app_user
        driver-class-name: org.postgresql.Driver
        hikari:
          maximum-pool-size: 10
          minimum-idle: 2
          connection-timeout: 30000
          idle-timeout: 600000
          max-lifetime: 1800000
      jpa:
        hibernate:
          ddl-auto: update
        show-sql: true
        properties:
          hibernate:
            dialect: org.hibernate.dialect.PostgreSQLDialect
            format_sql: true
      security:
        oauth2:
          client:
            registration:
              google:
                client-id: 1022889238527-7uj1m4esptuf1udtlkhc82csqot3aajd.apps.googleusercontent.com
                client-secret: 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
                scope:
                  - email
                  - https://www.googleapis.com/auth/gmail.send
              user:
                provider: keycloak
                client-id: normal-user
                authorization-grant-type: authorization_code
                redirect-uri: https://api.flexin.dev/oauth2/flexin
                scope:
                  - openid
              admin:
                provider: keycloak
                client-id: admin-user
                authorization-grant-type: authorization_code
                redirect-uri: https://api.flexin.dev/oauth2/flexin
                scope:
                  - openid
                  - profile
            provider:
              keycloak:
                issuer-uri: https://auth.flexin.dev/realms/flexin
          resource-server:
            jwt:
              issuer-uri: https://auth.flexin.dev/realms/flexin
              jwk-set-uri: ${spring.security.oauth2.client.provider.keycloak.issuer-uri}/protocol/openid-connect/certs

    # Keycloak Configuration
    keycloak:
      auth-server-url: https://auth.flexin.dev
      realm: flexin
      user:
        client-id: flexin-user
        client-secret: D855N9kfSU0TuVEG9BUTW9XhjVZS0NIu
      admin:
        client-id: flexin-admin
        client-secret: bP5PkQ39OBWaSE90i5fcoYemBV76OK5R
      token-uri: ${keycloak.auth-server-url}/realms/${keycloak.realm}/protocol/openid-connect/token
      default-role-name: default-roles-flexin

    # JWT Configuration
    jwt:
      temp-token:
        secret: "your-256-bit-secret-key-for-temporary-token-generation"

    google:
      storage:
        bucket-name: tung-flexin-storage
        credential-url: google-cloud-key/ancient-truth-454914-r1-7c2ebd9e4ebc.json
      firebase:
        credential-url: google-cloud-key/flexin-test-firebase-adminsdk-fbsvc-57b48ab4c5.json

    # Logging configuration for dev environment
    logging:
      level:
        com.flexin: DEBUG
        org.springframework.security: DEBUG
        org.springframework.web: INFO
      pattern:
        console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
